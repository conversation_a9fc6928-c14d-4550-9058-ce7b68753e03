DROP TABLE IF EXISTS "public"."sag_message_send_log";
CREATE TABLE "public"."sag_message_send_log" (
                                                 "id" varchar(36)  NOT NULL,
                                                 "project_id" varchar(36) DEFAULT NULL,
                                                 "person_id" varchar(36) DEFAULT NULL,
                                                 "investigator_id" varchar(36) DEFAULT NULL,
                                                 "recipient" varchar(100) DEFAULT NULL,
                                                 "language" varchar(50) DEFAULT NULL,
                                                 "message_purpose" varchar(50) DEFAULT NULL,
                                                 "message_type" varchar(50) DEFAULT NULL,
                                                 "answer_code_type" varchar(50) DEFAULT NULL,
                                                 "subject" varchar(255) DEFAULT NULL,
                                                 "content" text DEFAULT NULL,
                                                 "file_ids" json DEFAULT NULL,
                                                 "ext_data" json DEFAULT NULL,
                                                 "status" varchar(50) DEFAULT NULL,
                                                 "sent_time" timestamp(3) DEFAULT NULL,
                                                 "remark" text DEFAULT NULL,
                                                 "create_by"  VARCHAR(36),
                                                 "create_time"  TIMESTAMP(3) DEFAULT NOW(),
                                                 "update_time"  TIMESTAMP(3) DEFAULT NOW(),
                                                 "last_update_by" VARCHAR(36),
                                                 "is_deleted" BOOL DEFAULT false,
                                                 "app_id" VARCHAR(36),
                                                 CONSTRAINT "___pk___sag_message_send_log___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."sag_message_send_log"."id" IS '主键';
COMMENT ON COLUMN "public"."sag_message_send_log"."project_id" IS '活动Id';
COMMENT ON COLUMN "public"."sag_message_send_log"."message_purpose" IS '发送类型';
COMMENT ON COLUMN "public"."sag_message_send_log"."message_type" IS '发送方式';
COMMENT ON COLUMN "public"."sag_message_send_log"."person_id" IS '人员Id';
COMMENT ON COLUMN "public"."sag_message_send_log"."investigator_id" IS '被调查人Id';
COMMENT ON COLUMN "public"."sag_message_send_log"."subject" IS '标题';
COMMENT ON COLUMN "public"."sag_message_send_log"."content" IS '内容';
COMMENT ON COLUMN "public"."sag_message_send_log"."status" IS '状态';
COMMENT ON COLUMN "public"."sag_message_send_log"."recipient" IS '接收者的手机号码/邮箱/企微号/钉钉号';
COMMENT ON COLUMN "public"."sag_message_send_log"."sent_time" IS '发送时间';
COMMENT ON COLUMN "public"."sag_message_send_log"."file_ids" IS '文件';
COMMENT ON COLUMN "public"."sag_message_send_log"."language" IS '语言类型';
COMMENT ON COLUMN "public"."sag_message_send_log"."ext_data" IS '附加数据';
COMMENT ON COLUMN "public"."sag_message_send_log"."answer_code_type" IS '邀请类型';
COMMENT ON COLUMN "public"."sag_message_send_log"."remark" IS '备注';
COMMENT ON TABLE  "public"."sag_message_send_log" IS '消息发送记录';

CREATE INDEX ___idx_sag_message_send_log_project_id___ ON public.sag_message_send_log(project_id);
CREATE INDEX ___idx_sag_message_send_log_person_id___ ON public.sag_message_send_log(person_id);
CREATE INDEX ___idx_sag_message_send_log_investigator_id___ ON public.sag_message_send_log(investigator_id);
CREATE INDEX ___idx_sag_message_send_log_recipient___ ON public.sag_message_send_log(recipient);
