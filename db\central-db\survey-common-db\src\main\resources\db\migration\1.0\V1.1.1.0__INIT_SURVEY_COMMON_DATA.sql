/*新增一个新的应答风格模块*/
INSERT INTO "survey_standard_sag_report_module"("key", "parent_name", "name", "edit")
VALUES ('newResponseStyle', '核心内容', '应答风格', 'NO'),
('trainingSuggestions', '核心内容', '培养建议', 'YES'),
('decisionMakingStyle', '核心内容', '决策风格', 'YES'),
('leadershipStyle', '核心内容', '领导风格', 'YES');

update "survey_standard_sag_report_module" set "name" = '社会称许性'  where "key"='responseStyle';

/*插入MCA报告初始数据*/
INSERT INTO "survey_standard_sag_report_template" ("id","name","code","type","standard_report_id","status","tenant_id","create_by","create_time","update_time","last_update_by","is_deleted","standard_report_type","sample_url","app_id","report_style","config_type")
 VALUES ('1279354877043977769', '{"zh_CN":"“领航” 测评(MCA)"}', '023', 'STANDARD', null, 'ON', null, null, null , null , null , 'f', 'MCA', null, null,'MCA_STANDARD',null);


INSERT INTO "survey_standard_sag_report_template_config" ( "id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id" )
VALUES
( '1280332187906785556', '1279354877043977769', 'PAYMENT',
'{"paymentConfig":{"frontCover":0,"appendix":0,"preface":0,"overview":0,"adaptationDetails":0,"detailedScore":0,"trainingSuggestions":0,"newResponseStyle":0,"responseStyle":0,"fit":0,"fitnessLevel":0,"traitDifference":0,"decisionMakingStyle":0,"leadershipStyle":0,"personalDevelopment":0,"interviewSuggestions":0,"total":0}}
', null, null, null, null, 'f', NULL );