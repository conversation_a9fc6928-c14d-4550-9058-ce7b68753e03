package com.knx.feign.file;

import com.alibaba.fastjson.JSONObject;
import com.knx.common.base.enums.BusinessTypeEnum;
import com.knx.common.base.service.ServiceNameConstants;
import com.knx.common.base.web.response.IDValue;
import com.knx.common.base.web.response.WebResponse;
import com.knx.common.security.FeignFilter;
import com.knx.survey.client.file.model.DownloadFile;
import com.knx.survey.client.file.model.FileLocation;
import com.knx.survey.client.file.model.FileLocationDTO;
import feign.Response;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * <AUTHOR>
 * @Package com.knx.feign.uas
 * @date 2020/7/14 15:34
 */

@FeignClient(contextId = "knxFileService", value = ServiceNameConstants.KNX_FILE_SERVICE, configuration = FeignFilter.class)
public interface RemoteFileService {


    /**
     * 使用方式  将文件流封装为 MultipartFile
     * FileInputStream in = new FileInputStream("D:\\test.txt");
     * MockMultipartFile multipartFile = new MockMultipartFile("file", in);
     * file名称字段必须和参数的@RequestPart("file") 名称一致
     * <p>
     * 引入jar包 <dependency>
     * <groupId>org.springframework</groupId>
     * <artifactId>spring-test</artifactId>
     * <version>5.1.9.RELEASE</version>
     * </dependency>
     */
    @RequestMapping(value = "/file/upload", produces = {MediaType.APPLICATION_JSON_UTF8_VALUE}, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Deprecated
    WebResponse<JSONObject> upload(@Valid @RequestParam("fileType") String fileType, @RequestParam("isPublic") Boolean isPublic, @RequestPart("file") MultipartFile file);


    @ApiOperation(value = "创建上传文件记录", notes = "创建上传文件记录")
    @PostMapping("/file/create")
    WebResponse<IDValue> create(@Valid @RequestBody FileLocationDTO fileLocationDTO);


    @GetMapping("/file/www/job/{fileId}")
    public DownloadFile wwwjob(@PathVariable("fileId") String fileId);

    @GetMapping("/file/get/{fileId}")
    public WebResponse<FileLocation> get(@PathVariable("fileId") String fileId);

    @PostMapping(value = "/file/uploadByBusinessType", produces = {MediaType.APPLICATION_JSON_UTF8_VALUE}, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Deprecated
    WebResponse<JSONObject> uploadByBusinessType(@Valid @RequestParam("fileType") String fileType,
                                                 @RequestParam("isPublic") Boolean isPublic,
                                                 @RequestParam("businessType") BusinessTypeEnum businessType,
                                                 @NotNull @RequestPart("file") MultipartFile multipartFile);


    @PostMapping(value = "/file/uploadWithBusinessType", produces = {MediaType.APPLICATION_JSON_UTF8_VALUE}, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    WebResponse uploadWithBusinessType(@Valid @RequestParam("effectiveFileTypes") List<String> effectiveFileTypes,
                                       @RequestParam("isPublic") Boolean isPublic,
                                       @RequestParam("businessType") BusinessTypeEnum businessType,
                                       @NotNull @RequestPart("file") MultipartFile multipartFile);

    @GetMapping(value = "/file/download/{fileId}")
    Response downloadFile(@PathVariable("fileId") String fileId);

    @GetMapping(value = "/file/www/{fileId}")
    Response wwwFile(@PathVariable("fileId") String fileId);
}
