DROP TABLE IF EXISTS survey_investigator_role_weight_mapping;
CREATE TABLE survey_investigator_role_weight_mapping
(
    "id"                              VARCHAR(36)  NOT NULL,
    "project_id"                      VARCHAR(36)  NOT NULL,
    "investigator_id"                 VARCHAR(36)  NOT NULL,
    "role_id"                         VARCHAR(36)  NOT NULL,
    "weights"                         numeric(53,2) NOT NULL,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___survey_investigator_role_weight_mapping___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."survey_investigator_role_weight_mapping" IS '调查者角色比重表';
COMMENT ON COLUMN "public"."survey_investigator_role_weight_mapping"."project_id" IS '活动ID';
COMMENT ON COLUMN "public"."survey_investigator_role_weight_mapping"."investigator_id" IS '调查者ID';
COMMENT ON COLUMN "public"."survey_investigator_role_weight_mapping"."role_id" IS '角色ID';
COMMENT ON COLUMN "public"."survey_investigator_role_weight_mapping"."weights" IS '角色比重';

CREATE INDEX ___idx_survey_investigator_role_weight_mapping_project_id___ ON public.survey_investigator_role_weight_mapping(project_id);
CREATE INDEX ___idx_survey_investigator_role_weight_mapping_investigator_id___ ON public.survey_investigator_role_weight_mapping(investigator_id);