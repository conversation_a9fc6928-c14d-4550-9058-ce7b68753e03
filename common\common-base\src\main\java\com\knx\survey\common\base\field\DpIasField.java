package com.knx.survey.common.base.field;

import com.knx.survey.model.I18NString;
import com.knx.survey.model.SagI18NString;
import org.apache.poi.ss.formula.functions.Count;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DpIasField.java
 * @Description 双视角定义
 * @createTime 2024年06月12日 11:34:00
 */
public class DpIasField {

    /**
     * 报告生成日期
     */
    public static I18NString REPORT_CREATE_TIME = new SagI18NString("报告生成日期", "");

    /**
     * 交叉分析群体
     */
    public static I18NString CROSS_ANALYSIS_GROUP = new SagI18NString("交叉分析群体", "Cross analysis of groups");

    /**
     * 标签类别
     */
    public static I18NString LABEL_CATEGORY = new SagI18NString("标签类别", "Demographic Groups");

    /**
     * 标签群体
     */
    public static I18NString LABEL_GROUP = new SagI18NString("标签群体", "Segment Groups");

    /**
     * 标签内容
     */
    public static I18NString LABEL_CONTENT = new SagI18NString("标签内容", "");

    /**
     * OCI指数名称
     */
    public static I18NString OCI_INDEX_NAME = new SagI18NString("组织能力健康度 OCI", "Organizational Capability Index OCI");

    /**
     * OCI指数名称
     */
    public static I18NString OCI_INDEX_SIMPLE_NAME = new SagI18NString("组织能力", "Organizational Capability");

    /**
     * OCI指数名称
     */
    public static I18NString OCI_STAR_INDEX_NAME = new SagI18NString(OCI_INDEX_NAME.getZh_CN()+" *", OCI_INDEX_NAME.getEn_US()+" *");

    /**
     * EEI指数名称
     */
    public static I18NString EEI_INDEX_NAME = new SagI18NString("敬业度指数 EEI", "Employee Engagement Index EEI");

    /**
     * EEI指数名称
     */
    public static I18NString EEI_INDEX_FULL_NAME = new SagI18NString("敬业度指数EEI", "");

    /**
     * EEI指数名称
     */
    public static I18NString EEI_INDEX_FULL_NAME2 = new SagI18NString("敬业度指数", "");
    /**
     * EEI指数名称
     */
    public static I18NString EEI_INDEX_SIMPLE_NAME = new SagI18NString("敬业度", "Employee Engagement");

    /**
     * EEI指数名称
     */
    public static I18NString EEI_INDEX_SIMPLE_NAME2 = new SagI18NString("敬业度EEI", "Employee Engagement EEI");

    /**
     * ESI指数名称
     */
    public static I18NString ESI_INDEX_NAME = new SagI18NString("满意度指数 ESI", "Employee Satisfaction Index ESI");

    /**
     * ESI指数名称
     */
    public static I18NString ESI_INDEX_FULL_NAME = new SagI18NString("满意度指数ESI", "");

    /**
     * ESI指数名称
     */
    public static I18NString ESI_INDEX_FULL_NAME2 = new SagI18NString("满意度指数", "");

    /**
     * ESI指数名称
     */
    public static I18NString ESI_INDEX_SIMPLE_NAME2 = new SagI18NString("满意度ESI", "");

    /**
     * 一级维度
     */
    public static I18NString FIRST_LEVEL_DIMENSION_NAME = new SagI18NString("一级维度", "Level-1 Dimension");

    /**
     * 二级维度
     */
    public static I18NString SECOND_LEVEL_DIMENSION_NAME = new SagI18NString("二级维度", "Level-2 Dimension");

    /**
     * 三级维度
     */
    public static I18NString THIRD_LEVEL_DIMENSION_NAME = new SagI18NString("三级维度", "Level-3 Dimension");

    /**
     * 题目
     */
    public static I18NString QUESTION_NAME = new SagI18NString("题目", "Item");

    /**
     * 填答时间
     */
    public static I18NString ANSWER_TIME = new SagI18NString("填答时间", "Response time");

    /**
     * 下一级部门
     */
    public static I18NString NEXT_ORG_NAME = new SagI18NString("下一级部门", "Lower-Level Organization");
    /**
     * 邀请人数
     */
    public static I18NString INVITE_NUMBER_NAME = new SagI18NString("邀请人数", "Survey Invitations");
    /**
     * 填答人数
     */
    public static I18NString FINISH_NUMBER_NAME = new SagI18NString("填答人数", "Total Survey Completed");
    /**
     * 有效填答人数
     */
    public static I18NString VALID_NUMBER_NAME = new SagI18NString("有效填答人数", "Valid Responses");
    /**
     * 有效填答率
     */
    public static I18NString VALID_RATE_NAME = new SagI18NString("有效填答率", "Valid respond rate");
    /**
     * 人
     */
    public static I18NString PERSON_NAME = new SagI18NString("人", "");

    /**
     * 占比
     */
    public static I18NString ANSWER_PERCENT = new SagI18NString("占比", "Rate");

    /**
     * 常模标准职能
     */
    public static I18NString STANDARD_DEM_JOB_FUNCTION = new SagI18NString("常模标准职能", "Benchmark Job Functions");

    /**
     * 常模标准职能
     */
    public static I18NString STANDARD_DEM_JOB_LEVEL = new SagI18NString("常模标准职级", "Benchmark Job Levels");

    /**
     * 常模标准职能
     */
    public static I18NString ANALYZING_SUBJECTS__COUNTERPARTS_JOB_FUNCTION = new SagI18NString("分析主体-对应群体", "Internal Job Functions");
    /**
     * 常模标准职级
     */
    public static I18NString ANALYZING_SUBJECTS__COUNTERPARTS_JOB_LEVEL = new SagI18NString("分析主体-对应群体", "Internal Job Grades");

    /**
     * 外部常模
     */
    public static I18NString NORM_NAME = new SagI18NString("外部常模", "External Norm");

    /**
     * 外部常模
     */
    public static I18NString YEAR_NAME = new SagI18NString("所属年份", "Year");

    /**
     * 企业数
     */
    public static I18NString ENTERPRISE_COUNT_NAME = new SagI18NString("企业数", "Enterprise Number");

    /**
     * 总参调人次
     */
    public static I18NString PERSON_COUNT_NAME = new SagI18NString("总参调人次（万）", "Participant Number");

    /**
     * 参照值
     */
    public static I18NString REFERENCE_VALUE_NAME = new SagI18NString("参照值", "Reference Vale");


    /**
     * 外部主常模
     */
    public static I18NString MAIN_NORM_NAME = new SagI18NString("外部主常模", "Priority External Norm");

    /**
     * 内部常模
     */
    public static I18NString INNER_NORM_NAME = new SagI18NString("内部常模", "Internal Norm");
    
    /**
     * 所属部门
     */
    public static I18NString DEPARTMENT_NAME = new SagI18NString("所属部门", "Organization");

    /**
     * 人口标签名称
     */
    public static I18NString DEMOGRAPHIC_LABEL_NAME = new SagI18NString("人口标签", "Demographic Groups");

    /**
     * 内部主常模
     */
    public static I18NString MAIN_INNER_NORM_NAME = new SagI18NString("内部主常模", "Priority Internal Norm");

    /**
     * 整体结果概览
     */
    public static I18NString OVER_VIEW_RESULT_NAME = new SagI18NString("整体结果概览", "General views of data");

    /**
     * 指数与题目
     */
    public static I18NString INDEX_QUESTION_NAME = new SagI18NString("指数/题目", "Index / Item");

    /**
     * 空
     */
    public static I18NString EMPTY_NAME = new SagI18NString("", "");

    /**
     * 分析主体
     *
     */
    public static I18NString ANALYSIS_OBJECT = new SagI18NString("分析主体", "Target Group ");

    /**
     * 有效填答人数及有效填答率
     *
     */
    public static I18NString VALID_PERSON_NUM_AND_VALID_RATE = new SagI18NString("有效填答人数及有效填答率(%)", "Valid Number & Percentage (%)");

    /**
     * 赞成百分比
     */
    public static I18NString APPROVAL_ABBR_NAME = new SagI18NString("赞成百分比", "Favorable");

    /**
     * 中立百分比
     */
    public static I18NString NEUTRAL_ABBR_NAME = new SagI18NString("中立百分比", "Neutral");

    /**
     * 不赞成百分比
     */
    public static I18NString DISAPPROVAL_ABBR_NAME = new SagI18NString("不赞成百分比", "Un-favorable");

    /**
     * 不赞成百分比
     */
    public static I18NString IGNORE_ABBR_NAME = new SagI18NString("不选择百分比", "Unselect");

    /**
     * 名称
     */
    public static I18NString NAME_NAME = new SagI18NString("名称", "Name");

    /**
     * 内部常模
     */
    public static String INNER_NORM_TYPE = "INNER_NORM";

    /**
     * 外部常模
     */
    public static String NORM_TYPE = "NORM";

    /**
     * 历史
     */
    public static String HISTORY_TYPE = "HISTORY";

    /**
     * 赞成
     */
    public static I18NString APPROVAL_NAME = new SagI18NString("赞成", "Favorable");

    /**
     * 赞成度
     */
    public static I18NString APPROVAL_RATE_NAME = new SagI18NString("赞成度", "Favorable");

    /**
     * 中立
     */
    public static I18NString NEUTRAL_NAME = new SagI18NString("中立", "Neutral");

    /**
     * 不赞成
     */
    public static I18NString DISAPPROVAL_NAME = new SagI18NString("不赞成", "Un-favorable");

    /**
     * 不选择
     */
    public static I18NString IGNORE_NAME = new SagI18NString("不选择", "Unselect");

    /**
     * 维度
     *
     */
    public static I18NString DIMENSION_NAME = new SagI18NString("维度", "Level-2 Dimension");

    /**
     * 历史对比
     */
    public static I18NString VS_HISTORY_NAME = new SagI18NString("历史对比", "Historical comparison");

    /**
     * 内部对比
     */
    public static I18NString VS_INNER_NORM_NAME = new SagI18NString("内部对比", "Internal Comparison");

    /**
     * 常模对比
     */
    public static I18NString VS_NORM_NAME = new SagI18NString("常模对比", "Norm comparison");

    /**
     * 指数/维度/题目
     */
    public static I18NString INDEX_DIMENSION_QUESTION_NAME = new SagI18NString("指数/维度/题目", "Dimension/Item");

    /**
     * 维度/题目
     */
    public static I18NString DIMENSION_QUESTION_NAME = new SagI18NString("维度/题目", "Dimension/Item");

    /**
     * 因变量
     */
    public static I18NString DEPENDENT_VARIABLE_NAME = new SagI18NString("因变量", "Outcome Variable");

    /**
     * 驱动因素
     */
    public static I18NString DRIVING_FACTOR_NAME = new SagI18NString("驱动因素", " Predictor variable");

    /**
     * 群体
     */
    public static I18NString GROUP_NAME = new SagI18NString("群体", "");

    /**
     * 选项
     */
    public static I18NString OPTION_NAME = new SagI18NString("选项", "Option");

    /**
     * 数量
     */
    public static I18NString NUM_NAME = new SagI18NString("数量", "Frequency");

    /**
     * 主问题
     */
    public static I18NString CONDITION_QUESTION_NAME = new SagI18NString("主问题", "");

    /**
     * 追问规则
     */
    public static I18NString PIERCE_THROUG_RULE_OPTION_NAME = new SagI18NString("{{强烈不同意/不同意/略有异议}}", "");

    /**
     * 选择题
     */
    public static I18NString CHOICE_NAME = new SagI18NString("选择题", "Choice Questions");

    /**
     * 开饭题
     */
    public static I18NString ESSAY_QUESTION_NAME = new SagI18NString("开放题", "Open-Ended Questions");

    /**
     * 组织能力视角
     */
    public static I18NString OCI_VIEW_NAME = new SagI18NString("组织能力视角", "Organizational Capability");

    /**
     * 敬业度视角
     */
    public static I18NString EEI_VIEW_NAME = new SagI18NString("敬业度视角", "Employee Engagement");

    /**
     * 双视角关键驱动因素综合统计
     *
     */
    public static I18NString DP_KEY_DRIVER_FACTORS_STATISTICS_NAME = new SagI18NString("双视角\n" +
            "驱动因素\n" +
            "综合统计 *", "Comprehensive\n"+"analysis of key\n"+"drivers *");

    /**
     * 相对优劣势 *
     * VS{{主常模}}
     * 从高到低排序
     *
     */
    public static I18NString RELATIVE_ADVANTAGES_DISADVANTAGE_RANK_NAME = new SagI18NString(
            "相对优劣势 *\n" +
            "VS{{主常模}}\n" +
            "从高到低排序", "Relative\n"+"strengths / \n"+"weaknesses\n"+"(VS {{主常模}})");

    /**
     * 重点维度类别
     *
     */
    public static I18NString KEY_DIMENSION_CATEGORIES_NAME = new SagI18NString(
            "重点维度" +
                    "类别 *", "Priority dimensions *");

    /**
     * 回归系数
     *
     */
    public static I18NString REGRESSION_NAME = new SagI18NString("回归系数", "Regression coefficient");

    /**
     * 相关系数
     *
     */
    public static I18NString CORRELATION_NAME = new SagI18NString("相关系数", "Correlation\n"+"coefficient");

    /**
     * 排序
     *
     */
    public static I18NString RANK_NAME = new SagI18NString("排序", "Ranking");

    /**
     * 注意平衡
     *
     */
    public static I18NString PAY_ATTENTION_BALANCE_NAME = new SagI18NString("注意平衡", "Balance required");

    /**
     * 适度关注
     *
     */
    public static I18NString GIVE_MODERATE_ATTENTION_TO_NAME = new SagI18NString("适度关注", "Manage with measured attention");

    /**
     * 重点关注
     *
     */
    public static I18NString FOCUS_ON_KEY_AREAS_NAME = new SagI18NString("重点关注", "Critical priority");

    /**
     * 持续进步
     */
    public static I18NString CONTINUOUSLY_IMPROVE_NAME = new SagI18NString("持续进步", "Continuous improvement");

    /**
     * 综合统计
     */
    public static I18NString COMPREHENSIVE_STATISTICS_NAME = new SagI18NString("综合统计", "Comprehensive\n"+"analysis");

    /**
     * 内部视角
     */
    public static I18NString INTERNAL_PERSPECTIVE_NAME = new SagI18NString("内部视角", "Internal Perspective");

    /**
     * 内部视角
     */
    public static I18NString DISAPPROVAL_TOP_10_NAME = new SagI18NString("反对度*最高10题", "Top 10\n"+"Highest\n"+"Unfavorable\n"+"%");

    /**
     * 外部视角
     */
    public static I18NString EXTERNAL_PERSPECTIVE_NAME = new SagI18NString("外部视角", "External");

    /**
     * 最高10题
     */
    public static I18NString TOP_10_QUESTION = new SagI18NString("最高10题", "Top 10 Items\n");

    /**
     * 公司整体
     */
    public static I18NString COMPANY_NAME = new SagI18NString("公司整体", "");

    /**
     * 关键驱动因素
     *
     */
    public static I18NString KEY_DRIVER_FACTORS_NAME = new SagI18NString("关键驱动因素" , "");

    /**
     * 区隔度*
     */
    public static I18NString PARTITION_STAR_NAME = new SagI18NString("区隔度*" , "");

    /**
     * 最查10题
     */
    public static I18NString LAST_10_QUESTION = new SagI18NString("最低10题", "Bottom 10 Items\n");

    /**
     * 下降群体
     */
    public static I18NString DESCENDING_GROUP = new SagI18NString("下降群体", "");

    /**
     * 下降群体
     */
    public static I18NString COLON = new SagI18NString("：", ":");

    /**
     * 矩阵说明
     */
    public static I18NString MATRIX_DESCRIPTION = new SagI18NString("矩阵说明", "Description");

    /**
     * 象限
     */
    public static I18NString QUADRANT_NAME = new SagI18NString("象限", "Zone");

    /**
     * 序号
     */
    public static I18NString SERIAL_NUMBER_NAME = new SagI18NString("序号", "No.");

    /**
     * 活力区
     */
    public static I18NString ENERGY_AREA_NAME = new SagI18NString("活力区", "High-Performance Zone");
    /**
     * 疲惫区
     */
    public static I18NString FATIGUE_AREA_NAME = new SagI18NString("疲惫区", "Burnout Zone");
    /**
     * 怠慢区
     */
    public static I18NString NEGLECT_AREA_NAME = new SagI18NString("怠慢区", "Complacency Zone");
    /**
     * 风险区
     */
    public static I18NString RISK_AREA_NAME = new SagI18NString("风险区", "At-Risk Zone");

    /**
     * 反对度最高且≥5%的3个群体
     */
    public static I18NString DEAGREE_GREAT_FIVE_THREE_GROUP = new SagI18NString("反对度最高且≥5%的3个群体", "Top 3 Groups by Unfavorable % (≥5%)");

    /**
     * 组织能力驱动因素
     */
    public static I18NString OCI_DRIVER_NAME = new SagI18NString("组织能力驱动因素", "Driving factors of Organizational Capability Index");

    /**
     * 组织能力驱动因素
     */
    public static I18NString EEI_DRIVER_NAME = new SagI18NString("敬业度驱动因素", "");

    /**
     * 人口标签名称
     */
    public static I18NString DEMOGRAPHIC_LABEL_FULL_NAME = new SagI18NString("人口标签名称", "");

    /**
     * 主历史
     */
    public static I18NString MAIN_HISTORY_NAME = new SagI18NString("主历史", "");

    /**
     * 题目均分
     */
    public static I18NString QUESTION_AVG_NAME = new SagI18NString("题目均分", "");

    /**
     * 题目均分维度均分
     */
    public static I18NString DIMENSION_AVG_NAME = new SagI18NString("维度均分", "");

    /**
     * 组织名称
     *
     */
    public static I18NString ORGANIZATION_NAME = new SagI18NString("组织", "Organization");

    /**
     * 题目1选项占比*
     *
     */
    public static I18NString QUESTION_1_OPTION_RATE = new SagI18NString("题目1选项占比*", "Item1:response distribution");

    /**
     * 题目2各选项得分均值*
     *
     */
    public static I18NString QUESTION_2_OPTION_AVG_SCORE = new SagI18NString("题目2各选项得分均值*", "Item 2: Average scores of each option*");

    /**
     * 题目2各选项得分均值*
     *
     */
    public static I18NString QUESTION_2_OPTION_AVG_SCORE_REPLACE = new SagI18NString("题目2{{1}}得分*", "Item2{{1}}Scores*");

    /**
     * 其他指数
     */
    public static I18NString OTHER_LABEL = new SagI18NString("其他指数", "");

    /**
     * 其他指数
     */
    public static I18NString MAIN_SUBJECT_EACH_DEM_EEI = new SagI18NString("分析主体各群体EEI", "");

    /**
     * 分析主体
     *
     */
    public static I18NString ANALYSIS_OBJECT_N = new SagI18NString("分析\n主体", "Target group");

    /**
     * 选择题
     */
    public static I18NString THROUGH_CONDITION_RULE = new SagI18NString("当员工选择“{{1}}”时，将进一步展开追问，员工可选择{{2}}项。", "When employees select \"{{1}}\", additional follow-up questions will be triggered. Employees may select up to {{2}} options.");

    /**
     * 选择题
     */
    public static I18NString THROUGH_CONDITION_RULE2 = new SagI18NString("当员工选择“{{1}}”时，将进一步展开追问。", "When employees select \"{{1}}\", additional follow-up questions will be triggered. ");

    /**
     * 自定义指数名称
     */
    public static I18NString CUSTOM_INDEX_NAME = new SagI18NString("自定义指数名称", "Custom Index Name");

    /**
     * 一级维度
     */
    public static I18NString FIRST_LEVEL_DIMENSION_NAME2 = new SagI18NString("一级\n维度", "Level-1\nDimension");

    /**
     * 部门
     */
    public static I18NString DEPARTMENT = new SagI18NString("部门", "");


}
