#spring:
#  datasource:
#    dynamic:
#      datasource:
#        master:
#          driver-class-name: org.postgresql.Driver
#          url: **********************************************
#          username: <PERSON><PERSON>(uwItgnnrUFlUg2OJnxjdzXcLVU3ztJPh)
#          password: ENC(Ea/osKJLMN1LaY+hWGvOru5Lxn/bbj0j)

spring:
  datasource:
    url: *******************************************
    username: postgres
    password: postgres
  kafka:
    bootstrap-servers: ***********:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
redis:
  host: ***********
  password: E<PERSON>(HomZdWjq4a2dnx75/KFS7A==)
  port: 6379

user:
  resetTokenUrlPrefix: https://sagittarius-stg.vxhcm.com/main/#/setting/update?

swagger:
  show: true
