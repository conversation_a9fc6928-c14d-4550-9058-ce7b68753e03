DROP TABLE IF EXISTS "public"."survey_report_schedule";
CREATE TABLE public.survey_report_schedule (
id 							varchar(36) 	NOT NULL,
report_type 				varchar(100) 	NOT NULL,
report_name 				varchar(100) 	NOT NULL,
name 						varchar(100) 	NOT NULL,
request_id 					varchar(36) 	NOT NULL,
request_ids                 json,
progress_bar 				int 	        NULL DEFAULT 0,
estimated_remaining_time 	varchar(36) 	NULL DEFAULT NULL,
start_time 					timestamp(3) 	NULL DEFAULT now(),
end_time 					timestamp(3) 	NULL DEFAULT NULL,
status 						varchar(100) 	NULL DEFAULT 'INIT',
remark 						text 	        NULL DEFAULT NULL,
create_by 					varchar(36)  	NULL,
create_time 				timestamp(3) 	NULL DEFAULT now(),
update_time 				timestamp(3) 	NULL DEFAULT now(),
last_update_by 				varchar(36) 	NULL,
is_deleted 					bool 			NULL DEFAULT false,
app_id 						varchar(36) 	NULL,
CONSTRAINT "___pk___survey_report_schedule___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."survey_report_schedule"."id" IS '主键';
COMMENT ON COLUMN "public"."survey_report_schedule"."report_type" IS '报告类型';
COMMENT ON COLUMN "public"."survey_report_schedule"."report_name" IS '报告名称';
COMMENT ON COLUMN "public"."survey_report_schedule"."name" IS '报告文件名称';
COMMENT ON COLUMN "public"."survey_report_schedule"."request_id" IS '报告自带UUID';
COMMENT ON COLUMN "public"."survey_report_schedule"."progress_bar" IS '进度条';
COMMENT ON COLUMN "public"."survey_report_schedule"."estimated_remaining_time" IS '预计剩余时间';
COMMENT ON COLUMN "public"."survey_report_schedule"."start_time" IS '开始时间';
COMMENT ON COLUMN "public"."survey_report_schedule"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."survey_report_schedule"."status" IS '状态';
COMMENT ON COLUMN "public"."survey_report_schedule"."remark" IS '备注';
COMMENT ON COLUMN "public"."survey_report_schedule"."request_ids" IS '报告自带UUID';