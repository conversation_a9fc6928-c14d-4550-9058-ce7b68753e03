DROP TABLE IF EXISTS prisma_history_data;
CREATE TABLE prisma_history_data
(
    "id"                              VARCHAR(36)  NOT NULL,
    "project_id"                      VARCHAR(36)  NOT NULL,
    "name"                            json  NOT NULL,
    "file_name"                       VARCHAR(50)  DEFAULT NULL,
    "status"                          VARCHAR(50)  NOT NULL,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___prisma_history_data___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."prisma_history_data" IS 'prisma历史对比数据表';
COMMENT ON COLUMN "public"."prisma_history_data"."name" IS '名称';
COMMENT ON COLUMN "public"."prisma_history_data"."project_id" IS '活动ID';
COMMENT ON COLUMN "public"."prisma_history_data"."file_name" IS '文件名称';
COMMENT ON COLUMN "public"."prisma_history_data"."status" IS '状态（启用/禁用）';

CREATE INDEX ___idx_prisma_history_data_project_id___ ON public.prisma_history_data(project_id);
CREATE INDEX ___idx_prisma_history_data_status___ ON public.prisma_history_data(status);


