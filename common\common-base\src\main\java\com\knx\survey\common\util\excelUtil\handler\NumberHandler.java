package com.knx.survey.common.util.excelUtil.handler;

import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2021/7/22 11:25
 */
public class NumberHandler extends DataValidationHandler {

    private Integer setCol = 4;

    public NumberHandler(Integer setCol) {
        this.setCol = setCol;
    }

    public NumberHandler() {
    }

    @Override
    public List<DataValidation> createDataValidation(DataValidationHelper helper, Map<Integer, String[]> mapDropDown, Sheet sheetHidden) {
        String hiddenName = sheetHidden.getSheetName();
        CellRangeAddressList addressList = new CellRangeAddressList(1, 10000, setCol, setCol);
        DataValidationConstraint explicitListConstraint = helper.createFormulaListConstraint(hiddenName + "!$B$1:$B$101");
        DataValidation dataValidation = helper.createValidation(explicitListConstraint, addressList);
        if (dataValidation instanceof XSSFDataValidation) {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }
//        sheet.addValidationData(dataValidation);
        return Collections.singletonList(dataValidation);
    }


    @Override
    public Sheet createSheet(WriteWorkbookHolder writeWorkbookHolder) {
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        Sheet sheet = workbook.getSheet("hidden");
        if (sheet != null) {
            return sheet;
        }
        Sheet hidden = workbook.createSheet("hidden");
        for (int i = 1; i < 101; i++) {
            Row row = hidden.createRow(i);
            Cell cell = row.createCell(1);
            cell.setCellValue(i);
        }
        int sheetIndex = workbook.getSheetIndex(hidden);
        workbook.setSheetHidden(sheetIndex, true);
        return hidden;
    }
}
