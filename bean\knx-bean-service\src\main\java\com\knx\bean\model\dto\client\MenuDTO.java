package com.knx.bean.model.dto.client;

import com.knx.common.base.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Package com.knx.uas.dao.model
 * @date 2020/6/4 15:20
 */
@ApiModel("菜单")
@Data
public class MenuDTO extends BaseModel {

    /**
     * 上级ID，一级菜单为0
     */
    @ApiModelProperty(value = "上级ID", example = "1324255348076441602")
    private String parentId;
    /**
     * 菜单URL
     */
    @ApiModelProperty(value = "菜单URL", example = "/center/system/email-set")
    private String url;
    /**
     * 类型   0：菜单   1：按钮
     */
    @ApiModelProperty(value = "类型", example = "0")
    private Integer type;
    /**
     * 菜单图标
     */
    @ApiModelProperty("菜单图标")
    private String icon;
    /**
     * 权限标识，如：sys:menu:save
     */
    @ApiModelProperty(value = "权限标识", example = "sys:menu:save")
    private String permissions;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序", example = "100")
    private Integer sort;
    /**
     * 系统id
     */
    @ApiModelProperty(value = "产品id", example = "1319287915221618690")
    private String productId;
    /**
     * 菜单名，用于显示
     */
    @ApiModelProperty(value = "菜单名", example = "邮箱设置")
    private String name;

}
