ALTER TABLE survey_package_question_mapping DROP CONSTRAINT ___pk___survey_package_question_mapping___;
ALTER TABLE survey_package_question_mapping ADD COLUMN id SERIAL PRIMARY KEY;
ALTER TABLE survey_package_question_mapping ALTER COLUMN id TYPE varchar(36) USING id::varchar;
ALTER TABLE survey_package_question_mapping ALTER COLUMN id DROP DEFAULT;
CREATE INDEX ___idx_survey_package_question_mapping_package_id_question_id___ ON survey_package_question_mapping(package_id, question_id);

ALTER TABLE survey_package_question_mapping ADD "create_by" varchar(36) NULL;
ALTER TABLE survey_package_question_mapping ADD "create_time" TIMESTAMP(3) DEFAULT NOW();
ALTER TABLE survey_package_question_mapping ADD "update_time" TIMESTAMP(3) DEFAULT NOW();
ALTER TABLE survey_package_question_mapping ADD "last_update_by" varchar(36) NULL;
ALTER TABLE survey_package_question_mapping ADD is_deleted bool NULL DEFAULT false;
ALTER TABLE survey_package_question_mapping ADD "app_id" varchar(36) NULL;