DROP TABLE IF EXISTS survey_lottery_icon;
CREATE TABLE survey_lottery_icon
(
    "id"                              VARCHAR(36)  NOT NULL,
    "lottery_id"                      VARCHAR(36)  NOT NULL,
    "icon_pic"                        VARCHAR(50) DEFAULT NULL,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___survey_lottery_icon___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."survey_lottery_icon" IS '调研抽奖图标表';
COMMENT ON COLUMN "public"."survey_lottery_icon"."lottery_id" IS '抽奖ID';
COMMENT ON COLUMN "public"."survey_lottery_icon"."icon_pic" IS '图标地址';

