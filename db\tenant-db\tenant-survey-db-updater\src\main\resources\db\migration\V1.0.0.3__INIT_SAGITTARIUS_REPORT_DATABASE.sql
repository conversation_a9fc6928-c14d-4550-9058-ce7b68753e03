DROP TABLE IF EXISTS "public"."report_template";
CREATE TABLE "public"."report_template" (
	"id"                        VARCHAR(36)        NOT NULL,
	"name"                      JSON ,
	"standard_report_type"      VARCHAR(50) ,
	"create_by"                 VARCHAR(36),
	"create_time"               TIMESTAMP(3),
	"update_time"               TIMESTAMP(3),
	"last_update_by"            VARCHAR(36),
	"is_deleted"                BOOL,
	"app_id"                    VARCHAR(36),
	CONSTRAINT  "___pk__report_template___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."report_template"."name" IS '报告名';
COMMENT ON COLUMN "public"."report_template"."standard_report_type" IS '报告类型（4种报告）';
COMMENT ON TABLE  "public"."report_template" IS '租户报告模板';



DROP TABLE IF EXISTS "public"."report_template_config";
CREATE TABLE "public"."report_template_config" (
	"id"                    VARCHAR(36)            NOT NULL,
	"report_template_id"    VARCHAR(36),
	"type"                  VARCHAR (50),
	"data"                  JSON,
	"create_by"             VARCHAR(36),
	"create_time"           TIMESTAMP(3),
	"update_time"           TIMESTAMP(3),
	"last_update_by"        VARCHAR(36),
	"is_deleted"            BOOL,
	"app_id"                VARCHAR(36),
	CONSTRAINT  "___pk__report_template_config___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."report_template_config"."report_template_id" IS '报告模板id';
COMMENT ON COLUMN "public"."report_template_config"."type" IS '报告模块类型';
COMMENT ON COLUMN "public"."report_template_config"."data" IS '编辑模块数据。key：模板名/模块名/付费，val：名字/数据/具体付费';
COMMENT ON TABLE  "public"."report_template_config" IS '租户报告模板';


DROP TABLE IF EXISTS "public"."sag_norm_transform";
CREATE TABLE "public"."sag_norm_transform" (
	"id" INT4       NOT NULL,
	"type"          VARCHAR(50),
	"project_id"    VARCHAR(36) ,
	"low_tenths"    NUMERIC(53,2),
	"high_tenths"   NUMERIC(53,2),
	CONSTRAINT  "___pk__sag_norm_transform___" PRIMARY KEY ("id","project_id","type")
);
COMMENT ON COLUMN "public"."sag_norm_transform"."project_id" IS '活动ID';
COMMENT ON COLUMN "public"."sag_norm_transform"."type" IS '报告类型';
COMMENT ON COLUMN "public"."sag_norm_transform"."low_tenths" IS '十分质最低分值';
COMMENT ON COLUMN "public"."sag_norm_transform"."high_tenths" IS '十分质最高分值';
COMMENT ON TABLE  "public"."sag_norm_transform" IS '租户常模转换表';



DROP TABLE IF EXISTS "public"."sag_report_dimension";
CREATE TABLE "public"."sag_report_dimension" (
	"id"                                VARCHAR(36)        NOT NULL,
	"project_id"                        VARCHAR(36) ,
	"name"                              JSON,
	"description"                       JSON,
	"close_left_description"            JSON,
	"close_right_description"           JSON,
	"code"                              VARCHAR (50),
	"close_left_describe_advantage"     JSON,
	"close_left_describe_disadvantage"  JSON,
	"close_right_describe_advantage"    JSON,
	"close_right_describe_disadvantage" JSON,
	"sort"                              INT4,
	"create_by"                         VARCHAR(36),
	"create_time"                       TIMESTAMP(3)     DEFAULT NOW(),
	"update_time"                       TIMESTAMP(3)     DEFAULT NOW(),
	"last_update_by"                    VARCHAR(36),
	"is_deleted"                        BOOL             DEFAULT FALSE,
	"app_id"                            VARCHAR(36),
	CONSTRAINT  "___pk__sag_report_dimension___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."sag_report_dimension"."project_id" IS '活动ID';
COMMENT ON COLUMN "public"."sag_report_dimension"."name" IS '维度名字';
COMMENT ON COLUMN "public"."sag_report_dimension"."description" IS '维度解释';
COMMENT ON COLUMN "public"."sag_report_dimension"."close_left_description" IS '分数靠左解释';
COMMENT ON COLUMN "public"."sag_report_dimension"."close_right_description" IS '分数靠右解释';
COMMENT ON COLUMN "public"."sag_report_dimension"."code" IS '维度唯一编码';
COMMENT ON COLUMN "public"."sag_report_dimension"."close_left_describe_advantage" IS '维度靠左优势描述';
COMMENT ON COLUMN "public"."sag_report_dimension"."close_left_describe_disadvantage" IS '维度靠左劣势描述';
COMMENT ON COLUMN "public"."sag_report_dimension"."close_right_describe_advantage" IS '维度靠右优势描述';
COMMENT ON COLUMN "public"."sag_report_dimension"."close_right_describe_disadvantage" IS '维度靠右劣势描述';
COMMENT ON COLUMN "public"."sag_report_dimension"."sort" IS '维度排序';
COMMENT ON TABLE  "public"."sag_report_dimension" IS '报告维度';


DROP TABLE IF EXISTS "public"."sag_report_score";
CREATE TABLE "public"."sag_report_score" (
	"id"                VARCHAR(36) NOT NULL,
	"project_id"        VARCHAR(36) ,
	"type"              VARCHAR (50) ,
	"code"              VARCHAR (50) ,
	"name"              JSON,
	"high_score"        NUMERIC(53,2),
	"low_score"         NUMERIC(53,2),
	"description"       JSON,
	"comment"           JSON,
	"advantage"         JSON,
	"development"       JSON,
	"create_by"         VARCHAR(36),
	"create_time"       TIMESTAMP(3),
	"update_time"       TIMESTAMP(3),
	"last_update_by"    VARCHAR(36),
	"is_deleted"        BOOL,
	"app_id"            VARCHAR(36),
	CONSTRAINT  "___pk__sag_report_score___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."sag_report_score"."project_id" IS '活动ID';
COMMENT ON COLUMN "public"."sag_report_score"."type" IS '标准报告类型';
COMMENT ON COLUMN "public"."sag_report_score"."code" IS '维度编码';
COMMENT ON COLUMN "public"."sag_report_score"."name" IS '维度名字';
COMMENT ON COLUMN "public"."sag_report_score"."high_score" IS '分段高分值';
COMMENT ON COLUMN "public"."sag_report_score"."low_score" IS '分段低分值';
COMMENT ON COLUMN "public"."sag_report_score"."description" IS '描述';
COMMENT ON COLUMN "public"."sag_report_score"."comment" IS '评语';
COMMENT ON COLUMN "public"."sag_report_score"."advantage" IS '优势';
COMMENT ON COLUMN "public"."sag_report_score"."development" IS '发展';
COMMENT ON TABLE  "public"."sag_report_score" IS '分值分区间解释';



DROP TABLE IF EXISTS "public"."sag_report_dimension_score";
CREATE TABLE "public"."sag_report_dimension_score" (
	"id"                            VARCHAR(36)  NOT NULL,
	"project_id"                    VARCHAR(36) ,
	"questionnaire_id"              VARCHAR(36) ,
	"person_id"                     VARCHAR(36) ,
	"dimension_code"                VARCHAR (50) ,
	"fraction"                      NUMERIC(53,2),
	"total_number_of_questions"     INT4,
	"number_of_questions_answered"  INT4,
	"correct_number_of_questions"   INT4,
	"create_by"                     VARCHAR(36),
	"create_time"                   TIMESTAMP(3),
	"update_time"                   TIMESTAMP(3),
	"last_update_by"                VARCHAR(36),
	"is_deleted"                    BOOL,
	"app_id"                        VARCHAR(36),
	CONSTRAINT  "___pk__sag_report_dimension_score___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."sag_report_dimension_score"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."sag_report_dimension_score"."questionnaire_id" IS '问卷id';
COMMENT ON COLUMN "public"."sag_report_dimension_score"."person_id" IS '人员id';
COMMENT ON COLUMN "public"."sag_report_dimension_score"."dimension_code" IS '维度code';
COMMENT ON COLUMN "public"."sag_report_dimension_score"."fraction" IS '经过算法后子维度得分';
COMMENT ON COLUMN "public"."sag_report_dimension_score"."total_number_of_questions" IS '该问卷维度的总题数';
COMMENT ON COLUMN "public"."sag_report_dimension_score"."number_of_questions_answered" IS '该问卷维度的该人员填答题数';
COMMENT ON COLUMN "public"."sag_report_dimension_score"."correct_number_of_questions" IS '该问卷维度的正确数';
COMMENT ON TABLE  "public"."sag_report_dimension_score" IS '经过算法后报告子维度得分';



DROP TABLE IF EXISTS "public"."report_module_content";
CREATE TABLE "public"."report_module_content" (
	"id"                    VARCHAR(36)        NOT NULL,
	"type"                  VARCHAR (50) ,
	"module"                VARCHAR (50) ,
	"questionnaire_id"      VARCHAR(36) ,
	"person_id"             VARCHAR(36) ,
	"project_id"            VARCHAR(36) ,
	"data"                  JSON,
	"create_by"             VARCHAR(36),
	"create_time"           TIMESTAMP(3) ,
	"update_time"           TIMESTAMP(3) ,
	"last_update_by"        VARCHAR(36),
	"is_deleted"            BOOL,
	"app_id"                VARCHAR(36),
	CONSTRAINT  "___pk__report_module_content___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."report_module_content"."type" IS '报告类型';
COMMENT ON COLUMN "public"."report_module_content"."module" IS '报告模块';
COMMENT ON COLUMN "public"."report_module_content"."questionnaire_id" IS '问卷id';
COMMENT ON COLUMN "public"."report_module_content"."person_id" IS '人员id';
COMMENT ON COLUMN "public"."report_module_content"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."report_module_content"."data" IS '报告数据';
COMMENT ON TABLE  "public"."report_module_content" IS '报告模块内容';


DROP TABLE IF EXISTS "public"."sag_report_file";
CREATE TABLE "public"."sag_report_file" (
	"id"                        VARCHAR(36)        NOT NULL,
	"project_id"                VARCHAR(36) ,
	"questionnaire_id"          VARCHAR(36) ,
	"person_id"                 VARCHAR(36) ,
	"url"                       VARCHAR (100) ,
	"file_url"                  VARCHAR (100) ,
	"remark"                    VARCHAR (500) ,
	"create_by"                 VARCHAR(36),
	"create_time"               TIMESTAMP(3),
	"update_time"               TIMESTAMP(3),
	"last_update_by"            VARCHAR(36),
	"is_deleted"                BOOL,
	"app_id"                    VARCHAR(36),
    CONSTRAINT  "___pk__sag_report_file___" PRIMARY KEY ("id")
) ;
COMMENT ON COLUMN "public"."sag_report_file"."project_id" IS '活动ID';
COMMENT ON COLUMN "public"."sag_report_file"."questionnaire_id" IS '问卷ID';
COMMENT ON COLUMN "public"."sag_report_file"."person_id" IS '人员ID';
COMMENT ON COLUMN "public"."sag_report_file"."url" IS '查看报告路径';
COMMENT ON COLUMN "public"."sag_report_file"."file_url" IS '下载路径';
COMMENT ON COLUMN "public"."sag_report_file"."remark" IS '备注';
COMMENT ON TABLE  "public"."sag_report_file" IS '报告上传文件表';
