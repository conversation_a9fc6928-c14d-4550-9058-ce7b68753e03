DROP TABLE IF EXISTS "public"."tenant_info";
CREATE TABLE tenant_info (
	id varchar(36) NOT NULL,
	tenant_id varchar(36) NOT NULL,
	remark text NULL,
	is_old_tenant bool NULL DEFAULT false,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__tenant_info___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."tenant_info"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."tenant_info"."remark" IS '备注';
COMMENT ON COLUMN "public"."tenant_info"."is_old_tenant" IS '是否老租户';
COMMENT ON TABLE  "public"."tenant_info" IS '租户信息表';


CREATE INDEX ___idx_tenant_info_tenant_id___ ON public.tenant_info(tenant_id);