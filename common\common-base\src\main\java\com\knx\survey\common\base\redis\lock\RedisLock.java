package com.knx.survey.common.base.redis.lock;


import com.knx.common.redis.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.params.SetParams;

import java.util.Collections;


@Component
public class RedisLock {

    private static final String LOCK_SUCCESS = "OK";
    private static final Long RELEASE_SUCCESS = 1L;
    private static final Integer EXPIRE_TIME = 60000;
    private static final Integer FAIL_TIME = 100000;


    @Autowired
    private JedisPool jedisPool;

    @Autowired
    private RedisUtil redisUtil;


    public boolean lock(String lockKey, String requestId) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            SetParams setParams = new SetParams();
            setParams.nx();
            setParams.px((long) EXPIRE_TIME);
            long start = System.currentTimeMillis();

            for (; ; ) {
                String result = jedis.set(lockKey, requestId, setParams);
                if (LOCK_SUCCESS.equals(result)) {
                    return true;
                }
                long end = System.currentTimeMillis();
                if ((end - start) > FAIL_TIME) {
                    return false;
                }
            }
        } finally {
            redisUtil.close(jedis);
        }
    }

    public boolean unLock(String lockKey, String requestId) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
            Object result = jedis.eval(script, Collections.singletonList(lockKey), Collections.singletonList(requestId));
            return RELEASE_SUCCESS.equals(result);
        } finally {
            redisUtil.close(jedis);
        }
    }
}
