package com.knx.bean.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/10/19 9:43
 */

@ToString(callSuper = true)
@AllArgsConstructor
@Getter
@FieldNameConstants
public enum KnxCardTypeEnum {

    POPULARIZE("推广"),
    COOPERATION("合作"),
    PRESENTATION("赠送"),
    OTHER("其它");

    private String name;
}
