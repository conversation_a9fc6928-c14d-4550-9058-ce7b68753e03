DROP TABLE IF EXISTS "public"."sag_file";
CREATE TABLE public.sag_file (
    id varchar(36) NOT NULL,
    business_type varchar(50) NOT NULL,
    project_id varchar(36) NOT NULL,
    questionnaire_id varchar(36) DEFAULT NULL,
    question_id varchar(36) DEFAULT NULL,
    file_url varchar(500) DEFAULT NULL,
    file_type varchar(50) NOT NULL,
    fail_count int4 DEFAULT 0,
    file_language varchar(50) DEFAULT 'zh_CN',
    prisma_report_data_id varchar(36) DEFAULT NULL,
    create_by varchar(36) DEFAULT NULL,
    create_time timestamp(3) NULL DEFAULT now(),
    update_time timestamp(3) NULL DEFAULT now(),
    last_update_by varchar(36) NULL,
    is_deleted bool NULL DEFAULT false,
    app_id varchar(36) NULL,
    CONSTRAINT "___pk___sag_file___" PRIMARY KEY (id)
);
COMMENT ON TABLE  "public"."sag_file" IS '射手座文件表';
COMMENT ON COLUMN "public"."sag_file"."business_type" IS '业务类型';
COMMENT ON COLUMN "public"."sag_file"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."sag_file"."questionnaire_id" IS '问卷id';
COMMENT ON COLUMN "public"."sag_file"."question_id" IS '题目id';
COMMENT ON COLUMN "public"."sag_file"."file_url" IS '文件id';
COMMENT ON COLUMN "public"."sag_file"."file_type" IS '文件类型';
COMMENT ON COLUMN "public"."sag_file"."file_language" IS '文件语言';
COMMENT ON COLUMN "public"."sag_file"."fail_count" IS '打印文件失败次数';
COMMENT ON COLUMN "public"."sag_file"."prisma_report_data_id" IS 'prisma报告ID';