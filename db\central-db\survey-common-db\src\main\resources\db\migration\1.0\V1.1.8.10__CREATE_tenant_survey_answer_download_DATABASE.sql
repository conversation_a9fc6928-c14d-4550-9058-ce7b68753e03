DROP TABLE IF EXISTS "public"."tenant_survey_answer_download";
CREATE TABLE tenant_survey_answer_download (
	id varchar(36) NOT NULL,
	tenant_ids json NOT NULL,
	standard_questionnaire_ids json NOT NULL,
	file_url varchar(36) NULL,
	status varchar(50) NOT NULL,
	reason text NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__tenant_survey_answer_download___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."tenant_survey_answer_download"."tenant_ids" IS '租户ID列表';
COMMENT ON COLUMN "public"."tenant_survey_answer_download"."standard_questionnaire_ids" IS '标准问卷ID列表';
COMMENT ON COLUMN "public"."tenant_survey_answer_download"."file_url" IS '文件下载地址';
COMMENT ON COLUMN "public"."tenant_survey_answer_download"."status" IS '状态';
COMMENT ON COLUMN "public"."tenant_survey_answer_download"."reason" IS '失败原因';
COMMENT ON TABLE  "public"."tenant_survey_answer_download" IS '租户原始填答下载记录表';