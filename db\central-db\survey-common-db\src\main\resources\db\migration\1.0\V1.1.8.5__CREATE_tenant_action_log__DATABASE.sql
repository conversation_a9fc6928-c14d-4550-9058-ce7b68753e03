CREATE TABLE "public"."tenant_action_log" (
    "id" varchar(36)  NOT NULL,
    "user_id" varchar(36) NULL,
    "user_name" varchar(100) NULL,
    "role_name" varchar(500) NULL,
    "role_id" varchar(500) NULL,
    "tenant_id" varchar(36) NULL,
    "tenant_name" varchar(100) NULL,
    "employee_group_id" varchar(500) NULL,
    "employee_group_name" varchar(500) NULL,
    "action_type" varchar(36) NULL,
    "result" varchar(36) NULL,
    "action_time" timestamp(3) DEFAULT now(),
    "host_ip" varchar(36) NULL,
    "host_name" varchar(50) NULL,
    "browser" varchar(50) NULL,
    "remark" varchar(500) NULL,
    "create_by" varchar(36) NULL,
    "create_time" timestamp(6) NULL DEFAULT now(),
    "update_time" timestamp(6) NULL DEFAULT now(),
    "last_update_by" varchar(36) NULL,
    "is_deleted" bool NULL DEFAULT false,
    "app_id" varchar(36) NULL,
    CONSTRAINT "___pk__tenant_action_log___" PRIMARY KEY (id)
)
;
COMMENT ON COLUMN "public"."tenant_action_log"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."tenant_action_log"."user_name" IS '用户名';
COMMENT ON COLUMN "public"."tenant_action_log"."role_name" IS '角色权限名称';
COMMENT ON COLUMN "public"."tenant_action_log"."role_id" IS '角色权限ID';
COMMENT ON COLUMN "public"."tenant_action_log"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."tenant_action_log"."tenant_name" IS '租户名';
COMMENT ON COLUMN "public"."tenant_action_log"."employee_group_id" IS '数据权限组名称';
COMMENT ON COLUMN "public"."tenant_action_log"."employee_group_name" IS '数据权限组ID';
COMMENT ON COLUMN "public"."tenant_action_log"."action_type" IS '操作类型';
COMMENT ON COLUMN "public"."tenant_action_log"."result" IS '操作结果';
COMMENT ON COLUMN "public"."tenant_action_log"."action_time" IS '操作时间';
COMMENT ON COLUMN "public"."tenant_action_log"."host_ip" IS '主机IP';
COMMENT ON COLUMN "public"."tenant_action_log"."host_name" IS '主机名';
COMMENT ON COLUMN "public"."tenant_action_log"."browser" IS '浏览器';
COMMENT ON COLUMN "public"."tenant_action_log"."remark" IS '备注';
COMMENT ON TABLE "public"."tenant_action_log" IS '租户登录日志表';

-- ----------------------------
-- Indexes structure for table tenant_action_log
-- ----------------------------
CREATE INDEX ___idx_tenant_action_log_action_type___ ON public.tenant_action_log(action_type);

CREATE INDEX ___idx_tenant_action_log_host_ip___ ON public.tenant_action_log(host_ip);

CREATE INDEX ___idx_tenant_action_log_user_id___ ON public.tenant_action_log(user_id);

CREATE INDEX ___idx_tenant_action_log_action_time_action_type___ ON public.tenant_action_log(action_time,action_type);
