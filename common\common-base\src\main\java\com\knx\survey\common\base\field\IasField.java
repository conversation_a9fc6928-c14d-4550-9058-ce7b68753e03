package com.knx.survey.common.base.field;

import com.knx.survey.model.I18NString;
import com.knx.survey.model.SagI18NString;

import java.text.MessageFormat;

public class IasField {
    public static I18NString NAN_NAME = new SagI18NString(SurveyField.NAN, SurveyField.NAN);
    public static I18NString EMPTY_NAME = new SagI18NString("", "");
    public static SagI18NString SAG_EMPTY_NAME = new SagI18NString("", "");
    public static I18NString EEI_INDEX_NAME = new SagI18NString("敬业度指数（EEI）", "Employee Engagement Index（EEI）");
    public static I18NString QUESTION_ITEM_NAME = new SagI18NString("题目名称", "Item");
    public static I18NString APPROVAL_NAME = new SagI18NString("赞成百分比", "Approval");
    public static I18NString NEUTRAL_NAME = new SagI18NString("中立百分比", "Neutrality");
    public static I18NString DISAPPROVAL_NAME = new SagI18NString("不赞成百分比", "Disapproval");

    public static I18NString APPROVAL_ABBR_NAME = new SagI18NString("赞成", "Approval");
    public static I18NString NEUTRAL_ABBR_NAME = new SagI18NString("中立", "Neutrality");
    public static I18NString DISAPPROVAL_ABBR_NAME = new SagI18NString("不赞成", "Disapproval");

    public static I18NString NORM_DIVIDED_AREA_NAME = new SagI18NString("常模切分区域", "Norm-divided Area");
    public static I18NString NO_NAME = new SagI18NString("序号", "No");

    public static I18NString EEI_NAME = new SagI18NString("敬业度指数", "EEI");
    public static I18NString EEI_ABBR_NAME = new SagI18NString("敬业度", "EEI");
    public static I18NString EEI_NAME_VIVO = new SagI18NString("敬业水平", "EEI");
    public static I18NString OCI_NAME = new SagI18NString("组织能力指数", "OCI");
    public static I18NString ESI_NAME = new SagI18NString("满意度指数", "ESI");
    public static I18NString ESI_ABBR_NAME = new SagI18NString("满意度", "ESI");
    public static I18NString ESI_NAME_VIVO = new SagI18NString("满意水平", "ESI");
    public static I18NString ANALYSIS_OBJECT_NAME = new SagI18NString("分析主体", "Analysis Object");
    public static I18NString COMPANY_NAME = new SagI18NString("公司整体", "Company");
    public static I18NString COMPANY_NAME_VIVO = new SagI18NString("公司值", "Company");
    public static I18NString DEPARTMENT_NAME = new SagI18NString("部门", "Department");
    public static I18NString ORGANIZATION_NAME = new SagI18NString("组织", "Organization");
    public static I18NString ORGANIZATION_NAME_VIVO = new SagI18NString("组织名称", "Superior Department");

    public static I18NString DEPARTMENT_NAME_VIVO = new SagI18NString("N-1组织", "Department");
    public static I18NString DIMENSION_NAME = new SagI18NString("维度", "Dimension");
    public static I18NString QUESTION_NAME = new SagI18NString("题目", "Item");
    public static I18NString DIMENSION_QUESTION_NAME = new SagI18NString("维度/题目", "Dimension/Item");
    public static I18NString NORM_NAME = new SagI18NString("常模", "Norm");
    public static I18NString INDEX_NAME = new SagI18NString("指数", "Index");

    public static I18NString PERSON_NUMBER_NAME = new SagI18NString("人数", "Number");

    public static I18NString PRIMARY_DEPARTMENT_NAME = new SagI18NString("一级部门", "Primary department");
    public static I18NString SUPERIOR_DEPARTMENT_NAME = new SagI18NString("上级部门", "Superior Department");
    public static I18NString SUPERIOR_DEPARTMENT_NAME_VIVO = new SagI18NString("上级组织", "Superior Department");
    public static I18NString SUPERIOR_DEPARTMENT_NAME_VIVO2 = new SagI18NString("N+1组织", "Superior Department");
    public static I18NString SUPERIOR_NAME = new SagI18NString("上一级", "Superior");

    public static I18NString BENCHMARK_NAME = new SagI18NString("标杆群体", "Benchmark");
    public static I18NString AVG_NAME = new SagI18NString("平均分", "Average score");
    public static I18NString CORRELATION_NAME = new SagI18NString("相关系数", "Correlation");

    public static I18NString DATE_NAME = new SagI18NString("调研时间", "Date");
    public static I18NString INVITE_NUMBER_NAME = new SagI18NString("邀请人数", "Invite Number");
    public static I18NString FINISH_NUMBER_NAME = new SagI18NString("完成人数", "Finish Number");
    public static I18NString VALID_NUMBER_NAME = new SagI18NString("有效填答人数", "Valid Number");
    public static I18NString VALID_RATE_NAME = new SagI18NString("有效填答率", "Valid Rate");

    public static I18NString FINISH_ANSWER_NUMBER_NAME = new SagI18NString("应答数", "Finish Number");
    public static I18NString VALID_ANSWER_NUMBER_NAME = new SagI18NString("有效应答数", "Valid Number");
    public static I18NString VALID_ANSWER_RATE_NAME = new SagI18NString("有效应答率", "Valid Rate");
    public static I18NString INVALID_RATE_NAME = new SagI18NString("无效填答率", "Valid Rate");


    public static I18NString VALID_PERSON_AND_PERCENTAGE_NAME = new SagI18NString("有效人数及占比", "Valid Number & Percentage");

    public static I18NString LOWEST_ITEM = new SagI18NString("最低分项目", "Lowest Item");
    public static I18NString IS_LOWEST_ITEM = new SagI18NString("是否最低分项目", "");

    public static I18NString HIGHEST_ITEM = new SagI18NString("最高分项目", "Highest Item");
    public static I18NString PAST_YEAR_KD = new SagI18NString("是否上年KD", "Past Year KD");
    public static I18NString PAST_YEAR_KDA = new SagI18NString("是否上年KDA", "Past Year KD");
    public static I18NString FOCUS = new SagI18NString("关注度", "Focus");
    public static I18NString SCORE_TOP_10 = new SagI18NString("得分前10", "Score Top 10");
    public static I18NString TOP_10 = new SagI18NString("前10", "Top 10");
    public static I18NString SCORE_BOTTOM_10 = new SagI18NString("得分后10", "Score Bottom 10");
    public static I18NString BOTTOM_10 = new SagI18NString("后10", "Bottom 10");
    public static I18NString CORRELATION_TOP_10 = new SagI18NString("相关前10", "Correlation Top 10");
    public static I18NString CORRELATION_BOTTOM_10 = new SagI18NString("相关后10", "Correlation Bottom 10");
    public static I18NString COMPANY_TOP_10 = new SagI18NString("公司整体前10", "Company Top 10");
    public static I18NString COMPANY_BOTTOM_10 = new SagI18NString("公司整体后10", "Company Bottom 10");

    public static I18NString Y_NAME = new SagI18NString("是", "Y");

    public static I18NString ENERGY_AREA_NAME = new SagI18NString("活力区", "Energy Area");
    public static I18NString FATIGUE_AREA_NAME = new SagI18NString("疲惫区", "Fatigue Area");
    public static I18NString NEGLECT_AREA_NAME = new SagI18NString("怠慢区", "Neglect Area");
    public static I18NString RISK_AREA_NAME = new SagI18NString("风险区", "Risk Area");

    public static I18NString OPTION_NAME = new SagI18NString("选项", "Option");
    public static I18NString PERCENT_NAME = new SagI18NString("占比", "Percent");

    public static I18NString DIMENSION_SCORE_NAME = new SagI18NString("维度得分", "Approval");
    public static I18NString SCORE_NAME = new SagI18NString("得分", "Approval");

    public static I18NString THIS_YEAR = new SagI18NString("今年", "this year");

    public static I18NString NAME_NAME = new SagI18NString("名称", "Name");

    public static I18NString PROTOTYPE_NAME = new SagI18NString("原型", "Prototype");

    public static I18NString HIGH_PROTOTYPE_NAME = new SagI18NString("较高原型", "");

    public static I18NString PROTOTYPE_SCORE_NAME = new SagI18NString("原型得分", "");

    public static I18NString QUESTION_SCORE_NAME = new SagI18NString("题目得分", "");

    public static I18NString PROTOTYPE_DESCRIPTION_NAME = new SagI18NString("原型描述", "");

    public static I18NString ADVANTAGE_NAME = new SagI18NString("优势", "");

    public static I18NString DISADVANTAGE_NAME = new SagI18NString("劣势", "");

    public static I18NString RISK_NAME = new SagI18NString("风险", "");

    public static I18NString PROTOTYPE_AVERAGE_NAME = new SagI18NString("原型均分", "");

    public static I18NString ORGANIZATION_THINKING_NAME = new SagI18NString("组织思维", "");

    public static I18NString ORGANIZATION_BEHAVIOUR_NAME = new SagI18NString("组织行为", "");

    public static I18NString EXPECTATION_CULTURE_NAME = new SagI18NString("期望文化", "");

    public static I18NString D_VALUE_NAME = new SagI18NString("差值", "");

    public static I18NString MANAGER_LEVEL_NAME = new SagI18NString("员工分类", "");

    public static I18NString MANAGER_NAME = new SagI18NString("管理层", "");

    public static I18NString NOT_MANAGER_NAME = new SagI18NString("非管理层", "");

    public static I18NString ORGANIZATIONAL_STRUCTURE_NAME = new SagI18NString("组织架构", "");

    public static I18NString DEMOGRAPHIC_NAME = new SagI18NString("人口标签", "");

    public static I18NString DEMOGRAPHIC_ITEM_NAME = new SagI18NString("人口标签项目", "");

    public static I18NString CONCENTRATION_DIMENSION_NAME = new SagI18NString("专注力", "Concentration");

    public static String VALID_QUESTION_CODE = "valid-question-1";

    public static I18NString VALID_QUESTION_NAME = new SagI18NString("本题为测试填答专注力的题目，请您选择“强烈不同意”。", "Please select \"strongly disagree\" to test your concentration.");

    public static I18NString VALID_QUESTION_OPTION_1_NAME = new SagI18NString("强烈不同意", "Strongly disagree");

    public static I18NString VALID_QUESTION_OPTION_2_NAME = new SagI18NString("不同意", "Disagree");

    public static I18NString VALID_QUESTION_OPTION_3_NAME = new SagI18NString("略有异议", "Mildly disagree");

    public static I18NString VALID_QUESTION_OPTION_4_NAME = new SagI18NString("有点同意", "Mildly agree");

    public static I18NString VALID_QUESTION_OPTION_5_NAME = new SagI18NString("同意", "Agree");

    public static I18NString VALID_QUESTION_OPTION_6_NAME = new SagI18NString("强烈同意", "Strongly agree");

    public static I18NString INDEX_DIMENSION_NAME = new SagI18NString("指数/维度", "Index/Dimension");

    public static I18NString RELIABILITY_NAME = new SagI18NString("调研有效性", "Reliability");

    public static I18NString MULTI_OPEN_TOTAL_SELECT_NUMBER_DESCRIPTION = new SagI18NString("群体追问题频次统计，频次为各选项被选择的次数占总选择次数的比例。", "Multiple Choice Selection Rate: The percentage of times an option was chosen out of all selections made.");

    public static I18NString MULTI_OPEN_TOTAL_PERSON_NUMBER_DESCRIPTION = new SagI18NString("群体追问题频次统计，频次为各选项被选择的次数占总有效评价人数的比例。", "Multiple Choice Selection Rate: The percentage of times an option was chosen out of the total number of valid respondents.");


    /**
     * 四窗第一象限
     */
    public static String QUADRANT_1 = "QUADRANT_1";

    /**
     * 四窗第二象限
     */
    public static String QUADRANT_2 = "QUADRANT_2";

    /**
     * 四窗第三象限
     */
    public static String QUADRANT_3 = "QUADRANT_3";

    /**
     * 四窗第四象限
     */
    public static String QUADRANT_4 = "QUADRANT_4";

    /**
     * 冒号
     */
    public static final String COLON=":";

    /**
     * 换行符
     */
    public static final String NEW_LINE="\n";

    /**
     * 五角星
     */
    public static final String STAR ="★";


}
