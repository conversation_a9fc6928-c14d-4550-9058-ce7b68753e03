package com.knx.survey.common.base.aspect;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.knx.common.base.service.exception.BusinessException;
import com.knx.common.security.MultipleTenantDataSourceSessionContext;
import com.knx.survey.common.base.model.OperatorLogModel;
import com.knx.survey.common.util.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 描述 日志aop
 */
@Slf4j
@Aspect
@Component
public class LogAspect {
    private static ThreadLocal<Long> startTime = new ThreadLocal<>();
    private static ThreadLocal<OperatorLogModel> operatorLog = new ThreadLocal<>();

    @Autowired
    private LogUtils logUtils;

    /**
     * 描述 切面
     */
    @Pointcut("execution(* *..controller..*.*(..))")
    public void webLog() {
    }

    @Before("webLog()")
    public void doBeforeInServiceLayer(JoinPoint joinPoint) {
        try {
            // 接收到请求，记录请求内容
            startTime.set(System.currentTimeMillis());
            operatorLog.set(new OperatorLogModel());
            OperatorLogModel operatorLogModel = operatorLog.get();
            String methodName = joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName();
            logUtils.doBefore(operatorLogModel, methodName, joinPoint.getArgs());
            operatorLogModel.setStartTime(new Date(startTime.get()));
            operatorLogModel.setTenantId(MultipleTenantDataSourceSessionContext.getTenantId());
            log.info("request before logId={} requestLog={}", operatorLogModel.getId(), JSON.toJSONString(operatorLogModel));
        } catch (Exception e) {
            e.printStackTrace();
            startTime.remove();
            operatorLog.remove();
        }
    }

    @Around("webLog()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        try {
            return proceedingJoinPoint.proceed();
        } catch (Exception e) {
            OperatorLogModel operatorLogModel = operatorLog.get();
            operatorLogModel.setEndTime(new Date(System.currentTimeMillis()));
            operatorLogModel.setSpendTime(System.currentTimeMillis() - startTime.get());
            operatorLogModel.setException(Throwables.getStackTraceAsString(e));
            if (e instanceof BusinessException) {
                log.info("request error logId={} spendTime={} msg={} exception={}", operatorLogModel.getId(), operatorLogModel.getSpendTime(), e.getMessage(), operatorLogModel.getException(), e);
            } else {
                log.error("request error logId={} spendTime={} msg={} exception={}", operatorLogModel.getId(), operatorLogModel.getSpendTime(), e.getMessage(), operatorLogModel.getException(), e);
            }

            startTime.remove();
            operatorLog.remove();
            throw e;
        }
    }

    @AfterReturning(returning = "ret", pointcut = "webLog()")
    public void doAfterReturning(Object ret) {
        try {
            OperatorLogModel operatorLogModel = operatorLog.get();
            operatorLogModel.setEndTime(new Date(System.currentTimeMillis()));
            operatorLogModel.setSpendTime(System.currentTimeMillis() - startTime.get());
            log.info("request after logId={} spendTime={} uri={}", operatorLogModel.getId(), operatorLogModel.getSpendTime(), operatorLogModel.getUri());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            startTime.remove();
            operatorLog.remove();
        }
    }

}
