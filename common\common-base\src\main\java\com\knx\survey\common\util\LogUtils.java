package com.knx.survey.common.util;

import com.alibaba.fastjson.JSONObject;
import com.knx.survey.common.base.model.OperatorLogModel;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Component
public class LogUtils {

    public void doBefore(OperatorLogModel operatorLogModel, String method, Object[] args) {
        // 接收到请求，记录请求内容
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        //获取传入目标方法的参数
        for (int i = 0; i < args.length; i++) {
            Object o = args[i];
            if (o instanceof ServletRequest || (o instanceof ServletResponse) || o instanceof MultipartFile || o instanceof MultipartFile[]) {
                args[i] = o.toString();
            }
        }
        String parameterStr = JSONObject.toJSONString(args);
        operatorLogModel.setParameter(parameterStr);
        operatorLogModel.setUri(request.getRequestURI());
        operatorLogModel.setRequestUri(request.getRequestURL().toString());
        Map<String, String> browserMap = ToolUtil.getOsAndBrowserInfo(request);
        operatorLogModel.setBrowser(browserMap.get("os") + "-" + browserMap.get("browser"));
        String ip = ToolUtil.getClientIp(request);
        operatorLogModel.setRemoteAddr(ip);
        operatorLogModel.setClassMethod(method);
        operatorLogModel.setHttpMethod(request.getMethod());
        operatorLogModel.setType(ToolUtil.isAjax(request) ? "Ajax请求" : "普通请求");

    }
}
