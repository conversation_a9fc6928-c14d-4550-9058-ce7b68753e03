package com.knx.survey.service.impl.domain.report.tencent;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.knx.common.base.service.exception.BusinessException;
import com.knx.common.dynamic.annotation.MultiTenant;
import com.knx.survey.dao.mapper.report.tencent.PrismaOrganizationDemographicApprovalDataMapper;
import com.knx.survey.error.SurveyErrorCode;
import com.knx.survey.model.enums.DimensionRankEnum;
import com.knx.survey.model.report.prisma.tencent.ApprovalQuestionDimensionDetail;
import com.knx.survey.model.report.prisma.tencent.PrismaOrganizationDemographicApprovalData;
import com.knx.survey.model.tenant.project.SurveyProject;
import com.knx.survey.service.api.domain.IProjectService;
import com.knx.survey.service.api.domain.report.tencent.IPrismaOrganizationDemographicApprovalDataService;
import com.knx.survey.service.api.domain.tencent.ITencentParsingExcelService;
import com.knx.survey.vo.ProjectVO;
import com.knx.survey.vo.tencent.ImportDimensionScoreResultVo;
import com.knx.survey.vo.tencent.ImportIndexDimensionDataVo;
import com.knx.survey.vo.tencent.ProjectDataCountVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import com.knx.survey.model.enums.MainlandEnum;
import com.knx.survey.vo.tencent.ScaleDataWarehouseResponse;

/**
 * 组织-人口标签维度赞成度数据服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-08
 */
@Slf4j
@MultiTenant
@Service
public class PrismaOrganizationDemographicApprovalDataServiceImpl 
        extends ServiceImpl<PrismaOrganizationDemographicApprovalDataMapper, PrismaOrganizationDemographicApprovalData>
        implements IPrismaOrganizationDemographicApprovalDataService {

    @Autowired
    private IProjectService projectService;

    @Autowired
    @Lazy
    private ITencentParsingExcelService tencentParsingExcelService;

    @Override
    public void deleteByProjectAndOrgAndMainlandType(String projectId, String organization, String mainlandType) {
        this.baseMapper.deleteByProjectAndOrgAndMainlandType(projectId, organization, mainlandType);
    }
    /**
     * 保存维度评分数据
     *
     * @param resultVo 输入的维度评分结果视图对象
     */
    @Override
    public void saveDimensionScore(ImportDimensionScoreResultVo resultVo) {
        if (resultVo == null || CollectionUtils.isEmpty(resultVo.getSubject())) {
            log.warn("Invalid dimension score data received");
            return;
        }

        PrismaOrganizationDemographicApprovalData organizationDemographicApprovalData = transferImportDataToApprovalData(resultVo.getSubject().get(0));


        // 初始化实体列表以存储最终数据
        List<ApprovalQuestionDimensionDetail> entities = new ArrayList<>();

        // 处理并保存主体数据
        List<ImportIndexDimensionDataVo> subjectData = resultVo.getSubject();
        entities.addAll(subjectData.stream()
                .map(this::createEntity)
                .collect(Collectors.toList()));

        // 处理并保存人口标签数据
        if (MapUtils.isNotEmpty(resultVo.getDems())) {
            resultVo.getDems().values().stream()
                .flatMap(map -> map.values().stream())
                .flatMap(List::stream)
                .map(this::createEntity)
                .forEach(entities::add);
        }

        organizationDemographicApprovalData.setData(entities);

        // 检查是否有数据需要批量保存
        if (CollectionUtils.isNotEmpty(entities)) {
            log.info("Saving  dimension score data for projectId: {}, orgCode: {}, mainlandType: {}, size: {}", organizationDemographicApprovalData.getProjectId(), organizationDemographicApprovalData.getOrganization(), organizationDemographicApprovalData.getMainlandType().name(), entities.size());
            this.baseMapper.deleteByProjectAndOrgAndMainlandType(organizationDemographicApprovalData.getProjectId(), organizationDemographicApprovalData.getOrganization(), organizationDemographicApprovalData.getMainlandType().name());
            this.save(organizationDemographicApprovalData);
        }
    }

    private PrismaOrganizationDemographicApprovalData transferImportDataToApprovalData(ImportIndexDimensionDataVo data) {
        PrismaOrganizationDemographicApprovalData entity = new PrismaOrganizationDemographicApprovalData();

        // 设置项目ID
        entity.setProjectId(data.getProjectId());

        // 设置发布年份（若未定义，则设为当前年份）
        entity.setReleaseYear(data.getProjectYear());

        // 设置组织编码
        entity.setOrganization(data.getOrgCode());

        // 设置历史数据标识符（假设为固定值，若未指定）
        entity.setHistoryCode(data.getHistoryId());

        // 设置数据覆盖范围类型（假设为总体，可以扩展）
        entity.setMainlandType(data.getMainlandType());

        // 设置参与人数和有效响应数
        entity.setParticipantCount(data.getParticipantCount());
        entity.setValidRespondentCount(data.getValidRespondentCount());

        // 设置是否自定义问题和常模列表（假设未提供时为空）
        entity.setNormCodes(Lists.newArrayList(data.getCompanyNormCode(),data.getInternetNormCode()).stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()));

        // 设置逻辑删除状态（假设未提供时为不删除）
        entity.setIsDeleted(false);

        return entity;
    }


    /**
     * 根据输入视图对象创建实体对象
     *
     * 从视图对象中提取并设置数据信息至实体对象中，
     * 处理各种格式的字段并返回实体对象。
     *
     * @param data 包含调查数据的视图对象
     * @return 设置完毕的实体对象
     */
    private ApprovalQuestionDimensionDetail createEntity(ImportIndexDimensionDataVo data) {
        ApprovalQuestionDimensionDetail entity = new ApprovalQuestionDimensionDetail();

        // 设置人口标签编码
        entity.setDemographic(data.getDemographicCode());

        // 设置类型，转换等级名称为小写
        if (data.getLevel() == DimensionRankEnum.LABEL) {
            entity.setType("index");
        } else if (data.getLevel() == DimensionRankEnum.QUESTION) {
            entity.setType("question");
        } else if (data.getLevel() == DimensionRankEnum.ONE_RANK) {
            entity.setType("dimension1");
        } else if (data.getLevel() == DimensionRankEnum.TWO_RANK) {
            entity.setType("dimension2");
        }

        // 设置指标名（如果标签存在，使用其名称）
        entity.setIndex(data.getLabel() != null ? data.getLabel().name() : null);

        // 设置主维度和次维度编码
        entity.setPrimaryDimension(data.getOneDimensionCode());
        entity.setDimension(data.getTwoDimensionCode());

        // 设置问题编码
        entity.setQuestion(data.getQuestionCode());

        // 处理数值字段
        entity.setApproval(data.getApproval() != null ? BigDecimal.valueOf(data.getApproval()) : null);
        entity.setNeutral(data.getNetural() != null ? BigDecimal.valueOf(data.getNetural()) : null);
        entity.setDisapproval(data.getDisApproval() != null ? BigDecimal.valueOf(data.getDisApproval()) : null);

        // 设置比较数值字段
        entity.setVsHistory(data.getVsHistory() != null ? BigDecimal.valueOf(data.getVsHistory()) : null);
        entity.setVsParentOrg(data.getVsParentOrg() != null ? BigDecimal.valueOf(data.getVsParentOrg()) : null);
        entity.setVsBg(data.getVsBG() != null ? BigDecimal.valueOf(data.getVsBG()) : null);
        entity.setVsCompany(data.getVsCompany() != null ? BigDecimal.valueOf(data.getVsCompany()) : null);
        entity.setVsInternet(data.getVsInternet() != null ? BigDecimal.valueOf(data.getVsInternet()) : null);
        entity.setCorr(data.getCorr() != null ? BigDecimal.valueOf(data.getCorr()) : null);

        // 设置是否自定义问题和常模列表（假设未提供时为空）
        if (data.getLevel() == DimensionRankEnum.QUESTION) {
            entity.setCustomized(StringUtils.isNotBlank(data.getSelectObject()));
        }
        return entity;
    }

    @Override
    public List<PrismaOrganizationDemographicApprovalData> getByProjectIdAndOrganizationCodesAndMainlandType(
            String projectId, List<String> organizationCodes, MainlandEnum mainlandType) {
        return this.lambdaQuery()
                .eq(PrismaOrganizationDemographicApprovalData::getProjectId, projectId)
                .in(PrismaOrganizationDemographicApprovalData::getOrganization, organizationCodes)
                .eq(PrismaOrganizationDemographicApprovalData::getMainlandType, mainlandType)
                .list();
    }

    @Override
    public ScaleDataWarehouseResponse getScaleData(String projectCode, List<String> organizationCodes, String isMainland) {
        if(!NumberUtil.isNumber(projectCode)){
            throw new BusinessException(SurveyErrorCode.PROJECT_IS_NOT_EXIST);
        }

        SurveyProject project = projectService.getByCode(Integer.valueOf(projectCode));
        if (project == null) {
            throw new BusinessException(SurveyErrorCode.PROJECT_IS_NOT_EXIST);
        }
        String projectId = project.getId();

        if (CollectionUtils.isEmpty(organizationCodes)) {
            throw new BusinessException(SurveyErrorCode.ORGANIZATION_CODE_IS_NOT_EMPTY);
        }

        int limitOrganizationSize = 50;
        if (organizationCodes.size() > limitOrganizationSize) {
            throw new BusinessException(String.format(SurveyErrorCode.ORGANIZATION_CODE_SIZE_OVER_LIMIT.getMessage(), limitOrganizationSize), SurveyErrorCode.ORGANIZATION_CODE_SIZE_OVER_LIMIT);
        }

        MainlandEnum mainlandType = MainlandEnum.getByAnother(isMainland);
        if (mainlandType == null) {
            throw new BusinessException("报告属性isMainland:" + isMainland + "不存在", SurveyErrorCode.MAINLAND_NOT_NULL);
        }

        ScaleDataWarehouseResponse response = new ScaleDataWarehouseResponse();
        List<ScaleDataWarehouseResponse.ScaleData> dataListResponse = new ArrayList<>();
        
        if (CollectionUtils.isNotEmpty(organizationCodes)) {
            // 循环处理每个organizationCode
            for (String organizationCode : organizationCodes) {
                // 每次循环只查询一个organizationCode的数据
                List<PrismaOrganizationDemographicApprovalData> orgDataList = this.lambdaQuery()
                        .eq(PrismaOrganizationDemographicApprovalData::getProjectId, projectId)
                        .eq(PrismaOrganizationDemographicApprovalData::getOrganization, organizationCode)
                        .eq(PrismaOrganizationDemographicApprovalData::getMainlandType, mainlandType)
                        .list();
                
                if (CollectionUtils.isNotEmpty(orgDataList)) {
                    ScaleDataWarehouseResponse.ScaleData scaleData = new ScaleDataWarehouseResponse.ScaleData();
                    scaleData.setProjectId(projectCode);
                    scaleData.setReleaseYear(orgDataList.get(0).getReleaseYear());
                    scaleData.setOrganization(organizationCode);
                    scaleData.setHistoryCode(orgDataList.get(0).getHistoryCode());
                    scaleData.setMainlandType(isMainland);
                    scaleData.setParticipantCount(orgDataList.get(0).getParticipantCount());
                    scaleData.setValidRespondentCount(orgDataList.get(0).getValidRespondentCount());
                    scaleData.setNormCodes(orgDataList.get(0).getNormCodes());
                    scaleData.setValues(orgDataList.get(0).getData());
                    dataListResponse.add(scaleData);
                }
            }
        }
        response.setData(dataListResponse);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAndSaveBatch(String projectId, String organization, String mainlandType, List<PrismaOrganizationDemographicApprovalData> datas) {
        this.baseMapper.deleteByProjectAndOrgAndMainlandType(projectId, organization, mainlandType);
        for (PrismaOrganizationDemographicApprovalData data : datas) {
            this.save(data);
        }
    }

    @Override
    public List<String> listIdsByProjectId(String projectId) {
        return this.baseMapper.listIdsByProjectId(projectId);
    }

    @Override
    public List<ProjectDataCountVO> countAllProjectsOrganizationDemographicApprovalData(String remoteServerAddress, String token) {
        List<ProjectDataCountVO> resultList = new ArrayList<>();

        // 获取远程项目列表
        List<ProjectVO> remoteProjectList = tencentParsingExcelService.getRemoteProjects(remoteServerAddress, token);
        if (CollectionUtils.isEmpty(remoteProjectList)) {
            log.warn("未获取到远程项目列表");
            return resultList;
        }

        log.info("开始统计 {} 个远程项目的PrismaOrganizationDemographicApprovalData数据", remoteProjectList.size());

        // 循环统计每个项目
        for (ProjectVO project : remoteProjectList) {
            String projectId = project.getId();
            try {
                int projectDataCount = countProjectOrganizationDemographicApprovalData(projectId);
                ProjectDataCountVO projectDataCountVO = new ProjectDataCountVO(
                    project.getId(),
                    project.getName(),
                    project.getCode(),
                    projectDataCount
                );
                resultList.add(projectDataCountVO);
                log.info("项目 {} ({}) 统计完成，data总数量: {}", project.getName(), projectId, projectDataCount);
            } catch (Exception e) {
                log.error("统计项目 {} ({}) 时发生异常: {}", project.getName(), projectId, e.getMessage(), e);
                ProjectDataCountVO projectDataCountVO = new ProjectDataCountVO(
                    project.getId(),
                    project.getName(),
                    project.getCode(),
                    0
                );
                resultList.add(projectDataCountVO);
            }
        }

        int totalCount = resultList.stream().mapToInt(ProjectDataCountVO::getDataCount).sum();
        log.info("所有项目统计完成，总项目数: {}，总data数量: {}", remoteProjectList.size(), totalCount);

        return resultList;
    }

    /**
     * 统计单个项目的组织-人口标签维度赞成度数据中data的数量
     * @param projectId 项目ID
     * @return data总数量
     */
    private int countProjectOrganizationDemographicApprovalData(String projectId) {
        // 根据projectId查询所有数据ID
        List<String> ids = this.listIdsByProjectId(projectId);
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }

        int totalDataCount = 0;
        log.info("开始统计项目 {} 的PrismaOrganizationDemographicApprovalData数据，共 {} 条记录", projectId, ids.size());

        // 逐条查询数据并统计data字段的数量，避免一次性加载大量JSON数据
        for (int i = 0; i < ids.size(); i++) {
            String id = ids.get(i);
            try {
                // 每次只查询一条记录
                List<PrismaOrganizationDemographicApprovalData> dataList = this.listByIds(Arrays.asList(id));

                if (CollectionUtils.isNotEmpty(dataList)) {
                    PrismaOrganizationDemographicApprovalData data = dataList.get(0);
                    if (data.getData() != null) {
                        totalDataCount += data.getData().size();
                    }
                }

                // 每处理100条记录输出一次进度日志
                log.info("项目 {} 已处理 {}/{} 条记录，当前统计总数: {}", projectId, i + 1, ids.size(), totalDataCount);
            } catch (Exception e) {
                log.error("处理项目 {} 记录ID {} 时发生异常: {}", projectId, id, e.getMessage(), e);
                // 继续处理下一条记录
            }
        }

        return totalDataCount;
    }
}
