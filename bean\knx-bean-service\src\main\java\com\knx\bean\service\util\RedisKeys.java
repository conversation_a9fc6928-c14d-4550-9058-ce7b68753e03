package com.knx.bean.service.util;

/**
 * redis key
 *
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RedisKeys
 * @email <EMAIL>
 * @date 2020/5/26 14:09
 **/
public class RedisKeys {

    private RedisKeys() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 肯豆流水号key
     */
    public static String getSeqNumberKey() {
        return "sag:account:kendou:seqno:";
    }
}
