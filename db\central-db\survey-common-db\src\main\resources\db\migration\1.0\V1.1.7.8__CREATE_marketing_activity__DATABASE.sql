DROP TABLE IF EXISTS "public"."marketing_activity";
CREATE TABLE marketing_activity (
	id varchar(36) NOT NULL,
	standard_questionnaire_id varchar(36) NOT NULL,
	scope_type varchar(50) NOT NULL DEFAULT 'ALL',
	data json NULL,
	start_time timestamp(3) NOT NULL,
	end_time timestamp(3) NOT NULL,
	description text NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__marketing_activity___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."marketing_activity"."standard_questionnaire_id" IS '标准问卷ID';
COMMENT ON COLUMN "public"."marketing_activity"."scope_type" IS '范围';
COMMENT ON COLUMN "public"."marketing_activity"."data" IS '数据';
COMMENT ON COLUMN "public"."marketing_activity"."start_time" IS '开始时间';
COMMENT ON COLUMN "public"."marketing_activity"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."marketing_activity"."description" IS '描述';
COMMENT ON TABLE  "public"."marketing_activity" IS '营销活动表';


CREATE INDEX ___idx_marketing_activity_standard_questionnaire_id___ ON public.marketing_activity(standard_questionnaire_id);