package com.knx.survey.common.base.mq;

import com.knx.common.base.message.KnxMessage;
import com.knx.common.base.message.MessageHeader;
import com.knx.common.security.MultipleTenantDataSourceSessionContext;
import com.knx.common.security.SessionUserContext;
import com.knx.survey.common.util.IDUtil;

import java.util.Date;

public class KnxMessageBuilder {

    private static MessageHeader getHeader() {
        MessageHeader header = new MessageHeader();
        header.setId(IDUtil.getSimpleUUID());
        header.setTimestamp(new Date());
        header.setCreateBy(SessionUserContext.getUserId());
        return header;
    }

    public static KnxMessage buildWithoutLogin(Object params) {
        SurveyMqPayload mqPayload = new SurveyMqPayload();
        mqPayload.setTenantId(MultipleTenantDataSourceSessionContext.getTenantId());
        mqPayload.setNeedLogin(false);
        mqPayload.setParams(params);
        KnxMessage message = new KnxMessage();
        message.setHeader(getHeader());
        message.setPayload(mqPayload);
        return message;
    }

    public static KnxMessage buildWithLogin(Object params) {
        SurveyMqPayload mqPayload = new SurveyMqPayload();
        mqPayload.setTenantId(MultipleTenantDataSourceSessionContext.getTenantId());
        mqPayload.setNeedLogin(true);
        mqPayload.setParams(params);
        KnxMessage message = new KnxMessage();
        message.setHeader(getHeader());
        message.setPayload(mqPayload);
        return message;
    }

    public static KnxMessage buildWithLogin(String tenantId, Object params) {
        SurveyMqPayload mqPayload = new SurveyMqPayload();
        mqPayload.setTenantId(tenantId);
        mqPayload.setNeedLogin(true);
        mqPayload.setParams(params);
        KnxMessage message = new KnxMessage();
        message.setHeader(getHeader());
        message.setPayload(mqPayload);
        return message;
    }

    public static KnxMessage buildTenantSchedule(TenantScheduleMqVo params) {
        SurveyMqPayload mqPayload = new SurveyMqPayload();
        mqPayload.setTenantId(MultipleTenantDataSourceSessionContext.getTenantId());
        mqPayload.setParams(params);
        KnxMessage message = new KnxMessage();
        message.setHeader(getHeader());
        message.setPayload(mqPayload);
        return message;
    }
}
