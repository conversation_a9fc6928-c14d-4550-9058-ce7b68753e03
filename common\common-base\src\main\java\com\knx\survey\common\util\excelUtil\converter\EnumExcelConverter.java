package com.knx.survey.common.util.excelUtil.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.knx.survey.common.util.excelUtil.anno.EnumFormat;
import lombok.SneakyThrows;

import java.lang.reflect.Field;
import java.util.Objects;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/6/11 14:20
 */
public class EnumExcelConverter implements Converter<Enum> {

    @Override
    public Class supportJavaTypeKey() {
        return Enum.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    /**
     * 读表格会调用
     *
     * @param cellData
     * @param contentProperty
     * @param globalConfiguration
     * @return
     */
    @SneakyThrows
    @Override
    public Enum convertToJavaData(CellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String cellDataStr = cellData.getStringValue();
        EnumFormat annotation = contentProperty.getField().getAnnotation(EnumFormat.class);
        Class enumClazz = annotation.value();
        String name = annotation.name();
        Object[] enumConstants = enumClazz.getEnumConstants();
        for (Object obj : enumConstants
        ) {
            if (obj instanceof Enum) {
                String enumName = ((Enum) obj).name();
                if (Objects.equals(enumName, cellDataStr)) {
                    return (Enum) obj; //表格和枚举的字段一样直接返回枚举
                }
                Field[] declaredFields = obj.getClass().getDeclaredFields();
                for (Field field : declaredFields
                ) {
                    field.setAccessible(true);
                    if (Objects.equals(field.getName(), name) && Objects.equals(cellDataStr, field.get(obj))) {//表格和枚举的指定字段的值一样
                        return (Enum) obj;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 写表格会调用
     *
     * @param value
     * @param contentProperty
     * @param globalConfiguration
     * @return
     */
    @SneakyThrows
    @Override
    public CellData convertToExcelData(Enum value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {

        String enumName = value.name();

        EnumFormat annotation = contentProperty.getField().getAnnotation(EnumFormat.class);
        Class enumClazz = annotation.value();
        String name = annotation.name();
        Object[] enumConstants = enumClazz.getEnumConstants();
        for (Object obj : enumConstants
        ) {
            if (obj instanceof Enum && Objects.equals(enumName, ((Enum) obj).name())) {
                Field[] declaredFields = obj.getClass().getDeclaredFields();
                for (Field field : declaredFields
                ) {
                    field.setAccessible(true);
                    if (Objects.equals(field.getName(), name)) {
                        return new CellData(field.get(obj).toString());
                    }
                    ;
                }
            }

        }
        return new CellData(enumName);
    }
}
