-- description字段转换成json类型
ALTER TABLE public.survey_standard_dimension ADD description_bak varchar(50) NULL;
update survey_standard_dimension set description_bak = description where description  is not null;
update survey_standard_dimension set description = null where description  is not null;
ALTER TABLE public.survey_standard_dimension ALTER COLUMN description TYPE json USING description::json;
-- 替换换行符
update survey_standard_dimension set description_bak = replace(description_bak,CHR(10), '') where description_bak is not null;
-- 替换回车符
update survey_standard_dimension set description_bak = replace(description_bak,CHR(13), '') where description_bak is not null;
update survey_standard_dimension set description = cast(concat('{"en_US":"","zh_CN":"', description_bak, '"}') AS json) where description_bak is not null;
ALTER TABLE public.survey_standard_dimension DROP COLUMN description_bak;
