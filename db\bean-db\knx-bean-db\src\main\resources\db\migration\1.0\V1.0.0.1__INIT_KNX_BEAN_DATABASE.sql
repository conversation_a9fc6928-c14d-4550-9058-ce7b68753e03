DROP TABLE IF EXISTS "public"."knx_card";
CREATE TABLE "public"."knx_card"
(
    "id"             VARCHAR(36),
    "card_password"  VARCHAR(50),
    "is_used"        bool,
    "expire_date"    date DEFAULT NOW(),
    "card_type"      VARCHAR(50),
    "amount"         decimal,
    "tenant_id"      VARCHAR(36),
    "create_by"      VARCHAR(36),
    "create_time"    TIMESTAMP(3) DEFAULT NOW(),
    "update_time"    TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by" VARCHAR(36),
    "is_deleted"     BOOL  DEFAULT FALSE,
    "app_id"         VARCHAR(36),
    CONSTRAINT  "___pk__knx_card___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."knx_card"."card_password" IS '卡密码';
COMMENT ON COLUMN "public"."knx_card"."is_used" IS '是否使用';
COMMENT ON COLUMN "public"."knx_card"."expire_date" IS '过期时间';
COMMENT ON COLUMN "public"."knx_card"."amount" IS '金额';
COMMENT ON COLUMN "public"."knx_card"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."knx_card"."card_type" IS '卡片类型';
COMMENT ON TABLE "public"."knx_card" IS '肯豆卡';