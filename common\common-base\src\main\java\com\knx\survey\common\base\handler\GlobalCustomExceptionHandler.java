package com.knx.survey.common.base.handler;

import com.alibaba.excel.exception.ExcelGenerateException;
import com.knx.common.base.service.exception.BusinessException;
import com.knx.common.base.service.exception.CommonErrorCode;
import com.knx.common.base.web.response.WebResponse;
import com.knx.common.security.MultipleTenantDataSourceSessionContext;
import com.knx.common.web.config.GlobalExceptionHandler;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.authz.UnauthorizedException;
import org.postgresql.util.PSQLException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * User: 林超
 * Date: 2020/3/24
 * Time: 11:20 AM
 */
@Slf4j
@ControllerAdvice
@Order(10)
public class GlobalCustomExceptionHandler extends GlobalExceptionHandler {

    @Autowired(required = false)
    private ITenantErrorLogSaveService tenantErrorLogSaveService;

    @ResponseBody
    @ExceptionHandler(BusinessException.class)
    @Override
    public WebResponse globalException(HttpServletResponse response, BusinessException e) {
        return super.globalException(response, e);
    }

    @ResponseBody
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @Override
    public WebResponse globalException(HttpServletResponse response, MethodArgumentNotValidException e) {
        return super.globalException(response, e);
    }

    @ResponseBody
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @Override
    public WebResponse globalException(HttpServletResponse response, HttpRequestMethodNotSupportedException e) {
        return super.globalException(response, e);
    }

    @ResponseBody
    @ExceptionHandler(UnauthorizedException.class)
    @Override
    public WebResponse unauthorizedExceptionException(HttpServletResponse response, UnauthorizedException e) {
        return super.unauthorizedExceptionException(response, e);
    }

    @ResponseBody
    @ExceptionHandler(AuthorizationException.class)
    @Override
    public WebResponse authorizationException(HttpServletResponse response, AuthorizationException e) {
        return super.authorizationException(response, e);
    }

    @ResponseBody
    @ExceptionHandler(AuthenticationException.class)
    @Override
    public WebResponse authenticationException(HttpServletResponse response, AuthenticationException e) {
        return super.authenticationException(response, e);
    }

    @ResponseBody
    @ExceptionHandler(DataIntegrityViolationException.class)
    @Override
    public WebResponse createGlobalException(HttpServletResponse response, PSQLException e) {
        return super.createGlobalException(response, e);
    }


    @ResponseBody
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @Override
    public WebResponse badRequestGlobalException(HttpServletResponse response, HttpMessageNotReadableException e) {
        return super.badRequestGlobalException(response, e);
    }

    @ResponseBody
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @Override
    public WebResponse badMediaTypeNotSupportedException(HttpServletResponse response, HttpMediaTypeNotSupportedException e) {
        return super.badMediaTypeNotSupportedException(response, e);
    }

    @ResponseBody
    @ExceptionHandler(Exception.class)
    @Override
    public WebResponse internalServerErrorException(HttpServletResponse response, Exception e) {
        recordLog(response, e);
        return super.internalServerErrorException(response, e);
    }

    @ResponseBody
    @ExceptionHandler(FeignException.class)
    @Override
    public WebResponse feignServerErrorException(HttpServletResponse response, FeignException e) {
        return super.feignServerErrorException(response, e);
    }

    @ResponseBody
    @ExceptionHandler(BadSqlGrammarException.class)
    @Override
    public WebResponse badSqlGrammarException(HttpServletResponse response, BadSqlGrammarException e) {
        return super.badSqlGrammarException(response, e);
    }

    @ResponseBody
    @ExceptionHandler(HttpMessageNotWritableException.class)
    public WebResponse httpMessageNotWritableException(HttpMessageNotWritableException e) {
        WebResponse webResponse = new WebResponse();
        webResponse.setResult(CommonErrorCode.BAD_REQUEST_400);
        webResponse.setData(e.getMessage());
        return webResponse;
    }

    @ResponseBody
    @ExceptionHandler(ExcelGenerateException.class)
    public WebResponse excelGenerateException(ExcelGenerateException e) {
        WebResponse webResponse = new WebResponse();
        webResponse.setResult(CommonErrorCode.BAD_REQUEST_400);
        webResponse.setData(e.getMessage());
        return webResponse;
    }

    @ResponseBody
    @ExceptionHandler({ClientAbortException.class})
    public WebResponse clientAbortException(HttpServletResponse response, ClientAbortException e) {
        return super.clientAbortException(response, e);
    }

    private void recordLog(HttpServletResponse response, Exception e) {
        try {
            if (MultipleTenantDataSourceSessionContext.getTenantId() != null && tenantErrorLogSaveService != null) {
                String message = e.getMessage();
                if (StringUtils.isBlank(message)) {
                    message = e.getClass().toString();
                }
                tenantErrorLogSaveService.recordLog(MultipleTenantDataSourceSessionContext.getTenantId(), message);
            }
        } catch (Exception e2) {
            log.error("exception record log error ", e2);
        }
    }
}
