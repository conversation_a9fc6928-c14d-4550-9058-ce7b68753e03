DROP TABLE IF EXISTS "public"."marketing_activity_purchase_record";
CREATE TABLE marketing_activity_purchase_record (
	id varchar(36) NOT NULL,
	marketing_activity_id varchar(36) NOT NULL,
	tenant_id varchar(36) NOT NULL,
    project_id varchar(36) NOT NULL,
    report_person_id varchar(36) NOT NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__marketing_activity_purchase_record___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."marketing_activity_purchase_record"."marketing_activity_id" IS '营销活动ID';
COMMENT ON COLUMN "public"."marketing_activity_purchase_record"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."marketing_activity_purchase_record"."project_id" IS '活动ID';
COMMENT ON COLUMN "public"."marketing_activity_purchase_record"."report_person_id" IS '报告人员ID';
COMMENT ON TABLE  "public"."marketing_activity_purchase_record" IS '营销活动购买记录表';


CREATE INDEX ___idx_marketing_activity_purchase_record_marketing_activity_id___ ON public.marketing_activity_purchase_record(marketing_activity_id);