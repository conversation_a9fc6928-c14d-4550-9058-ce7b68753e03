package com.knx.survey.dao.mapper.report.tencent;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.knx.common.dynamic.annotation.MultiTenant;
import com.knx.survey.model.report.prisma.tencent.PrismaOrgaztionOpenQuestionData;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【prisma_orgaztion_open_question_data(活动所有填答人开放题数据)】的数据库操作Mapper
* @createDate 2025-07-10 15:53:50
* @Entity com.knx.survey.model.report.prisma.tencent.PrismaOrgaztionOpenQuestionData
*/
@MultiTenant
public interface PrismaOrgaztionOpenQuestionDataMapper extends BaseMapper<PrismaOrgaztionOpenQuestionData> {

    /**
     * 按projectId删除所有开放题数据
     * @param projectId
     * @return 删除条数
     */
    @Delete("DELETE FROM prisma_orgaztion_open_question_data WHERE project_id = #{projectId}")
    int deleteByProjectId(@Param("projectId") String projectId);

    @Select({" SELECT id from prisma_orgaztion_open_question_data WHERE project_id =#{projectId} and is_deleted = FALSE "})
    List<String> listIdsByProjectId(@Param("projectId") String projectId);
}
