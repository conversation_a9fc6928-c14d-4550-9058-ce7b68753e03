package com.knx.survey.common.util.excelUtil.handler;

import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import org.apache.poi.ss.usermodel.Cell;

public class WidthCellWriteHandler extends AbstractColumnWidthStyleStrategy {

    private int columnIndex;

    private int width;

    public WidthCellWriteHandler(int columnIndex, int width) {
        this.columnIndex = columnIndex;
        this.width = width;
    }

    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, CellData cellData, Cell cell, Head head, int relativeRowIndex, boolean isHead) {
        if (cell.getColumnIndex() == columnIndex) {
            writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), width);
        }
    }
}
