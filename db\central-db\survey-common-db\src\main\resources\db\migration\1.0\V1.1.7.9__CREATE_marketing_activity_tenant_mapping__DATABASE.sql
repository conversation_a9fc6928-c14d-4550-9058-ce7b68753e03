DROP TABLE IF EXISTS marketing_activity_tenant_mapping;
CREATE TABLE marketing_activity_tenant_mapping
(
    "id"                              VARCHAR(36)  NOT NULL,
    "marketing_activity_id"              VARCHAR(36)  NOT NULL,
    "tenant_id"                       VARCHAR(36)  NOT NULL,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___marketing_activity_tenant_mapping___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."marketing_activity_tenant_mapping"."marketing_activity_id" IS '营销活动ID';
COMMENT ON COLUMN "public"."marketing_activity_tenant_mapping"."tenant_id" IS '租户ID';
COMMENT ON TABLE  "public"."marketing_activity_tenant_mapping" IS '营销活动租户关系表（指定租户）';


CREATE INDEX ___idx_marketing_activity_tenant_mapping_marketing_activity_id___ ON public.marketing_activity_tenant_mapping(marketing_activity_id);
CREATE INDEX ___idx_marketing_activity_tenant_mapping_tenant_id___ ON public.marketing_activity_tenant_mapping(tenant_id);