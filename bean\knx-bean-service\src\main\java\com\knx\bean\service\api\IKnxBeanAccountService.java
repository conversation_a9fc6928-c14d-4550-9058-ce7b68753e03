package com.knx.bean.service.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knx.bean.model.entity.KnxBeanAccount;

import java.math.BigDecimal;
import java.util.List;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-05-20 18:22:45
 */
public interface IKnxBeanAccountService extends IService<KnxBeanAccount> {


    KnxBeanAccount get(String tenantId);

    void defaultGiveKen(String tenantId, Integer balance);

    List<KnxBeanAccount> batchListByTenantId(List<String> tenantIds);

    BigDecimal getBalance(String tenantId);

    BigDecimal getMessageBalance(String tenantId);

}

