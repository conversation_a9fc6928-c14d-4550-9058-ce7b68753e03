DROP TABLE IF EXISTS "public"."order_history";
CREATE TABLE "public"."order_history" (
    "id" varchar(36) COLLATE "pg_catalog"."default" NOT NULL,
    "order_id" varchar(36) COLLATE "pg_catalog"."default" NOT NULL,
    "order_no" varchar(36) COLLATE "pg_catalog"."default" NOT NULL,
    "tenant_id" varchar(36) COLLATE "pg_catalog"."default" NOT NULL,
    "project_name" varchar(50) COLLATE "pg_catalog"."default",
    "sale_name" varchar(50) COLLATE "pg_catalog"."default",
    "sale_phone" varchar(20) COLLATE "pg_catalog"."default",
    "sale_team" varchar(20) COLLATE "pg_catalog"."default",
    "sale_city" varchar(50) COLLATE "pg_catalog"."default",
    "sale_channel" varchar(50) COLLATE "pg_catalog"."default",
    "oa_md_no" varchar(36) COLLATE "pg_catalog"."default",
    "sale_type" varchar(50) COLLATE "pg_catalog"."default",
    "valid_period_start" timestamp(3),
    "valid_period_end" timestamp(3),
    "project_scene_id" varchar(36) COLLATE "pg_catalog"."default",
    "deliver_type" varchar(50) COLLATE "pg_catalog"."default",
    "deliver_persons" varchar(36) COLLATE "pg_catalog"."default",
    "contract_no" varchar(36) COLLATE "pg_catalog"."default",
    "contract_amount_before_tax" numeric(10,2),
    "contract_amount_after_tax" numeric(10,2),
    "contract_file" varchar(36) COLLATE "pg_catalog"."default",
    "confirm_file" varchar(36) COLLATE "pg_catalog"."default",
    "milestone_id" varchar(36) COLLATE "pg_catalog"."default",
    "confirm_amount" numeric(10,2),
    "finance_confirm_file" varchar(36) COLLATE "pg_catalog"."default",
    "status" varchar(50) COLLATE "pg_catalog"."default",
    "operate" varchar(50) COLLATE "pg_catalog"."default",
    "create_time" timestamp(3),
    "update_time" timestamp(3),
    "last_update_by" varchar(36) COLLATE "pg_catalog"."default",
    "is_deleted" bool,
    "app_id" varchar(36) COLLATE "pg_catalog"."default",
    "create_by" varchar(36) COLLATE "pg_catalog"."default",
    "complete_time" timestamp(3)
)
;
COMMENT ON COLUMN "public"."order_history"."id" IS '主键';
COMMENT ON COLUMN "public"."order_history"."order_id" IS '订单id';
COMMENT ON COLUMN "public"."order_history"."order_no" IS '订单编号';
COMMENT ON COLUMN "public"."order_history"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."order_history"."project_name" IS '项目名称';
COMMENT ON COLUMN "public"."order_history"."sale_name" IS '销售人员姓名';
COMMENT ON COLUMN "public"."order_history"."sale_phone" IS '销售人员联系方式';
COMMENT ON COLUMN "public"."order_history"."sale_team" IS '销售人员所属师团';
COMMENT ON COLUMN "public"."order_history"."sale_city" IS '销售人员所属城市';
COMMENT ON COLUMN "public"."order_history"."sale_channel" IS '销售渠道';
COMMENT ON COLUMN "public"."order_history"."oa_md_no" IS 'OA/MD号';
COMMENT ON COLUMN "public"."order_history"."sale_type" IS '售卖方式';
COMMENT ON COLUMN "public"."order_history"."valid_period_start" IS '有效期开始时间';
COMMENT ON COLUMN "public"."order_history"."valid_period_end" IS '有效期结束时间';
COMMENT ON COLUMN "public"."order_history"."project_scene_id" IS '项目场景';
COMMENT ON COLUMN "public"."order_history"."deliver_type" IS '交付类型';
COMMENT ON COLUMN "public"."order_history"."deliver_persons" IS '交付人员';
COMMENT ON COLUMN "public"."order_history"."contract_no" IS '合同编号';
COMMENT ON COLUMN "public"."order_history"."contract_amount_before_tax" IS '税前合同金额';
COMMENT ON COLUMN "public"."order_history"."contract_amount_after_tax" IS '税后合同金额';
COMMENT ON COLUMN "public"."order_history"."contract_file" IS '合同文件';
COMMENT ON COLUMN "public"."order_history"."confirm_file" IS '确收文件';
COMMENT ON COLUMN "public"."order_history"."milestone_id" IS '里程碑ID';
COMMENT ON COLUMN "public"."order_history"."confirm_amount" IS '确收金额';
COMMENT ON COLUMN "public"."order_history"."finance_confirm_file" IS '财务确收';
COMMENT ON COLUMN "public"."order_history"."status" IS '订单状态';
COMMENT ON COLUMN "public"."order_history"."operate" IS '操作';
COMMENT ON COLUMN "public"."order_history"."complete_time" IS '结项时间';
COMMENT ON TABLE "public"."order_history" IS '订单修改历史表';

ALTER TABLE "public"."order_history" ADD CONSTRAINT "order_history_pkey" PRIMARY KEY ("id");
CREATE INDEX ___idx_order_history_order_id___ ON public.order_history(order_id);
