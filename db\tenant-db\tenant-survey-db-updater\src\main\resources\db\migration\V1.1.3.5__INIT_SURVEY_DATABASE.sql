/*prisma报告数据表*/
DROP TABLE IF EXISTS prisma_report_data;
CREATE TABLE prisma_report_data
(
    "id"                                     VARCHAR(36)  NOT NULL,
    "project_id"                             VARCHAR(36),
    "organization_ids"                       json,
    "demographic_contents"                   json,
    "norm_ids"                               json,
    "questionnaire_name"                     VARCHAR(255),
    "report_languages"                       json DEFAULT '["zh_CN"]',
    "is_all"                                 BOOL         DEFAULT false,
    "status"                                 VARCHAR(50) DEFAULT 'INIT',
    "create_by"                              VARCHAR(36),
    "create_time"                            TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                            TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                         VARCHAR(36),
    "is_deleted"                             BOOL         DEFAULT false,
    "app_id"                                 VARCHAR(36),
    CONSTRAINT  "___pk___prisma_report_data___" PRIMARY KEY ("id")
);
COMMENT ON TABLE "public"."prisma_report_data" IS 'prisma报告数据';
COMMENT ON COLUMN "public"."prisma_report_data"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."prisma_report_data"."organization_ids" IS '组织id';
COMMENT ON COLUMN "public"."prisma_report_data"."demographic_contents" IS '分析因子';
COMMENT ON COLUMN "public"."prisma_report_data"."norm_ids" IS '常模';
COMMENT ON COLUMN "public"."prisma_report_data"."questionnaire_name" IS '问卷名称';
COMMENT ON COLUMN "public"."prisma_report_data"."report_languages" IS '报告语言';
COMMENT ON COLUMN "public"."prisma_report_data"."is_all" IS '是否全部（默认报告）';
COMMENT ON COLUMN "public"."prisma_report_data"."status" IS '状态';


/*prisma报告数据-赞成度*/
DROP TABLE IF EXISTS prisma_report_data_approval;
CREATE TABLE prisma_report_data_approval
(
    "id"                                     VARCHAR(36)  NOT NULL,
    "prisma_report_data_id"                  VARCHAR(36),
    "organization_id"                        VARCHAR(36),
    "parent_organization_id"                 VARCHAR(36),
    "demographic_id"                         VARCHAR(36),
    "question_id"                            VARCHAR(36),
    "question_code"                          VARCHAR(36),
    "question_name"                          json,
    "dimension_code"                         VARCHAR(36),
    "total_count"                            int DEFAULT 0,
    "valid_count"                            int DEFAULT 0,
    "approval"                               numeric(53,2),
    "part_approval"                          numeric(53,2),
    "neutral"                                numeric(53,2),
	"disapproval"                            numeric(53,2),
    "option_percents"                        json DEFAULT '[]',
    "create_by"                              VARCHAR(36),
    "create_time"                            TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                            TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                         VARCHAR(36),
    "is_deleted"                             BOOL         DEFAULT false,
    "app_id"                                 VARCHAR(36),
    CONSTRAINT  "___pk___prisma_report_data_approval___" PRIMARY KEY ("id")
);
COMMENT ON TABLE "public"."prisma_report_data_approval" IS 'prisma报告数据-赞成度';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."prisma_report_data_id" IS 'prisma报告数据id';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."organization_id" IS '组织id';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."parent_organization_id" IS '父组织id';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."demographic_id" IS '分析因子id';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."question_id" IS '题目id';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."question_code" IS '题目编码';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."question_name" IS '题目名称';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."dimension_code" IS '维度编码';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."total_count" IS '总人数';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."valid_count" IS '有效人数';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."approval" IS '赞成度';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."part_approval" IS '部份赞成度（不过滤组织或分析因子）';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."neutral" IS '中立';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."disapproval" IS '不赞成';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."option_percents" IS '选项占比';


/*prisma报告数据-相关系数*/
DROP TABLE IF EXISTS prisma_report_data_correlation_coefficient;
CREATE TABLE prisma_report_data_correlation_coefficient
(
    "id"                                     VARCHAR(36)  NOT NULL,
    "prisma_report_data_id"                  VARCHAR(36),
    "question_id"                            VARCHAR(36),
    "question_code"                          VARCHAR(36),
    "question_name"                          json,
    "dimension_code"                         VARCHAR(36),
    "dedication_code"                        VARCHAR(36),
    "driving_factors_code"                   VARCHAR(36),
    "correlation_coefficient"                numeric(53,2),
    "create_by"                              VARCHAR(36),
    "create_time"                            TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                            TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                         VARCHAR(36),
    "is_deleted"                             BOOL         DEFAULT false,
    "app_id"                                 VARCHAR(36),
    CONSTRAINT  "___pk___prisma_report_data_correlation_coefficient___" PRIMARY KEY ("id")
);
COMMENT ON TABLE "public"."prisma_report_data_correlation_coefficient" IS 'prisma报告数据-相关系数';
COMMENT ON COLUMN "public"."prisma_report_data_correlation_coefficient"."prisma_report_data_id" IS 'prisma报告数据id';
COMMENT ON COLUMN "public"."prisma_report_data_correlation_coefficient"."question_id" IS '题目id';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."question_code" IS '题目编码';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."question_name" IS '题目名称';
COMMENT ON COLUMN "public"."prisma_report_data_approval"."dimension_code" IS '维度编码';
COMMENT ON COLUMN "public"."prisma_report_data_correlation_coefficient"."dedication_code" IS '敬业度编码';
COMMENT ON COLUMN "public"."prisma_report_data_correlation_coefficient"."driving_factors_code" IS '驱动因素编码';
COMMENT ON COLUMN "public"."prisma_report_data_correlation_coefficient"."correlation_coefficient" IS '相关系数';