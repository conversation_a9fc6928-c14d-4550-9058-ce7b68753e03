<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.knx</groupId>
        <artifactId>knx-survey-service</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>./../../pom.xml</relativePath>
    </parent>

    <artifactId>knx-sag-bean-service-client</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>

    <dependencies>

        <dependency>
            <groupId>com.knx</groupId>
            <artifactId>knx-base</artifactId>
            <version>${center.version}</version>
        </dependency>
        <dependency>
            <groupId>com.knx</groupId>
            <artifactId>survey-model</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

    </dependencies>
</project>
