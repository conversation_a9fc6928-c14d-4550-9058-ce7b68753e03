/* CSI */
alter table "public"."survey_standard_sag_norm_transform" add code varchar(36) ;
COMMENT ON COLUMN "public"."survey_standard_sag_norm_transform"."code" IS '报告编码';

alter table "public"."survey_standard_sag_norm_transform"  alter column id type varchar (36);

/* CA */
alter table "public"."survey_standard_sag_report_dimension" add reflective_question json ;
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."reflective_question" IS '反思问题';

alter table "public"."survey_standard_sag_report_dimension" add recommended_books json ;
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."recommended_books" IS '推荐书籍';

alter table "public"."survey_standard_sag_report_dimension" add interview_questions json ;
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."interview_questions" IS '面试问题';

alter table "public"."survey_standard_sag_report_dimension" add task_training json ;
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."task_training" IS '任务训练';

alter table "public"."survey_standard_sag_report_dimension" add recommended_courses json ;
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."recommended_courses" IS '推荐课程';


/* CSI */
alter table "public"."survey_standard_sag_report_template" add report_style varchar(36) ;
COMMENT ON COLUMN "public"."survey_standard_sag_report_template"."report_style" IS '报告样式';

alter table "public"."survey_standard_sag_report_template" add config_type varchar(36) ;
COMMENT ON COLUMN "public"."survey_standard_sag_report_template"."config_type" IS '配置类型';