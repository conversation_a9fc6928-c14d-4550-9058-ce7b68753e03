package com.knx.survey.common.util.excelUtil.listener;


import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSON;
import com.knx.survey.common.util.ToolUtil;
import com.knx.survey.common.util.excelUtil.pojo.ErrRows;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/6/11 14:09
 */
@Slf4j
public abstract class BaseExcelListener<Model> extends AnalysisEventListener<Model> {
    /**
     * 自定义用于暂时存储data。
     * 可以通过实例获取该值
     * 可以指定AnalysisEventListener的泛型来确定List的存储类型
     */
    @Getter
    private List<Model> data = new ArrayList<>();

    @Getter
    private List<String> sheetNames = new ArrayList<>();

    @Getter
    private Map<Integer, String> headMap;

    @Getter
    private Map<Integer, List<Model>> multiDataMap = new HashMap<>();

    @Getter
    @Setter
    private Boolean isMultiSheetData = false;

    /**
     * 如果是入库操作,可使用默认的3000条,然后清理list,方便内存回收
     */
    private int batchCount = 3000;
    /**
     * <p>读取时抛出异常是否继续读取.</p>
     * <p>true:跳过继续读取 , false:停止读取 , 默认true .</p>
     */
    private boolean continueAfterThrowing = true;
    /**
     * 读取过程中发生异常被跳过的行数记录
     * String 为 sheetNo
     * List<Integer> 为 错误的行数列表
     */
    private Map<String, List<Integer>> errRowsMap = new HashMap<>();
    /**
     * 错误行号的pojo形式
     */
    private List<ErrRows> errRowsList = new ArrayList<>();

    /**
     * 是否跳过空行，默认跳过
     */
    private boolean isSkipEmptyRow = true;

    /**
     * @param batchCount see batchCount
     * @return this
     */
    public BaseExcelListener batchCount(int batchCount) {
        this.batchCount = batchCount;
        return this;
    }

    /**
     * 设置抛出解析过程中抛出异常后是否跳过继续读取下一行
     *
     * @param continueAfterThrowing 解析过程中抛出异常后是否跳过继续读取下一行
     * @return this
     */
    public BaseExcelListener continueAfterThrowing(boolean continueAfterThrowing) {
        this.continueAfterThrowing = continueAfterThrowing;
        return this;
    }

    /**
     * 获取错误的行号,以pojo的形式返回
     *
     * @return 错误的行号
     */
    public List<ErrRows> getErrRowsList() {
        errRowsMap.forEach((sheetNo, rows) -> errRowsList.add(new ErrRows().setSheetNo(sheetNo).setErrRows(rows)));
        return errRowsList;
    }

    /**
     * 每解析一行会回调invoke()方法。
     * 如果当前行无数据,该方法不会执行,
     * 也就是说如果导入的的excel表无数据,该方法不会执行,
     * 不需要对上传的Excel表进行数据非空判断
     *
     * @param object  当前读取到的行数据对应的java模型对象
     * @param context 定义了获取读取excel相关属性的方法
     */
    @Override
    public void invoke(Model object, AnalysisContext context) {
        log.info("解析到一条数据:{}", object);
        //如果开启就空行跳过
        if (BooleanUtils.isTrue(isSkipEmptyRow)) {
            if (ToolUtil.isObjectEmpty(object)) {
                return;
            }
        }

        String sheetName = context.readSheetHolder().getSheetName();
        Integer sheetNo = context.readSheetHolder().getSheetNo();
        if (!validateBeforeAddData(object)) {
            throw new ExcelAnalysisException("数据校验不合法!");
        }

        // 数据存储到list，供批量处理，或后续自己业务逻辑处理。
        data.add(object);

        //如果continueAfterThrowing 为false 时保证数据插入的原子性
//        if (data.size() >= batchCount && !continueAfterThrowing) {
//            doService();
//            data.clear();
        //      }
        if (BooleanUtils.isTrue(isMultiSheetData) && sheetNo != null) {
            if (!multiDataMap.containsKey(sheetNo)) {
                multiDataMap.put(sheetNo, new ArrayList<>());
            }
            multiDataMap.get(sheetNo).add(object);
        }
    }

    /**
     * 该方法用于对读取excel过程中对每一行的数据进行校验操作,
     * 如果不需要对每行数据进行校验,则直接返回true即可.
     *
     * @param object 读取到的数据对象
     * @return 校验是否通过 true:通过 ; false:不通过
     */
    public abstract boolean validateBeforeAddData(Model object);

    /**
     * 对暂存数据的业务逻辑方法 .
     * 相关逻辑可以在该方法体内编写, 例如入库.
     */
//    {
//        log.info("模拟写入数据库");
//        log.info("/*------- {} -------*/", JSON.toJSONString(data));
//        data.clear();
//    }

    /**
     * 解析监听器
     * 每个sheet解析结束会执行该方法
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("/*------- 当前sheet读取完毕,sheetNo : {} , 读取错误的行号列表 : {} -------*/",
                getCurrentSheetNo(context), JSON.toJSONString(errRowsMap));
        String sheetName = context.readSheetHolder().getSheetName();
        sheetNames.add(sheetName);
        getSheetName(sheetName);
        //data.clear();//解析结束销毁不用的资源
    }

    /**
     * 获取sheet的名字
     */

    public String getSheetName(String sheetName) {
        return sheetName;
    }


    /**
     * 在转换异常 获取其他异常下会调用本接口。抛出异常则停止读取。如果这里不抛出异常则继续读取下一行。
     * 如果不重写该方法,默认抛出异常,停止读取
     *
     * @param exception exception
     * @param context   context
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        // 如果continueAfterThrowing为false,则直接将异常抛出
        if (!continueAfterThrowing) {
            throw exception;
        }

        Integer sheetNo = getCurrentSheetNo(context);
        Integer rowIndex = context.readRowHolder().getRowIndex();
        log.error("/*------- 读取发生错误! 错误SheetNo:{},错误行号:{} -------*/ ", sheetNo, rowIndex, exception);

        List<Integer> errRowNumList = errRowsMap.get(String.valueOf(sheetNo));
        if (Objects.isNull(errRowNumList)) {
            errRowNumList = new ArrayList<>();
            errRowNumList.add(rowIndex);
            errRowsMap.put(String.valueOf(sheetNo), errRowNumList);
        } else {
            errRowNumList.add(rowIndex);
        }
    }

    /**
     * 获取当前读取的sheet no
     *
     * @param context 定义了获取读取excel相关属性的方法
     * @return current sheet no
     */
    private Integer getCurrentSheetNo(AnalysisContext context) {
        return context.readSheetHolder().getSheetNo();
    }


    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // super.invokeHeadMap(headMap, context);
        this.headMap = headMap;
    }

    /**
     * 设置是否跳过空列
     *
     * @param skipEmptyRow
     */
    public void setSkipEmptyRow(boolean skipEmptyRow) {
        isSkipEmptyRow = skipEmptyRow;
    }
}
