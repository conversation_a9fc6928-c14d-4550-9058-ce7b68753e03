/*人员填答有效表*/
DROP TABLE IF EXISTS survey_person_valid;
CREATE TABLE survey_person_valid
(
    "id"             VARCHAR(36)  NOT NULL,
    "project_id"      VARCHAR(36)  NOT NULL,
    "person_id"      VARCHAR(36)  NOT NULL,
    "is_valid"       BOOL         DEFAULT true,
    "create_by"      VARCHAR(36),
    "create_time"    TIMESTAMP(3) DEFAULT NOW(),
    "update_time"    TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by" VARCHAR(36),
    "is_deleted"     BOOL         DEFAULT false,
    "app_id"         VARCHAR(36),
    CONSTRAINT  "___pk___survey_person_valid___" PRIMARY KEY ("id")
);


COMMENT ON TABLE "public"."survey_person_valid" IS '人员填答有效表';
COMMENT ON COLUMN "public"."survey_person_valid"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_person_valid"."person_id" IS '人员id';
COMMENT ON COLUMN "public"."survey_person_valid"."is_valid" IS '是否有效';
