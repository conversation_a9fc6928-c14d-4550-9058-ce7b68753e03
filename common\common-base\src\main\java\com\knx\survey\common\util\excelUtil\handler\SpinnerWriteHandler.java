package com.knx.survey.common.util.excelUtil.handler;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.knx.survey.common.util.excelUtil.anno.EnumFormat;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;

import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/8/17 15:38
 */
public class SpinnerWriteHandler implements SheetWriteHandler {


    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

    }

    @Override
    @SneakyThrows
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

        Class clazz = writeSheetHolder.getWriteSheet().getClazz();
        //CellStyle cellStyleAt = writeWorkbookHolder.getWorkbook().getCellStyleAt(0);
        Field[] declaredFields = clazz.getDeclaredFields();
        Map<Integer, String[]> mapDropDown = new HashMap<>();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            if (declaredField.isAnnotationPresent(ExcelProperty.class) && declaredField.isAnnotationPresent(EnumFormat.class)) {
                ExcelProperty excelProperty = declaredField.getAnnotation(ExcelProperty.class);
                EnumFormat enumFormat = declaredField.getAnnotation(EnumFormat.class);
                boolean isShow = enumFormat.isShow();
                if (!isShow) continue;
                Class enumClazz = enumFormat.value();
                String enumFieldName = enumFormat.name();
                String[] enumValues = enumFormat.values();
                Object[] enumConstants = enumClazz.getEnumConstants();
                String[] enumCells = new String[enumConstants.length];
                if (enumValues.length == 0) {
                    for (int i = 0; i < enumConstants.length; i++) {
                        if (enumConstants[i] instanceof Enum) {
                            Field field = enumConstants[i].getClass().getDeclaredField(enumFieldName);
                            field.setAccessible(true);
                            Object object = field.get(enumConstants[i]);
                            if (object instanceof String) {
                                String s = (String) object;
                                enumCells[i] = s;
                            }

                        }
                    }
                } else {
                    enumCells = enumValues;
                }
                List<String> list = Arrays.asList(enumCells);
                List<String> stringList = new ArrayList<>();
                for (String s : list) {
                    if (!Objects.equals(s, "NULL")) {
                        stringList.add(s);
                    }
                }
                enumCells = new String[stringList.size()];
                enumCells = stringList.toArray(enumCells);
                int index = excelProperty.index();
                mapDropDown.put(index, enumCells);

            }


        }
        Sheet sheet = writeSheetHolder.getSheet();
        DataValidationHelper helper = sheet.getDataValidationHelper();//设置下拉框
        for (Integer key : mapDropDown.keySet()) {
            CellRangeAddressList addressList = new CellRangeAddressList(1, 10000, key, key);
            DataValidationConstraint explicitListConstraint = helper.createExplicitListConstraint(mapDropDown.get(key));
            DataValidation dataValidation = helper.createValidation(explicitListConstraint, addressList);
            if (dataValidation instanceof XSSFDataValidation) {
                dataValidation.setSuppressDropDownArrow(true);
                dataValidation.setShowErrorBox(true);
            } else {
                dataValidation.setSuppressDropDownArrow(false);
            }
            sheet.addValidationData(dataValidation);
        }

    }
}
