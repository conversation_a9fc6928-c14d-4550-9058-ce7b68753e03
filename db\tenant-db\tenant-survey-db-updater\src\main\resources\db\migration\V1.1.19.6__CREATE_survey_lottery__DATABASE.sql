DROP TABLE IF EXISTS survey_lottery;
CREATE TABLE survey_lottery
(
    "id"                              VARCHAR(36)  NOT NULL,
    "project_id"                      VARCHAR(36)  NOT NULL,
    "partake_button_name"             json NOT NULL,
    "subject_name"                    json DEFAULT NULL,
    "subject_pic"                     VARCHAR(50) DEFAULT NULL,
    "subject_background_type"         VARCHAR(50) NOT NULL DEFAULT 'CUSTOM',
    "subject_background_color"        VARCHAR(50) DEFAULT NULL,
    "subject_background_pc_pic"       VARCHAR(50) DEFAULT NULL,
    "subject_background_mobile_pic"   VARCHAR(50) DEFAULT NULL,
    "draw_button_name"                json NOT NULL,
    "draw_button_pic"                 VARCHAR(50) DEFAULT NULL,
    "start_time"                      TIMESTAMP(3) DEFAULT NOW(),
    "end_time"                        TIMESTAMP(3) DEFAULT NOW(),
    "draw_rule_description"           json NOT NULL,
    "cash_rule_description"           json NOT NULL,
    "draw_number"                     INT4 NOT NULL DEFAULT 1,
    "partake_number"                  INT4 DEFAULT NULL,
    "current_partake_number"          INT4 DEFAULT 0,
    "box_background_color"            VARCHAR(50) DEFAULT NULL,
    "box_border_color"                VARCHAR(50) DEFAULT NULL,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___survey_lottery___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."survey_lottery" IS '调研抽奖表';
COMMENT ON COLUMN "public"."survey_lottery"."project_id" IS '活动ID';
COMMENT ON COLUMN "public"."survey_lottery"."partake_button_name" IS '参与按钮名称';
COMMENT ON COLUMN "public"."survey_lottery"."subject_name" IS '主题名称';
COMMENT ON COLUMN "public"."survey_lottery"."subject_pic" IS '主题图片';
COMMENT ON COLUMN "public"."survey_lottery"."subject_background_type" IS '主题背景类型';
COMMENT ON COLUMN "public"."survey_lottery"."subject_background_color" IS '主题背景颜色';
COMMENT ON COLUMN "public"."survey_lottery"."subject_background_pc_pic" IS '主题背景PC图片';
COMMENT ON COLUMN "public"."survey_lottery"."subject_background_mobile_pic" IS '主题背景手机图片';
COMMENT ON COLUMN "public"."survey_lottery"."draw_button_name" IS '抽奖按钮名称';
COMMENT ON COLUMN "public"."survey_lottery"."draw_button_pic" IS '抽奖按钮图片';
COMMENT ON COLUMN "public"."survey_lottery"."start_time" IS '开始时间';
COMMENT ON COLUMN "public"."survey_lottery"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."survey_lottery"."draw_rule_description" IS '抽奖规则';
COMMENT ON COLUMN "public"."survey_lottery"."cash_rule_description" IS '兑奖规则';
COMMENT ON COLUMN "public"."survey_lottery"."draw_number" IS '抽奖次数（每个人）';
COMMENT ON COLUMN "public"."survey_lottery"."partake_number" IS '参与人数';
COMMENT ON COLUMN "public"."survey_lottery"."partake_number" IS '当前参与人数';
COMMENT ON COLUMN "public"."survey_lottery"."box_background_color" IS '九宫格背景颜色';
COMMENT ON COLUMN "public"."survey_lottery"."box_border_color" IS '九宫格边框颜色';

CREATE INDEX ___idx_survey_lottery_project_id___ ON public.survey_lottery(project_id);
