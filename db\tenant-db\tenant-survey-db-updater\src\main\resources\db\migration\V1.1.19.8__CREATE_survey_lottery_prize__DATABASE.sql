DROP TABLE IF EXISTS survey_lottery_prize;
CREATE TABLE survey_lottery_prize
(
    "id"                              VARCHAR(36)  NOT NULL,
    "lottery_id"                      VARCHAR(36)  NOT NULL,
    "category_id"                     VARCHAR(36)  NOT NULL,
    "prize_name"                      json NOT NULL,
    "prize_pic"                       VARCHAR(50) DEFAULT NULL,
    "prize_icon"                      VARCHAR(50) DEFAULT NULL,
    "prize_icon_pic"                  VARCHAR(50) DEFAULT NULL,
    "probability"                    decimal DEFAULT NULL,
    "total_number"                    INT4 DEFAULT NULL,
    "current_number"                  INT4 DEFAULT 0,
    "reminder"                        json NOT NULL,
    "is_partake_prize"                BOOL DEFAULT false,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___survey_lottery_prize___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."survey_lottery_prize" IS '调研抽奖奖品表';
COMMENT ON COLUMN "public"."survey_lottery_prize"."lottery_id" IS '抽奖ID';
COMMENT ON COLUMN "public"."survey_lottery_prize"."category_id" IS '奖品分类ID';
COMMENT ON COLUMN "public"."survey_lottery_prize"."prize_name" IS '奖品名称';
COMMENT ON COLUMN "public"."survey_lottery_prize"."prize_pic" IS '奖品图片';
COMMENT ON COLUMN "public"."survey_lottery_prize"."prize_icon" IS '奖品图标';
COMMENT ON COLUMN "public"."survey_lottery_prize"."prize_icon_pic" IS '奖品图标图片';
COMMENT ON COLUMN "public"."survey_lottery_prize"."probability" IS '奖品中奖概率';
COMMENT ON COLUMN "public"."survey_lottery_prize"."total_number" IS '奖品总数量';
COMMENT ON COLUMN "public"."survey_lottery_prize"."current_number" IS '奖品当前数量';
COMMENT ON COLUMN "public"."survey_lottery_prize"."reminder" IS '提示语';
COMMENT ON COLUMN "public"."survey_lottery_prize"."is_partake_prize" IS '是否参与奖';


CREATE INDEX ___idx_survey_lottery_prize_lottery_id___ ON public.survey_lottery_prize(lottery_id);
CREATE INDEX ___idx_survey_lottery_prize_category_id___ ON public.survey_lottery_prize(category_id);
