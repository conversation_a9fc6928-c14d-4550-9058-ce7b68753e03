package com.knx.survey.common.util;

import com.knx.survey.model.I18NString;
import com.knx.survey.common.base.field.SurveyField;
import org.apache.commons.lang3.StringUtils;

import java.security.MessageDigest;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * User: 林超
 * Date: 2020/3/25
 * Time: 10:10 AM
 */
public class ServiceUtil {
    public static final String regex = "(http:|https:)//[^[A-Za-z0-9\\._\\?%&+\\-=/#]]*";

    public static final String questionRegex = "\\{\\{.[^\\{|\\}]*\\}\\}";

    private static Random random = new Random();

    /**
     * 得到32位的MD5
     *
     * @param source
     * @return
     */
    public static String getMD32(String source) {
        StringBuilder stringBuilder = new StringBuilder(32);
        try {
            MessageDigest md = MessageDigest.getInstance(SurveyField.MD5);
            byte[] array = md.digest(source.getBytes(SurveyField.UTF8));
            for (int i = 0; i < array.length; i++) {
                stringBuilder.append(Integer.toHexString((array[i] & 0xFF) | 0x100).substring(1, 3));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        String md5String = stringBuilder.toString();

        return md5String;
    }

    /**
     * 生成随机的smsCode
     *
     * @return
     */
    public static String getSMSCode() {
        String smsCode = (Math.random() * 9000 + 1000) + "";
        smsCode = smsCode.split("\\.")[0];
        return smsCode;
    }

    public static String getUrl(String content) {
        Pattern pattern = Pattern.compile(regex);
        if (content == null) {
            return StringUtils.EMPTY;
        }
        Matcher matcher = pattern.matcher(content);
        if (matcher.find()) {
            String group = matcher.group();
            return group;
        }
        return StringUtils.EMPTY;
    }


    public static List<String> getRandomStrings(String[] split, Integer size) {
        if (split == null || size == null) {
            return new ArrayList<>();
        }
        List<String> list0 = Arrays.stream(split).filter(data -> StringUtils.isNotBlank(data)).collect(Collectors.toList());
        int length = list0.size();
        if (length <= 0) {
            return new ArrayList<>();
        }
        Set<Integer> set = new HashSet<>();
        List<String> list = new ArrayList<>();
        for (; ; ) {
            int num = 0;
            try {
                num = random.nextInt(length);
            } catch (Exception e) {
                e.printStackTrace();
            }

            if (!set.contains(num)) {
                set.add(num);
                String recommendedCourse = list0.get(num);
                list.add(recommendedCourse);
            }
            if (set.size() >= size || set.size() == length) {
                break;
            }
        }
        return list;
    }

    public static List<String> getRandomStrings(String source) {
        if (source == null) {
            return Collections.emptyList();
        }
        return getRandomStrings(source.split(StringUtils.LF), 3);
    }

    public static List<I18NString> genRandomI18nStrings(I18NString source) {
        return genRandomI18nStrings(source, 3);
    }

    public static List<I18NString> genRandomI18nStrings(I18NString source, int num) {

        if (source == null) {
            return Collections.emptyList();
        }
        String[] recommendedBooksArrayZh = new String[0];
        if (source.getZh_CN() != null) {
            recommendedBooksArrayZh = source.getZh_CN().split(StringUtils.LF);
        }
        String[] recommendedBooksArrayEN = new String[0];
        if (source.getEn_US() != null) {
            recommendedBooksArrayEN = source.getEn_US().split(StringUtils.LF);
        }

        List<String> recommendedBooksListZh = ServiceUtil.getRandomStrings(recommendedBooksArrayZh, 3);
        List<String> recommendedBooksListEn = ServiceUtil.getRandomStrings(recommendedBooksArrayEN, 3);
        int size = Math.max(recommendedBooksListZh.size(), recommendedBooksListEn.size());

        List<I18NString> i18s = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            I18NString i18 = new I18NString();
            i18s.add(i18);
            i18.setZh_CN(recommendedBooksListZh.size() >= (i + 1) ? recommendedBooksListZh.get(i) : null);
            i18.setEn_US(recommendedBooksListEn.size() >= (i + 1) ? recommendedBooksListEn.get(i) : null);
        }
        return i18s;
    }


    public static void removeAllNull(List list) {
        if (list.remove(null)) {
            removeAllNull(list);
        } else {
            return;
        }
    }


    private static Set<String> findReplaceContent(String content) {
        Set<String> placeholder = new HashSet<>();
        if (StringUtils.isEmpty(content)) {
            return new HashSet<>();
        }
        Pattern r = Pattern.compile(questionRegex);

        Matcher matcher = r.matcher(content);

        while (matcher.find()) {
            String group = matcher.group();
            placeholder.add(group);
        }
        return placeholder;
    }

    public static String replaceQuestionRegex(String content) {
        Set<String> replaceContents = findReplaceContent(content);
        for (String replaceContent : replaceContents) {
            String[] split = replaceContent.split(":");
            if (split.length > 0) {
                String substring = split[0].substring(2);
                content = StringUtils.replace(content, replaceContent, substring);

            }
        }
        return content;
    }


    public static String replaceQuestionRegex(String content, String replace) {
        Set<String> replaceContents = findReplaceContent(content);
        for (String replaceContent : replaceContents) {
            String[] splitGroup = replaceContent.split(":");
            String[] splitReplace = replace.split(":");
            if (splitGroup.length > 1 && splitReplace.length > 1) {
                if (Objects.equals(splitGroup[1], splitReplace[1])) {
                    content = StringUtils.replace(content, replaceContent, replace);
                }
            }
        }

        return content;
    }

    /**
     * 去除html代码中含有的标签
     */
    public static String delHtmlTags(String htmlStr) {
        if (StringUtils.isEmpty(htmlStr)) {
            return htmlStr;
        }
        //定义script的正则表达式，去除js可以防止注入
        String scriptRegex = "<script[^>]*?>[\\s\\S]*?<\\/script>";
        //定义style的正则表达式，去除style样式，防止css代码过多时只截取到css样式代码
        String styleRegex = "<style[^>]*?>[\\s\\S]*?<\\/style>";
        //定义HTML标签的正则表达式，去除标签，只提取文字内容
        String htmlRegex = "<[^>]+>";
//        //定义空格,回车,换行符,制表符
//        String spaceRegex = "\\s*|\t|\r|\n";
        String spaceRegex = "\\&nbsp;";

        // 过滤script标签
        htmlStr = htmlStr.replaceAll(scriptRegex, "");
        // 过滤style标签
        htmlStr = htmlStr.replaceAll(styleRegex, "");
        // 过滤html标签
        htmlStr = htmlStr.replaceAll(htmlRegex, "");
        // 过滤空格等
        htmlStr = htmlStr.replaceAll(spaceRegex, "");
        return htmlStr.trim(); // 返回文本字符串
    }

    /**
     * 获取HTML代码里的内容
     */
    public static String getTextFromHtml(String htmlStr) {
        //去除html标签
        htmlStr = delHtmlTags(htmlStr);
//        //去除空格" "
//        htmlStr = htmlStr.replaceAll(" ","");
        return htmlStr;
    }


    public static void main(String[] args) {

        String[] strings = {""};

        List<String> randomStrings = getRandomStrings(strings, 3);
        System.out.println(randomStrings);
    }
}
