package com.knx.survey.common.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;


@Component
public class ResourceUtil {
    @Autowired
    private ResourceLoader resourceLoader;

    /**
     * 根据文件名字获取路径（不支持jar）
     *
     * @param fileNameAndPath
     * @return
     */
    public String getFilePath(String fileNameAndPath) throws IOException {
        File file = resourceLoader.getResource("file:" + fileNameAndPath).getFile();
        if (!file.exists()) {
            file = resourceLoader.getResource("classpath:" + fileNameAndPath).getFile();
        }
        return file.getAbsolutePath();
    }
}

