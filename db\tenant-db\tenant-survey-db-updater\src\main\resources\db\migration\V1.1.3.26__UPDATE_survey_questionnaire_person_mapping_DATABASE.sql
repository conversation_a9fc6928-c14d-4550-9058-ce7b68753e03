ALTER TABLE survey_questionnaire_person_mapping DROP CONSTRAINT ___pk___survey_questionnaire_person_mapping___;
ALTER TABLE survey_questionnaire_person_mapping ADD COLUMN id SERIAL PRIMARY KEY;
ALTER TABLE survey_questionnaire_person_mapping ALTER COLUMN id TYPE varchar(36) USING id::varchar;
ALTER TABLE survey_questionnaire_person_mapping ALTER COLUMN id DROP DEFAULT;
CREATE INDEX ___idx_survey_questionnaire_person_mapping_questionnaire_id_person_id___ ON survey_questionnaire_person_mapping(questionnaire_id, person_id);

ALTER TABLE survey_questionnaire_person_mapping ADD "create_by" varchar(36) NULL;
ALTER TABLE survey_questionnaire_person_mapping ADD "create_time" TIMESTAMP(3) DEFAULT NOW();
ALTER TABLE survey_questionnaire_person_mapping ADD "update_time" TIMESTAMP(3) DEFAULT NOW();
ALTER TABLE survey_questionnaire_person_mapping ADD "last_update_by" varchar(36) NULL;
ALTER TABLE survey_questionnaire_person_mapping ADD is_deleted bool NULL DEFAULT false;
ALTER TABLE survey_questionnaire_person_mapping ADD "app_id" varchar(36) NULL;