DROP TABLE IF EXISTS "public"."survey_standard_sag_report_template_job";
CREATE TABLE survey_standard_sag_report_template_job (
	id varchar(36) NOT NULL,
	report_template_id varchar(36) NULL,
	name varchar(50) NULL,
	sort int4 default 999,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__survey_standard_sag_report_template_job___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."survey_standard_sag_report_template_job"."report_template_id" IS '报告模板id';
COMMENT ON COLUMN "public"."survey_standard_sag_report_template_job"."name" IS '岗位名称';
COMMENT ON COLUMN "public"."survey_standard_sag_report_template_job"."sort" IS '排序号';
COMMENT ON TABLE  "public"."survey_standard_sag_report_template_job" IS '标准报告岗位表';
