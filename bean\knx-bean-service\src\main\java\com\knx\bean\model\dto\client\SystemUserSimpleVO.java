package com.knx.bean.model.dto.client;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 系统用户信息
 */
@Data
@ApiModel("SystemUserSimpleVO")
public class SystemUserSimpleVO {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String id;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", example = "lisa")
    private String username;

    /**
     * 真实姓名
     */
    @ApiModelProperty(value = "真实姓名", example = "lisa")
    private String realName;

    /**
     * email
     */
    @ApiModelProperty(value = "email", example = "<EMAIL>")
    private String email;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号", example = "134567890986")
    private String mobile;
}
