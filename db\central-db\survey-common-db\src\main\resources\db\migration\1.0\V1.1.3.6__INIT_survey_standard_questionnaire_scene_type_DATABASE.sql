DROP TABLE IF EXISTS "public"."survey_standard_questionnaire_scene_type_questionnaire_mapping";
CREATE TABLE "public"."survey_standard_questionnaire_scene_type_questionnaire_mapping" (
	id varchar(36) NOT NULL,
    scene_id varchar(36) NOT NULL,
	questionnaire_id varchar(36) NOT NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___survey_standard_questionnaire_scene_type_questionnaire_mapping___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."survey_standard_questionnaire_scene_type_questionnaire_mapping"."scene_id" IS '场景id';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_scene_type_questionnaire_mapping"."questionnaire_id" IS '问卷id';

alter table survey_standard_questionnaire drop column if exists scene_ids;