package com.knx.bean.model.dto.client;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.knx.bean.model.dto.client.enums.SysUserTypeEnum;
import com.knx.common.base.dao.mybatis.config.LogicalDeleteAddSuffix;
import com.knx.common.base.enums.UserCreateSourceEnum;
import com.knx.common.base.enums.UserGenderEnum;
import com.knx.common.base.enums.UserStatusEnum;
import com.knx.common.base.model.BaseTenantModel;
import com.knx.common.security.SessionUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR> Zhang
 * @Package com.knx.uas.dao.model
 * @date 2020/6/4 15:16
 */
@Data
@ApiModel("用户信息")
@TableName("sys_user")
@FieldNameConstants
public class UserDTO extends BaseTenantModel {

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", example = "张三", required = true)
    @NotEmpty(message = "用户名不能为空")
    @LogicalDeleteAddSuffix
    private String username;
    /**
     * 密码
     */
    @ApiModelProperty("密码")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)//密码属性只支持写
    private String password;
    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String realName;
    /**
     * 头像
     */
    @ApiModelProperty(value = "头像", example = "http://10.55.8.63/center/assets/images/header-man.png")
    private String headUrl;
    /**
     * 性别   0：男   1：女    2：保密
     */
    @ApiModelProperty(value = "性别", example = "WOMAN")
    private UserGenderEnum gender;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱", example = "<EMAIL>", required = true)
    @NotEmpty(message = "邮箱不能为空")
    @LogicalDeleteAddSuffix
    private String email;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "联系方式", example = "15117200020")
    @LogicalDeleteAddSuffix
    private String mobile;
    /**
     * 部门ID,  这个作为预留
     */
    @ApiModelProperty(value = "部门ID", example = "1324255348076441602")
    private String deptId;
    /**
     * 状态  0：停用   1：正常 默认1
     */
    @ApiModelProperty(value = "状态", example = "ENABLE")
    private UserStatusEnum status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", example = "测试用户")
    private String remark;
    /**
     * 职位编号, 关联数据字典
     */
    @ApiModelProperty(value = "职位编号", example = "1309035807469735938")
    private String position;

    /**
     * 密码加盐
     */
    @JsonIgnore
    @ApiModelProperty(value = "密码加盐")
    private String salt;

    /**
     * 重置密码的 token
     */
    @JsonIgnore
    @ApiModelProperty(value = "重置密码的 token")
    private String resetPasswordToken;

    /**
     * 角色
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "用户绑定的角色信息")
    private Set<RoleDTO> roles = new HashSet<>();

    /**
     * 权限
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "用户绑定的权限信息")
    private Set<PermissionDTO> permissions = new HashSet<>();

    /**
     * 是否修改过密码
     */
    @ApiModelProperty(value = "是否修改过密码", example = "false")
    private Boolean passwordChanged;

    /**
     * 用户创建来源
     */
    @ApiModelProperty(value = "职位编号", example = "DEFAULT 或者 ORG")
    private UserCreateSourceEnum createSource;

    @ApiModelProperty(value = "系统用户类型", example = "DOMESTIC_CONSUMER")
    private SysUserTypeEnum type;

    public SessionUser generateSessionUser() {
        SessionUser sessionUser = new SessionUser();
        sessionUser.setTenantId(getTenantId());
        sessionUser.setUserId(getId());
        sessionUser.setPassword(getPassword());
        sessionUser.setUsername(getUsername());
        sessionUser.setAppId(getAppId());
        Set<String> roleNames = new HashSet<>();
        for (RoleDTO role : roles) {
            String name = role.getName();
            if (StringUtils.isNotBlank(name)) {
                roleNames.add(role.getName());
            }
        }

        sessionUser.setRoleNames(roleNames);

        Set<String> permissionNames = new HashSet<>();
        for (PermissionDTO permission : permissions) {
            String name = permission.getName();
            if (StringUtils.isNotBlank(name)) {
                permissionNames.add(name);
            }
        }

        sessionUser.setPermissionNames(permissionNames);
        return sessionUser;
    }

}
