CREATE TABLE public.prisma_custom_index_combination (
	id varchar(36) NOT NULL,
	prisma_report_data_id varchar(36) NOT NULL,
	index_id varchar(36) NOT NULL,
	parent_dimension_id varchar(36) NULL,
	dimension_id varchar(36)  NULL,
	demographic_contents json default '[]',
	create_by varchar(36) NOT NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) NULL,
	CONSTRAINT "___pk___prisma_custom_index_combination___" PRIMARY KEY (id)
);
COMMENT ON TABLE  "public"."prisma_custom_index_combination" IS 'prisma自定义指数组合表';
COMMENT ON COLUMN "public"."prisma_custom_index_combination"."prisma_report_data_id" IS 'prisma报告ID';
COMMENT ON COLUMN "public"."prisma_custom_index_combination"."index_id" IS '指数ID';
COMMENT ON COLUMN "public"."prisma_custom_index_combination"."parent_dimension_id" IS '一级维度ID';
COMMENT ON COLUMN "public"."prisma_custom_index_combination"."dimension_id" IS '二级维度ID';
COMMENT ON COLUMN "public"."prisma_custom_index_combination"."demographic_contents" IS '人口标签';