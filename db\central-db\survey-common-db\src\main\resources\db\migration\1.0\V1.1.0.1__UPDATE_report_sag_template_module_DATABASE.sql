/*标准报告配置表更新*/
alter table "public"."survey_standard_sag_report_template_config" add dimension_config_type varchar(100) ;
COMMENT ON COLUMN "public"."survey_standard_sag_report_template_config"."dimension_config_type" IS '报告配置表维度内容配置类型';

alter table "public"."survey_standard_sag_report_template_config" add dimension_score_config_type varchar(100) ;
COMMENT ON COLUMN "public"."survey_standard_sag_report_template_config"."dimension_score_config_type" IS '报告配置表维度分数配置类型';

alter table "public"."survey_standard_sag_report_dimension" add config_type varchar(100) ;
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."config_type" IS '报告配置表配置类型';

update "public"."survey_standard_sag_report_template_config" set "data" = '{"paymentConfig":{"backCover":0,"detailedScore":0,"frontCover":0,"overview":0,"preface":0,"appendix":0,"responseStyle":0,"interviewSuggestions":0,"developmentSuggestions":0,"total":0}}'
where "id" = '1280332187905724418';


