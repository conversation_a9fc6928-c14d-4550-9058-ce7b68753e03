DROP TABLE IF EXISTS "public"."survey_user_permission";
CREATE TABLE survey_user_permission (
                                        id VARCHAR ( 36 ) NOT NULL,
                                        user_id VARCHAR ( 36 ) DEFAULT NULL,
                                        permission_id VARCHAR ( 36 ) DEFAULT NULL,
                                        create_by VA<PERSON>HAR ( 36 ) NULL,
                                        create_time TIMESTAMP ( 3 ) NULL DEFAULT now( ),
                                        update_time TIMESTAMP ( 3 ) NULL DEFAULT now( ),
                                        last_update_by VA<PERSON>HAR ( 36 ) NULL,
                                        is_deleted BOOL NULL DEFAULT FALSE,
                                        app_id VARCHAR ( 36 ) NULL,
                                        CONSTRAINT "___pk__survey_user_permission___" PRIMARY KEY ( ID )
);
COMMENT ON TABLE "public"."survey_user_permission" IS '用户权限关联表';
COMMENT ON COLUMN "public"."survey_user_permission"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."survey_user_permission"."permission_id" IS '权限id';
