DROP TABLE IF EXISTS prisma_history_data_match_mapping;
CREATE TABLE prisma_history_data_match_mapping
(
    "id"                              VARCHAR(36)  NOT NULL,
    "prisma_history_data_id"          VARCHAR(36)  NOT NULL,
    "type"                            VARCHAR(50)  NOT NULL,
    "base_id"                         VARCHAR(36)  NOT NULL,
    "relative_ids"                    JSON,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___prisma_history_data_match_mapping___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."prisma_history_data_match_mapping" IS '历史对比数据匹配关系表';
COMMENT ON COLUMN "public"."prisma_history_data_match_mapping"."prisma_history_data_id" IS '历史对比表ID';
COMMENT ON COLUMN "public"."prisma_history_data_match_mapping"."type" IS '类型（组织、人口标签、题目）';
COMMENT ON COLUMN "public"."prisma_history_data_match_mapping"."base_id" IS '当前活动组织、人口标签、题目ID';
COMMENT ON COLUMN "public"."prisma_history_data_match_mapping"."relative_ids" IS '对比的活动组织、人口标签、题目ID集合';

