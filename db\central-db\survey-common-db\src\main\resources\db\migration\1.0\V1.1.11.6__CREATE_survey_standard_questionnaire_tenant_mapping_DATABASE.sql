DROP TABLE IF EXISTS "public"."survey_standard_prisma_norm_tenant_mapping";
CREATE TABLE "public"."survey_standard_prisma_norm_tenant_mapping"
(
    id varchar(36) NOT NULL,
    prisma_norm_id VARCHAR(36),
    tenant_id  VARCHAR(36),
    create_by varchar(36) NULL,
    create_time timestamp(3) NULL DEFAULT now(),
    update_time timestamp(3) NULL DEFAULT now(),
    last_update_by varchar(36) NULL,
    is_deleted bool NULL DEFAULT false,
    app_id varchar(36) null,
    CONSTRAINT "___pk__survey_standard_prisma_norm_tenant_mapping___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."survey_standard_prisma_norm_tenant_mapping"."prisma_norm_id" IS 'prisma常模Id';
COMMENT ON COLUMN "public"."survey_standard_prisma_norm_tenant_mapping"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."survey_standard_prisma_norm_tenant_mapping" IS 'prisma常模租户关联表';
