package com.knx.bean.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.knx.bean.client.RemoteCacheUserService;
import com.knx.bean.dao.mapper.KnxBeanAccountMapper;
import com.knx.bean.dao.mapper.KnxBeanTransactionMapper;
import com.knx.bean.model.dto.client.SystemUserSimpleVO;
import com.knx.bean.model.dto.client.UserDTO;
import com.knx.bean.model.entity.KnxBeanAccount;
import com.knx.bean.model.entity.KnxBeanTransaction;
import com.knx.bean.model.enums.KnxBeanTransactionStatusEnum;
import com.knx.bean.model.enums.KnxBeanTransactionTypeEnum;
import com.knx.bean.model.vo.BeanTransactionExcelVo;
import com.knx.bean.mq.MqProducer;
import com.knx.bean.service.api.IKnxBeanTransactionService;
import com.knx.bean.service.util.KnxBeanUtils;
import com.knx.common.base.service.exception.BusinessException;
import com.knx.common.base.utils.QueryUtil;
import com.knx.common.base.web.response.WebResponse;
import com.knx.common.error.knx.KnxBeanErrorCode;
import com.knx.survey.common.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Transactional
@Service
@Slf4j
public class KnxBeanTransactionService extends BaseServiceImpl<KnxBeanTransactionMapper, KnxBeanTransaction> implements IKnxBeanTransactionService {

    private static Interner<String> lock = Interners.newWeakInterner();
    @Resource
    private KnxBeanTransactionMapper knxbeanTransactionMapper;
    @Resource
    private KnxBeanAccountMapper knxBeanAccountMapper;
    @Autowired
    @Lazy
    private KnxBeanAccountService knxBeanAccountService;
    @Autowired
    private KnxBeanUtils knxBeanUtils;

    @Autowired
    private RemoteCacheUserService remoteCacheUserService;
    @Autowired
    private MqProducer mqProducer;

    /**
     * 保存肯豆流水
     */
    @Override
    public boolean saveTransaction(KnxBeanTransaction knxBeanTransaction) {
        String reqNo = knxBeanUtils.generateReqNo();
        if (knxBeanTransaction.getOrderNo() == null) { //取消充值会用之前充值的订单号
            knxBeanTransaction.setOrderNo(reqNo);
        }
        int num = baseMapper.insert(knxBeanTransaction);
        return num > 0;
    }

    @Override
    public void recharge(KnxBeanTransaction knxBeanTransaction) {
        boolean result = compareAndUpdate(knxBeanTransaction);
        if (result) {
            mqProducer.sendTenantBeanChargeMessage(knxBeanTransaction.getTenantId());
        }
    }

    @Override
    public boolean cancelRecharge(String seqNo) {
        List<KnxBeanTransaction> transactions = knxbeanTransactionMapper.selectList(new QueryWrapper<KnxBeanTransaction>().eq(StringUtils.camelToUnderline(KnxBeanTransaction.Fields.orderNo), seqNo));
        if (CollectionUtils.isEmpty(transactions)) {
            throw new BusinessException(KnxBeanErrorCode.NO_CORRESPONDING_ORDER_FOUND);//订单没找到
        } else if (transactions.size() >= 2) {
            throw new BusinessException(KnxBeanErrorCode.THE_ORDER_HAS_BEEN_CANCELLED);//订单已被取消
        } else {

            KnxBeanTransaction knxBeanTransaction = transactions.get(0);

            KnxBeanTransaction transaction = new KnxBeanTransaction();
            transaction.setAmount(knxBeanTransaction.getAmount());
            transaction.setContractAmount(knxBeanTransaction.getContractAmount());
            transaction.setContractNo(knxBeanTransaction.getContractNo());
            transaction.setOrderNo(knxBeanTransaction.getOrderNo());
            transaction.setTenantId(knxBeanTransaction.getTenantId());
            transaction.setStatus(KnxBeanTransactionStatusEnum.USED);//取消订单流水已执行
            boolean result = Boolean.TRUE;
            if (Objects.equals(KnxBeanTransactionTypeEnum.RECHARGE.name(), knxBeanTransaction.getType().name())){
                transaction.setType(KnxBeanTransactionTypeEnum.CANCEL_RECHARGE);
                result = compareAndUpdate(transaction);
            } else if(Objects.equals(KnxBeanTransactionTypeEnum.MESSAGE_RECHARGE.name(), knxBeanTransaction.getType().name())){
                transaction.setType(KnxBeanTransactionTypeEnum.CANCEL_MESSAGE_RECHARGE);
                result = messageCompareAndUpdate(transaction);
            }


            knxBeanTransaction.setStatus(KnxBeanTransactionStatusEnum.CANCELED);

            return result;
        }

    }

    @Override
    public boolean spend(KnxBeanTransaction knxBeanTransaction) {
        return compareAndUpdate(knxBeanTransaction);
    }

    @Override
    public List<KnxBeanTransaction> listByTenantId(String tenantId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq(StringUtils.camelToUnderline(KnxBeanTransaction.Fields.tenantId), tenantId);
        QueryUtil.createTimeDesc(queryWrapper);
        return list(queryWrapper);
    }

    @Override
    public List<BeanTransactionExcelVo> export(String tenantId) {
        List<KnxBeanTransaction> beanTransactions = listByTenantId(tenantId);
        List<String> userIds = beanTransactions.stream().map(KnxBeanTransaction::getCreateBy).collect(Collectors.toList());
        List<SystemUserSimpleVO> users = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            WebResponse<List<SystemUserSimpleVO>> webResponse = remoteCacheUserService.getSystemUserByIds(userIds);
            if (webResponse != null && 0 == webResponse.getResult().getCode()) {
                users = webResponse.getData();
            }
        }
        List<BeanTransactionExcelVo> resultList = new ArrayList<>();
        for (KnxBeanTransaction item : beanTransactions) {
            BeanTransactionExcelVo vo = new BeanTransactionExcelVo();
            BeanUtils.copyProperties(item, vo);
            SystemUserSimpleVO userDTO = users.stream().filter(n -> Objects.equals(n.getId(), item.getCreateBy())).findFirst().orElse(null);
            if (userDTO != null) {
                vo.setUserName(userDTO.getRealName());
            }
            resultList.add(vo);
        }
        return resultList;
    }

    private boolean compareAndUpdate(KnxBeanTransaction knxBeanTransaction) {
        String tenantId = knxBeanTransaction.getTenantId();

        synchronized (lock.intern(tenantId)) {
            KnxBeanAccount account =
                    knxBeanAccountMapper.selectOne(new QueryWrapper<KnxBeanAccount>().eq(StringUtils.camelToUnderline(KnxBeanTransaction.Fields.tenantId), tenantId));
            if (account == null) {
                account = new KnxBeanAccount();
                account.setTenantId(tenantId);
                account.setBalance(BigDecimal.ZERO);
                account.setMessageBalance(BigDecimal.ZERO);
            }
            KnxBeanTransaction newestKnxBeanTransaction = knxbeanTransactionMapper.getNewest(tenantId);//最后一条流水记录
            BigDecimal amount = knxBeanTransaction.getAmount();
            if (amount == null) {
                amount = new BigDecimal(0);
            }
            if (Objects.equals(KnxBeanTransactionTypeEnum.RECHARGE.name(), knxBeanTransaction.getType().name()) || Objects.equals(KnxBeanTransactionTypeEnum.RECHARGE_CARD, knxBeanTransaction.getType())) {
                if (newestKnxBeanTransaction == null) { //第一次充值，没有流水记录
                    BigDecimal balance = amount;
                    knxBeanTransaction.setBalance(balance);//充值后金额(第一次充值)
                    account.setBalance(balance);
                } else {
                    BigDecimal balance = newestKnxBeanTransaction.getBalance().add(amount);//充值后金额(非第一次充值)
                    knxBeanTransaction.setBalance(balance);
                    account.setBalance(balance);
                }

            } else if (Objects.equals(KnxBeanTransactionTypeEnum.CANCEL_RECHARGE.name(), knxBeanTransaction.getType().name())) {
                BigDecimal balance = newestKnxBeanTransaction.getBalance().subtract(amount);//取消订单后金额
                knxBeanTransaction.setBalance(balance);
                account.setBalance(balance);//取消订单可以扣成负数
                knxbeanTransactionMapper.updateById(knxBeanTransaction);//更改充值订单流水为已取消
            } else {
                if (account.getBalance().compareTo(amount) < 0) {
                    throw new BusinessException(KnxBeanErrorCode.INSUFFICIENT_ACCOUNT_BALANCE);
                }
                BigDecimal balance = account.getBalance().subtract(amount);//消费后金额
                knxBeanTransaction.setBalance(balance);
                account.setBalance(balance);
            }

            boolean result = saveTransaction(knxBeanTransaction);//添加流水记录
            if (!result) {
                return result; //添加失败返回false，业务失败
            }
            QueryWrapper<KnxBeanAccount> qw = new QueryWrapper<>();
            qw.eq(StringUtils.camelToUnderline(KnxBeanTransaction.Fields.tenantId), account.getTenantId());

            account.setUpdateTime(new Date());

            KnxBeanAccount one = knxBeanAccountService.getOne(qw);
            boolean flag = false;
            if (one == null) {
                flag = knxBeanAccountService.save(account);
            } else {
                flag = knxBeanAccountService.updateById(account);
            }

            return flag;
        }


    }

    @Override
    public void messageRecharge(KnxBeanTransaction knxBeanTransaction) {
        boolean result = messageCompareAndUpdate(knxBeanTransaction);
        if (result) {
            mqProducer.sendTenantBeanChargeMessage(knxBeanTransaction.getTenantId());
        }
    }


    @Override
    public boolean messageSpend(KnxBeanTransaction knxBeanTransaction) {
        return messageCompareAndUpdate(knxBeanTransaction);
    }
    private boolean messageCompareAndUpdate(KnxBeanTransaction knxBeanTransaction) {
        String tenantId = knxBeanTransaction.getTenantId();

        synchronized (lock.intern(tenantId)) {
            KnxBeanAccount account =
                    knxBeanAccountMapper.selectOne(new QueryWrapper<KnxBeanAccount>().eq(StringUtils.camelToUnderline(KnxBeanTransaction.Fields.tenantId), tenantId));
            if (account == null) {
                account = new KnxBeanAccount();
                account.setTenantId(tenantId);
                account.setBalance(BigDecimal.ZERO);
                account.setMessageBalance(BigDecimal.ZERO);
            }
            KnxBeanTransaction newestKnxBeanTransaction = knxbeanTransactionMapper.getMessageNewest(tenantId );//最后一条流水记录
            BigDecimal amount = knxBeanTransaction.getAmount();
            if (amount == null) {
                amount = new BigDecimal(0);
            }
            if (Objects.equals(KnxBeanTransactionTypeEnum.MESSAGE_RECHARGE.name(), knxBeanTransaction.getType().name())) {
                if (newestKnxBeanTransaction == null) { //第一次充值，没有流水记录
                    BigDecimal balance = amount;
                    knxBeanTransaction.setBalance(balance);//充值后金额(第一次充值)
                    account.setMessageBalance(balance);
                } else {
                    BigDecimal balance = newestKnxBeanTransaction.getBalance().add(amount);//充值后金额(非第一次充值)
                    knxBeanTransaction.setBalance(balance);
                    account.setMessageBalance(balance);
                }

            } else if (Objects.equals(KnxBeanTransactionTypeEnum.CANCEL_MESSAGE_RECHARGE.name(), knxBeanTransaction.getType().name())) {
                BigDecimal balance = newestKnxBeanTransaction.getBalance().subtract(amount);//取消订单后金额
                knxBeanTransaction.setBalance(balance);
                account.setMessageBalance(balance);//取消订单可以扣成负数
                knxbeanTransactionMapper.updateById(knxBeanTransaction);//更改充值订单流水为已取消
            } else {
                if (account.getMessageBalance().compareTo(amount) < 0) {
                    throw new BusinessException(KnxBeanErrorCode.INSUFFICIENT_ACCOUNT_BALANCE);
                }
                BigDecimal balance = account.getMessageBalance().subtract(amount);//消费后金额
                knxBeanTransaction.setBalance(balance);
                account.setMessageBalance(balance);
            }

            boolean result = saveTransaction(knxBeanTransaction);//添加流水记录
            if (!result) {
                return result; //添加失败返回false，业务失败
            }
            QueryWrapper<KnxBeanAccount> qw = new QueryWrapper<>();
            qw.eq(StringUtils.camelToUnderline(KnxBeanTransaction.Fields.tenantId), account.getTenantId());

            account.setUpdateTime(new Date());

            KnxBeanAccount one = knxBeanAccountService.getOne(qw);
            boolean flag = false;
            if (one == null) {
                flag = knxBeanAccountService.save(account);
            } else {
                flag = knxBeanAccountService.updateById(account);
            }

            return flag;
        }


    }

}
