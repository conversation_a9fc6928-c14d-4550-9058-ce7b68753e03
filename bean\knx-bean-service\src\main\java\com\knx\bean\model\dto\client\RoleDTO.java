package com.knx.bean.model.dto.client;

import com.baomidou.mybatisplus.annotation.TableField;
import com.knx.common.base.dao.mybatis.config.LogicalDeleteAddSuffix;
import com.knx.common.base.model.BaseTenantModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Package com.knx.uas.dao.model
 * @date 2020/6/4 15:19
 */
@ApiModel("角色信息")
@Data
@FieldNameConstants
public class RoleDTO extends BaseTenantModel {

    /**
     * 角色名称
     * API 调用无法创建参与验证的角色name 只能从数据库创建
     */
    @ApiModelProperty(value = "角色名称", example = "AAA")
    @LogicalDeleteAddSuffix
    @Size(min = 0, max = 100, message = "角色名称范围为0~50")
    private String name;
    /**
     * 角色显示名称
     */
    @ApiModelProperty(value = "角色显示名称", example = "AAA")
    @Size(min = 0, max = 100, message = "角色名称范围为0~50")
    @NotBlank(message = "显示名称不能为空")
    private String displayName;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", example = "管理员角色")
    @Size(min = 0, max = 500, message = "备注范围为0~50")
    private String remark;

    @ApiModelProperty(value = "租户id", example = "1286860429435342850")
    private String tenantId;

    @TableField(exist = false)
    @ApiModelProperty(value = "菜单信息")
    private List<MenuDTO> menus;

    @TableField(exist = false)
    @ApiModelProperty(value = "权限信息")
    private Set<PermissionDTO> permissions;

    @TableField(exist = false)
    @ApiModelProperty(value = "用户id", example = "1324255348076441602")
    private String userId;

}
