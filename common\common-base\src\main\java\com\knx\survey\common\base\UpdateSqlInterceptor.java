package com.knx.survey.common.base;

import com.knx.survey.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Signature;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2021/10/12 16:39
 */

@Component
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
public class UpdateSqlInterceptor extends AbstractSqlInterceptor {

    @Override
    public String newSql(String oldSql) {
        String[] split = StringUtils.split(oldSql, ";");
        String newSql = "";
        for (String s : split) {
            String[] oldSqlArray = StringUtils.split(s, StringUtils.SPACE);
            for (int i = 0; i < oldSqlArray.length; i++) {
                newSql += oldSqlArray[i] + StringUtils.SPACE;
                if (i == 2) {
                    String time = DateUtil.formatTime2Millisecond(new Date());
                    newSql += "update_time = '" + time + "' ,";
                }
            }
            newSql += ";";
        }


        return newSql;
    }

    @Override
    public Boolean isInterceptor(String sqlType, String oldSql) {
        return StringUtils.equalsIgnoreCase(sqlType, "UPDATE") && !StringUtils.containsIgnoreCase(oldSql, "flyway_schema_history_survey") && !StringUtils.containsIgnoreCase(oldSql, "update_time");
    }
}
