package com.knx.survey.common.util.excelUtil.handler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import lombok.Getter;
import lombok.Setter;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2021/6/3 17:11
 */
public abstract class DataValidationHandler implements SheetWriteHandler {

    @Setter
    public Workbook workbook;
    @Setter
    @Getter
    private Map<Integer, String[]> mapDropDown;

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        this.workbook = writeWorkbookHolder.getWorkbook();
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        Sheet sheetHidden = createSheet(writeWorkbookHolder);
        List<DataValidation> dataValidation = createDataValidation(sheet.getDataValidationHelper(), mapDropDown, sheetHidden);
        for (DataValidation validation : dataValidation) {
            sheet.addValidationData(validation);
        }
    }


    /**
     * 下拉字符不能超过255
     *
     * @param helper
     * @param mapDropDown
     * @param sheetHidden
     * @return
     */
    public abstract List<DataValidation> createDataValidation(DataValidationHelper helper, Map<Integer, String[]> mapDropDown, Sheet sheetHidden);


    public abstract Sheet createSheet(WriteWorkbookHolder writeWorkbookHolder);

    public Map<Integer, String> numberLetterMap() {
        Map<Integer, String> map = new HashMap<>();
        map.put(0, "A");
        map.put(1, "B");
        map.put(2, "C");
        map.put(3, "D");
        map.put(4, "E");
        map.put(5, "F");
        map.put(6, "G");
        map.put(7, "H");
        map.put(8, "I");
        map.put(9, "J");
        map.put(10, "K");
        map.put(11, "L");
        map.put(12, "M");
        map.put(13, "N");
        map.put(14, "O");
        map.put(15, "P");
        map.put(16, "Q");
        map.put(17, "R");
        map.put(18, "S");
        map.put(19, "T");
        map.put(20, "U");
        map.put(21, "V");
        map.put(22, "W");
        map.put(23, "X");
        map.put(24, "Y");
        map.put(25, "Z");
        return map;
    }

}
