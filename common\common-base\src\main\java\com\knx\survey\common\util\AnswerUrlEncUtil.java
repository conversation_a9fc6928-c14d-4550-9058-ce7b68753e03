package com.knx.survey.common.util;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Map;

/**
 * 填答链接加密解密实现
 */
@Slf4j
public class AnswerUrlEncUtil {

    // 定义密钥字符串
    private static final String secret = "3n8VE#&G8*1hr%w!qV6@yb!3T";

    // 密钥字节数组
    private static final byte[] key;

    // AES Cipher 实例（线程安全）
    private static final Cipher AES_CIPHER;

    // 静态代码块，用于初始化密钥和Cipher
    static {
        try {
            key = MessageDigest.getInstance("SHA-256").digest(secret.getBytes(StandardCharsets.UTF_8));
            AES_CIPHER = Cipher.getInstance("AES/CTR/NoPadding");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) throws Exception {
        String plainText = "tenantid=1475283262011596802&projectid=1921790422918238209&answerCodeType=PRIVATE&personid=782ed07efa2ed316b6a34ecf2aaf109206905d8b31378e52477c9d25d4cc79bd";

        String encryptedData = encryptWithUrlEncode(plainText);
        System.out.println("加密结果 (Base64): " + encryptedData);

        String decryptedData = decryptWithUrlDecode(encryptedData);
        System.out.println("解密结果: " + decryptedData);
    }

    /**
     * 使用AES/CTR模式加密数据并进行URL编码
     *
     * @param text 原始数据
     * @return 加密后的数据（包含IV + 密文）经过URL编码
     * @throws UnsupportedEncodingException 编码失败
     * @throws Exception                    加密失败
     */
    public static String encryptWithUrlEncode(String text) throws UnsupportedEncodingException, Exception {
        return URLEncoder.encode(encrypt(text), "UTF-8");
    }

    /**
     * 使用AES/CTR模式解密数据并进行URL解码
     *
     * @param text 加密后的数据（包含IV + 密文）
     * @return 解密后的原始数据
     * @throws UnsupportedEncodingException 解码失败
     * @throws Exception                    解密失败
     */
    public static String decryptWithUrlDecode(String text) throws UnsupportedEncodingException, Exception {
        return decrypt(URLDecoder.decode(text, "UTF-8"));
    }

    /**
     * 使用AES/CTR模式加密数据
     *
     * @param text 原始数据
     * @return 加密后的数据（包含IV + 密文）
     * @throws Exception 加密失败时抛出
     */
    public static String encrypt(String text) throws Exception {
        byte[] plainText = text.getBytes(StandardCharsets.UTF_8);

        // 生成16字节的IV
        byte[] iv = new byte[16];
        new SecureRandom().nextBytes(iv);
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        // 初始化密钥
        SecretKeySpec keySpec = new SecretKeySpec(key, "AES");

        // 初始化加密器
        AES_CIPHER.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

        // 执行加密
        byte[] encrypted = AES_CIPHER.doFinal(plainText);

        // 合并IV和密文
        byte[] combined = new byte[iv.length + encrypted.length];
        System.arraycopy(iv, 0, combined, 0, iv.length);
        System.arraycopy(encrypted, 0, combined, iv.length, encrypted.length);

        return Base64.getEncoder().encodeToString(combined);
    }

    /**
     * 使用AES/CTR模式解密数据
     *
     * @param text 加密后的数据（包含IV + 密文）
     * @return 解密后的原始数据
     * @throws Exception 解密失败时抛出
     */
    public static String decrypt(String text) throws Exception {
        byte[] cipherText = Base64.getDecoder().decode(text);

        // 提取IV
        byte[] iv = new byte[16];
        System.arraycopy(cipherText, 0, iv, 0, iv.length);
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        // 提取密文
        byte[] encryptedData = new byte[cipherText.length - iv.length];
        System.arraycopy(cipherText, iv.length, encryptedData, 0, encryptedData.length);

        // 初始化密钥
        SecretKeySpec keySpec = new SecretKeySpec(key, "AES");

        // 初始化解密器
        AES_CIPHER.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

        // 执行解密
        return new String(AES_CIPHER.doFinal(encryptedData), StandardCharsets.UTF_8);
    }

    @SneakyThrows
    public static String transferToEncryptUrl(String prefixUrl, String paramsUrl) {
        if (!prefixUrl.endsWith("?")) {
            prefixUrl += "?";
        }
        return prefixUrl + "key=" + encryptWithUrlEncode(paramsUrl);
    }

    @SneakyThrows
    public static String transferToEncryptUrl(String oldUrl, Map<String, String> paramsMap) {
        String[] urlParts = oldUrl.split("\\?");
        //没有参数
        if (urlParts.length == 1) {
            return oldUrl;
        }

        String params = mapToUrlParams(paramsMap);
        return transferToEncryptUrl(urlParts[0],params);
    }

    public static String mapToUrlParams(Map<String, String> paramsMap) {
        return paramsMap.entrySet()
                .stream()
                .filter(entry -> entry.getValue() != null) // 过滤 null 值
                .map(entry -> {
                    try {
                        String encodedKey = URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8.name());
                        String encodedValue = URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8.name());
                        return encodedKey + "=" + encodedValue;
                    } catch (Exception e) {
                        throw new RuntimeException("URL encode failed for key: " + entry.getKey(), e);
                    }
                })
                .reduce((param1, param2) -> param1 + "&" + param2)
                .orElse("");
    }
}
