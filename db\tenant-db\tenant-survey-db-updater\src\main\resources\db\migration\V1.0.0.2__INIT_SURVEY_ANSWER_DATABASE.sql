
-- ----------------------------
-- Table structure for survey_answer
-- ----------------------------
DROP TABLE IF EXISTS "public"."survey_page_answer";
CREATE TABLE "public"."survey_page_answer" (
  "id"                  VARCHAR(36)        NOT NULL,
  "survey_type"         VARCHAR(50) ,
  "project_id"          VARCHAR(36) ,
  "questionnaire_id"    VARCHAR(36) ,
  "person_id"           VARCHAR(36) ,
  "page_no"             VARCHAR(50) ,
  "data"                JSON,
  "create_by"     VARCHAR(36),
  "create_time"         TIMESTAMP(3)    DEFAULT NOW(),
  "update_time"         TIMESTAMP(3)    DEFAULT NOW(),
  "last_update_by" VARCHAR(36),
  "is_deleted"          BOOL            DEFAULT false,
  "app_id"              VARCHAR(36),
  CONSTRAINT  "___pk__survey_page_answer___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_page_answer"."survey_type" IS '调研类型';
COMMENT ON COLUMN "public"."survey_page_answer"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_page_answer"."questionnaire_id" IS '问卷id';
COMMENT ON COLUMN "public"."survey_page_answer"."person_id" IS '人员id';
COMMENT ON COLUMN "public"."survey_page_answer"."page_no" IS '页码';
COMMENT ON COLUMN "public"."survey_page_answer"."data" IS '答案';
COMMENT ON TABLE  "public"."survey_page_answer" IS '填答页面';



-- ----------------------------
-- Table structure for survey_answer_time
-- ----------------------------
DROP TABLE IF EXISTS "public"."survey_answer_time";
CREATE TABLE "public"."survey_answer_time" (
  "id"                      VARCHAR(36)            NOT NULL,
  "survey_type"             VARCHAR(50) ,
  "project_id"              VARCHAR(36) ,
  "questionnaire_id"        VARCHAR(36) ,
  "person_id"               VARCHAR(36) ,
  "start_time"              TIMESTAMP(3),
  "end_time"                TIMESTAMP(3),
  "create_by"               VARCHAR(36),
  "create_time"             TIMESTAMP(3)        DEFAULT NOW(),
  "update_time"             TIMESTAMP(3)        DEFAULT NOW(),
  "last_update_by"          VARCHAR(36),
  "is_deleted"              BOOL                DEFAULT false,
  "app_id"                  VARCHAR(36),
  CONSTRAINT  "___pk__survey_answer_time___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_answer_time"."survey_type" IS '调研类型';
COMMENT ON COLUMN "public"."survey_answer_time"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_answer_time"."questionnaire_id" IS '答案';
COMMENT ON COLUMN "public"."survey_answer_time"."person_id" IS '人员id';
COMMENT ON COLUMN "public"."survey_answer_time"."start_time" IS '开始填答时间';
COMMENT ON COLUMN "public"."survey_answer_time"."end_time" IS '提交时间';
COMMENT ON TABLE  "public"."survey_answer_time" IS '填答页面';



DROP TABLE IF EXISTS "public"."survey_answer";
CREATE TABLE "public"."survey_answer" (
  "id"                      VARCHAR(36)        NOT NULL,
  "project_id"              VARCHAR(36) ,
  "person_id"               VARCHAR(36) ,
  "questionnaire_id"        VARCHAR(36) ,
  "question_id"             VARCHAR(36) ,
  "option_id"               VARCHAR(36) ,
  "text"                    TEXT ,
  "type"                    VARCHAR(50) ,
  "create_by"               VARCHAR(36),
  "create_time"             TIMESTAMP(3)    DEFAULT NOW(),
  "update_time"             TIMESTAMP(3)    DEFAULT NOW(),
  "last_update_by"          VARCHAR(36),
  "is_deleted"              BOOL            DEFAULT false,
  "app_id"                  VARCHAR(36),
  CONSTRAINT  "___pk__survey_answer___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_answer"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_answer"."person_id" IS '参与人员id';
COMMENT ON COLUMN "public"."survey_answer"."questionnaire_id" IS '问卷id';
COMMENT ON COLUMN "public"."survey_answer"."question_id" IS '题目id';
COMMENT ON COLUMN "public"."survey_answer"."option_id" IS '选项id';
COMMENT ON COLUMN "public"."survey_answer"."text" IS '填答内容';
COMMENT ON COLUMN "public"."survey_answer"."type" IS '题目类型';
COMMENT ON TABLE  "public"."survey_answer" IS '填答页面';


-- ----------------------------
-- Table structure for survey_person_answer_notice
-- ----------------------------
DROP TABLE IF EXISTS "public"."survey_person_answer_notice";
CREATE TABLE "public"."survey_person_answer_notice" (
  "id"                      VARCHAR(36)            NOT NULL,
  "project_id"              VARCHAR(36) ,
  "person_id"               VARCHAR(36) ,
  "survey_type"             VARCHAR(50) ,
  "status"                  VARCHAR(50) ,
  "create_by"               VARCHAR(36),
  "create_time"             TIMESTAMP(3)        DEFAULT NOW(),
  "update_time"             TIMESTAMP(3)        DEFAULT NOW(),
  "last_update_by"          VARCHAR(36),
  "is_deleted"              BOOL                DEFAULT false,
  "app_id"                  VARCHAR(36),
  CONSTRAINT  "___pk__survey_person_answer_notice___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_person_answer_notice"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_person_answer_notice"."person_id" IS '参与人员id';
COMMENT ON COLUMN "public"."survey_person_answer_notice"."survey_type" IS '调研类型';
COMMENT ON COLUMN "public"."survey_person_answer_notice"."status" IS '状态 初始 报表生成';
COMMENT ON TABLE  "public"."survey_person_answer_notice" IS '人员填答通知表';

