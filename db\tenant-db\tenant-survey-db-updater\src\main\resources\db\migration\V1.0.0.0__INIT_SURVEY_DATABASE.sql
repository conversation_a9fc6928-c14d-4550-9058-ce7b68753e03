/*活动表*/
DROP TABLE IF EXISTS survey_project;
CREATE TABLE survey_project
(
    "id"                              VARCHAR(36)  NOT NULL,
    "name"                            VARCHAR(50)  NOT NULL,
    "is_auto_answer_expire"           BOOL         NOT NULL,
    "answer_expire_time"              TIMESTAMP(3) DEFAULT NOW(),
    "permissions"                     VARCHAR(50)  ,
    "language"                        VARCHAR(50)  ,
    "is_enable_welcome_page"          BOOL         NOT NULL,
    "welcome_page"                    VARCHAR(100),
    "end_page"                        VARCHAR(100) ,
    "answer_type"                     VARCHAR(50)  ,
    "status"                          VARCHAR(50)  ,
    "is_show_questionnaire_list"      BOOL         ,
    "is_show_background_pic"          BOOL         ,
    "pc_background_pic"               VARCHAR(100) ,
    "mobile_background_pic"           VARCHAR(100) ,
    "start_time"                      TIMESTAMP(3) DEFAULT NOW(),
    "end_time"                        TIMESTAMP(3) DEFAULT NOW(),
    "is_check_license_notice"         BOOL,
    "is_check_answer_instructions"    BOOL,
    "estimates_count"                 INT4,
    "is_allow_repeated_write"         BOOL,
    "password_generation_type"        VARCHAR(50),
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___survey_project___" PRIMARY KEY ("id")
);

COMMENT ON TABLE  "public"."survey_project" IS 'survey活动表';
COMMENT ON COLUMN "public"."survey_project"."name" IS '活动名';
COMMENT ON COLUMN "public"."survey_project"."is_auto_answer_expire" IS '是否自动关闭填答';
COMMENT ON COLUMN "public"."survey_project"."answer_expire_time" IS '定时关闭的时间';
COMMENT ON COLUMN "public"."survey_project"."permissions" IS '枚举，公共和私有 默认公共';
COMMENT ON COLUMN "public"."survey_project"."language" IS '多语言';
COMMENT ON COLUMN "public"."survey_project"."is_enable_welcome_page" IS '是否开启欢迎页面';
COMMENT ON COLUMN "public"."survey_project"."welcome_page" IS '欢迎页面路径';
COMMENT ON COLUMN "public"."survey_project"."end_page" IS '结束界面路径';
COMMENT ON COLUMN "public"."survey_project"."answer_type" IS '枚举  填答方式';
COMMENT ON COLUMN "public"."survey_project"."status" IS '枚举 填答中，结束';
COMMENT ON COLUMN "public"."survey_project"."is_show_questionnaire_list" IS '是否显示问卷列表';
COMMENT ON COLUMN "public"."survey_project"."is_show_background_pic" IS '是否显示背景图片';
COMMENT ON COLUMN "public"."survey_project"."pc_background_pic" IS 'pc背景';
COMMENT ON COLUMN "public"."survey_project"."mobile_background_pic" IS '移动背景';
COMMENT ON COLUMN "public"."survey_project"."start_time" IS '活动开始周期';
COMMENT ON COLUMN "public"."survey_project"."end_time" IS '活动结束周期';
COMMENT ON COLUMN "public"."survey_project"."is_check_license_notice" IS '是否勾选许可声明';
COMMENT ON COLUMN "public"."survey_project"."is_check_answer_instructions" IS '是否勾选作答说明';
COMMENT ON COLUMN "public"."survey_project"."estimates_count" IS '预估人数';
COMMENT ON COLUMN "public"."survey_project"."is_allow_repeated_write" IS '是否开启重复填答';
COMMENT ON COLUMN "public"."survey_project"."password_generation_type" IS '口令 方式 指定字段 系统生成';




DROP TABLE IF EXISTS survey_questionnaire;
CREATE TABLE survey_questionnaire
(
    "id"                              VARCHAR(36)        NOT NULL,
    "project_id"                      VARCHAR(36),
    "name"                            JSON,
    "survey_type"                     VARCHAR(50),
    "source"                          VARCHAR(50),
    "standard_questionnaire_id"       VARCHAR(36),
    "duration"                        INT4,
    "answer_description"              TEXT,
    "is_shown_answer_description"     BOOL,
    "report_template_id"              VARCHAR(36),
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3)      DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3)      DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL              DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT  "___pk___survey_questionnaire___" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."survey_questionnaire" IS 'survey问卷表';
COMMENT ON COLUMN "public"."survey_questionnaire"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_questionnaire"."name" IS '问卷名称';
COMMENT ON COLUMN "public"."survey_questionnaire"."survey_type" IS '问卷类型';
COMMENT ON COLUMN "public"."survey_questionnaire"."source" IS '问卷来源';
COMMENT ON COLUMN "public"."survey_questionnaire"."standard_questionnaire_id" IS '引用标准问卷的id';
COMMENT ON COLUMN "public"."survey_questionnaire"."duration" IS '倒计时';
COMMENT ON COLUMN "public"."survey_questionnaire"."answer_description" IS '作答说明';
COMMENT ON COLUMN "public"."survey_questionnaire"."is_shown_answer_description" IS '是否展示作答说明';
COMMENT ON COLUMN "public"."survey_questionnaire"."report_template_id" IS '租户报告id';



DROP TABLE IF EXISTS survey_question;
CREATE TABLE survey_question
(
    "id"                                 VARCHAR(36) NOT NULL,
    "name"                               JSON,
    "questionnaire_id"                   VARCHAR(36),
    "standard_questionnaire_question_id" VARCHAR(36),
    "type"                               VARCHAR(50),
    "timing"                             INT4,
    "level"                              VARCHAR(50),
    "is_require"                         BOOL,
    "options"                            JSON,
    "parent_id"                          VARCHAR(36),
    "sort"                               INT4,
    "create_by"                          VARCHAR(36),
    "create_time"                        TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                        TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                     VARCHAR(36),
    "is_deleted"                         BOOL         DEFAULT false,
    "app_id"                             VARCHAR(36),
    CONSTRAINT "___pk___survey_question___" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."survey_question" IS 'survey问题表';
COMMENT ON COLUMN "public"."survey_question"."name" IS '题目名称';
COMMENT ON COLUMN "public"."survey_question"."questionnaire_id" IS '问卷id';
COMMENT ON COLUMN "public"."survey_question"."standard_questionnaire_question_id" IS '标准问卷题目id';
COMMENT ON COLUMN "public"."survey_question"."type" IS '题目类型 枚举值 单选、量表、多选、排序、比重、矩阵下拉、矩阵选择、问答题、描述说明 ';
COMMENT ON COLUMN "public"."survey_question"."timing" IS '计时设置';
COMMENT ON COLUMN "public"."survey_question"."level" IS '题目难度';
COMMENT ON COLUMN "public"."survey_question"."is_require" IS '题目是否必填';
COMMENT ON COLUMN "public"."survey_question"."options" IS '题目选项';
COMMENT ON COLUMN "public"."survey_question"."parent_id" IS '父子题标记id';
COMMENT ON COLUMN "public"."survey_question"."sort" IS '题目排序字段';



/*人口统计学表*/
DROP TABLE IF EXISTS survey_demographic;
CREATE TABLE survey_demographic
(
    "id"                      VARCHAR(36) NOT NULL,
    "project_id"              VARCHAR(36),
    "source"                  VARCHAR(50),
    "standard_demographic_id" VARCHAR(36),
    "name"                    JSON,
    "parent_id"               VARCHAR(36),
    "sort"                    INT4,
    "basic_label"             VARCHAR(100),
    "create_by"               VARCHAR(36),
    "create_time"             TIMESTAMP(3) DEFAULT NOW(),
    "update_time"             TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"          VARCHAR(36),
    "is_deleted"              BOOL   DEFAULT false,
    "app_id"                  VARCHAR(36),
    CONSTRAINT "___pk___survey_demographic___" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."survey_demographic" IS 'survey人口统计学表';
COMMENT ON COLUMN "public"."survey_demographic"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_demographic"."source" IS '枚举值： 自建或引用标准';
COMMENT ON COLUMN "public"."survey_demographic"."standard_demographic_id" IS '选择引用标准后必须传入的 标准人口统计学主键id';
COMMENT ON COLUMN "public"."survey_demographic"."name" IS '人口统计学名称';
COMMENT ON COLUMN "public"."survey_demographic"."parent_id" IS '父级id';
COMMENT ON COLUMN "public"."survey_demographic"."sort" IS '排序';
COMMENT ON COLUMN "public"."survey_demographic"."basic_label" IS '人口统计学基础标签';




/*人口统计题目表*/
DROP TABLE IF EXISTS survey_demographic_question;
CREATE TABLE survey_demographic_question
(
    "id"                  VARCHAR(36) NOT NULL,
    "project_id"          VARCHAR(36),
    "name"                JSON,
    "demographic_id"      VARCHAR(36),
    "is_require"          BOOL,
    "type"                VARCHAR(100),
    "rule"                VARCHAR(100),
    "sort"                INT4,
    "create_by"           VARCHAR(36),
    "create_time"         TIMESTAMP(3) DEFAULT NOW(),
    "update_time"         TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"      VARCHAR(36),
    "is_deleted"          BOOL   DEFAULT false,
    "app_id"              VARCHAR(36),
    CONSTRAINT  "___pk___survey_demographic_question___" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."survey_demographic_question" IS 'survey人口统计学题目表';
COMMENT ON COLUMN "public"."survey_demographic_question"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_demographic_question"."name" IS '人口统计学题目描述';
COMMENT ON COLUMN "public"."survey_demographic_question"."demographic_id" IS '人口统计学id';
COMMENT ON COLUMN "public"."survey_demographic_question"."is_require" IS '是否必填';
COMMENT ON COLUMN "public"."survey_demographic_question"."type" IS '枚举： 下拉和输入';
COMMENT ON COLUMN "public"."survey_demographic_question"."rule" IS '规则字段';
COMMENT ON COLUMN "public"."survey_demographic_question"."sort" IS '排序';



/*用户常模表*/
DROP TABLE IF EXISTS survey_norm;
CREATE TABLE survey_norm
(
    "id"                  VARCHAR(36)  NOT NULL,
    "source"              VARCHAR(100) NOT NULL,
    "standard_norm_id"    VARCHAR(36)  NOT NULL,
    "questionnaire_id"    VARCHAR(36)  NOT NULL,
    "name"                JSON         NOT NULL,
    "create_by"           VARCHAR(36),
    "create_time"         TIMESTAMP(3) DEFAULT NOW(),
    "update_time"         TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"      VARCHAR(36),
    "is_deleted"          BOOL         DEFAULT false,
    "app_id"              VARCHAR(36),
    CONSTRAINT  "___pk___survey_norm___" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."survey_norm" IS '用户常模表';
COMMENT ON COLUMN "public"."survey_norm"."source" IS '常模来源';
COMMENT ON COLUMN "public"."survey_norm"."standard_norm_id" IS '标准常模id';
COMMENT ON COLUMN "public"."survey_norm"."questionnaire_id" IS '问卷id';
COMMENT ON COLUMN "public"."survey_norm"."name" IS '常模名';



/*用户常模值表*/
DROP TABLE IF EXISTS survey_norm_value;
CREATE TABLE survey_norm_value
(
    "id"             VARCHAR(36)  NOT NULL,
    "norm_id"        VARCHAR(36)  NOT NULL,
    "question_id"    VARCHAR(36)  NOT NULL,
    "value"          VARCHAR(100) NOT NULL,
    "create_by"      VARCHAR(36),
    "create_time"    TIMESTAMP(3) DEFAULT NOW(),
    "update_time"    TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by" VARCHAR(36),
    "is_deleted"     BOOL         DEFAULT false,
    "app_id"         VARCHAR(36),
    CONSTRAINT  "___pk___survey_norm_value___" PRIMARY KEY ("id")
);

ALTER TABLE "public"."survey_norm_value" ADD CONSTRAINT  "___uk_norm_id___" UNIQUE (norm_id);

COMMENT ON TABLE "public"."survey_norm_value" IS '用户常模值表';
COMMENT ON COLUMN "public"."survey_norm_value"."norm_id" IS '常模id';
COMMENT ON COLUMN "public"."survey_norm_value"."question_id" IS '问题id';
COMMENT ON COLUMN "public"."survey_norm_value"."value" IS '常模值';

/*问卷维度表*/
DROP TABLE IF EXISTS survey_questionnaire_dimension;
CREATE TABLE survey_questionnaire_dimension
(
    "id"                        VARCHAR(36) NOT NULL,
    "name"                      JSON,
    "type"                      VARCHAR(50),
    "group_type"                VARCHAR(50),
    "questionnaire_id"          VARCHAR(36),
    "standard_questionnaire_id" VARCHAR(36),
    "package_id"                VARCHAR(36),
    "sort"                      INT4,
    "code"                      VARCHAR(50),
    "create_by"                 VARCHAR(36),
    "create_time"               TIMESTAMP(3) DEFAULT NOW(),
    "update_time"               TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"            VARCHAR(36),
    "is_deleted"                BOOL         DEFAULT false,
    "app_id"                    VARCHAR(36),
    CONSTRAINT "___pk___survey_questionnaire_dimension___" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."survey_questionnaire_dimension" IS '问卷维度表';
COMMENT ON COLUMN "public"."survey_questionnaire_dimension"."name" IS '问卷维度名称';
COMMENT ON COLUMN "public"."survey_questionnaire_dimension"."type" IS '问卷维度类型';
COMMENT ON COLUMN "public"."survey_questionnaire_dimension"."group_type" IS '枚举  单维度和组合维度';
COMMENT ON COLUMN "public"."survey_questionnaire_dimension"."questionnaire_id" IS '当前问卷id';
COMMENT ON COLUMN "public"."survey_questionnaire_dimension"."standard_questionnaire_id" IS '标准问卷id';
COMMENT ON COLUMN "public"."survey_questionnaire_dimension"."package_id" IS '题目组ID';
COMMENT ON COLUMN "public"."survey_questionnaire_dimension"."sort" IS '排序字段';
COMMENT ON COLUMN "public"."survey_questionnaire_dimension"."code" IS '编号';


/*维度关系表*/
DROP TABLE IF EXISTS survey_questionnaire_dimension_mapping;
CREATE TABLE survey_questionnaire_dimension_mapping
(
    "parent_id"      VARCHAR(36),
    "child_id"       VARCHAR(36),
     CONSTRAINT "___pk___survey_questionnaire_dimension_mapping___" PRIMARY KEY ("parent_id","child_id")
);

COMMENT ON TABLE "public"."survey_questionnaire_dimension_mapping" IS '维度关系表';
COMMENT ON COLUMN "public"."survey_questionnaire_dimension_mapping"."parent_id" IS '问卷维度父级id';
COMMENT ON COLUMN "public"."survey_questionnaire_dimension_mapping"."child_id" IS '问卷维度名称id';


/*问卷维度和题目*/
DROP TABLE IF EXISTS survey_questionnaire_dimension_question_mapping;
CREATE TABLE survey_questionnaire_dimension_question_mapping
(
    "questionnaire_dimension_id"      VARCHAR(36),
    "questionnaire_question_id"       VARCHAR(36),
     CONSTRAINT "___pk___survey_questionnaire_dimension_question_mapping___" PRIMARY KEY ("questionnaire_dimension_id","questionnaire_question_id")
);

COMMENT ON TABLE "public"."survey_questionnaire_dimension_question_mapping" IS '问卷维度和题目表';
COMMENT ON COLUMN "public"."survey_questionnaire_dimension_question_mapping"."questionnaire_dimension_id" IS '问卷维度id';
COMMENT ON COLUMN "public"."survey_questionnaire_dimension_question_mapping"."questionnaire_question_id" IS '问卷题目id';

/*调研参与人员信息*/
DROP TABLE IF EXISTS survey_person;
CREATE TABLE survey_person
(
    "id"               VARCHAR(36) NOT NULL,
    "code"             VARCHAR(50) UNIQUE,
    "project_id"       VARCHAR(36),
    "first_name"       VARCHAR(50),
    "last_name"        VARCHAR(50),
    "email"            VARCHAR(36),
    "phone"            VARCHAR(50),
    "password"         VARCHAR(50),
    "default_language" VARCHAR(50),
    "answer_status"    VARCHAR(50),
    "start_time"       TIMESTAMP(3),
    "end_time"         TIMESTAMP(3),
    "final_state"      VARCHAR(50),
    "report_url"       VARCHAR(100),
    "create_by"        VARCHAR(36),
    "create_time"      TIMESTAMP(3) DEFAULT NOW(),
    "update_time"      TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"   VARCHAR(36),
    "is_deleted"       BOOL         DEFAULT false,
    "app_id"           VARCHAR(36),
    CONSTRAINT "___pk___survey_person___" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."survey_person" IS '调研参与人员信息表';
COMMENT ON COLUMN "public"."survey_person"."code" IS '调研参与人员员工编号';
COMMENT ON COLUMN "public"."survey_person"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_person"."first_name" IS '调研参与人员firstName';
COMMENT ON COLUMN "public"."survey_person"."last_name" IS '调研参与人员lastName';
COMMENT ON COLUMN "public"."survey_person"."email" IS '调研参与人员邮箱地址';
COMMENT ON COLUMN "public"."survey_person"."phone" IS '调研参与人员手机号';
COMMENT ON COLUMN "public"."survey_person"."password" IS '调研参与人员口令';
COMMENT ON COLUMN "public"."survey_person"."default_language" IS '调研参与人员默认语言';
COMMENT ON COLUMN "public"."survey_person"."answer_status" IS '填答状态';
COMMENT ON COLUMN "public"."survey_person"."start_time" IS '填答时间';
COMMENT ON COLUMN "public"."survey_person"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."survey_person"."final_state" IS '填答结果';
COMMENT ON COLUMN "public"."survey_person"."report_url" IS '报告路径';


/*题目包表*/
DROP TABLE IF EXISTS survey_package;
CREATE TABLE survey_package
(
    "id"             VARCHAR(36) NOT NULL,
    "name"           JSON,
    "algorithm_type" VARCHAR(50),
    "algorithm"      JSON,
    "count"          INT4,
    "create_by"      VARCHAR(36),
    "create_time"    TIMESTAMP(3) DEFAULT NOW(),
    "update_time"    TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by" VARCHAR(36),
    "is_deleted"     BOOL         DEFAULT false,
    "app_id"         VARCHAR(36),
    CONSTRAINT "___pk___survey_package___" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."survey_package" IS '题目包表';
COMMENT ON COLUMN "public"."survey_package"."name" IS '题目包名';
COMMENT ON COLUMN "public"."survey_package"."algorithm_type" IS '算法类型枚举';
COMMENT ON COLUMN "public"."survey_package"."algorithm" IS '算法详情';
COMMENT ON COLUMN "public"."survey_package"."count" IS '题目总数';

/*题目包题目映射表*/
DROP TABLE IF EXISTS survey_package_question_mapping;
CREATE TABLE survey_package_question_mapping
(
    "package_id"              VARCHAR(36) NOT NULL,
    "standard_question_id"    VARCHAR(36) NOT NULL,
    "question_id"             VARCHAR(36) NOT NULL,
    "option_id"               VARCHAR(36),
     CONSTRAINT "___pk___survey_package_question_mapping___" PRIMARY KEY ("package_id","question_id")
);

COMMENT ON TABLE "public"."survey_package_question_mapping" IS '题目包题目映射表';
COMMENT ON COLUMN "public"."survey_package_question_mapping"."package_id" IS '题目组ID';
COMMENT ON COLUMN "public"."survey_package_question_mapping"."standard_question_id" IS '标准题目ID';
COMMENT ON COLUMN "public"."survey_package_question_mapping"."question_id" IS '题目ID';
COMMENT ON COLUMN "public"."survey_package_question_mapping"."option_id" IS '选项ID';




DROP TABLE IF EXISTS survey_questionnaire_person_mapping;
CREATE TABLE survey_questionnaire_person_mapping
(
    "questionnaire_id"        VARCHAR(36),
    "person_id"               VARCHAR(36),
     CONSTRAINT "___pk___survey_questionnaire_person_mapping___" PRIMARY KEY ("questionnaire_id","person_id")
);

COMMENT ON TABLE "public"."survey_questionnaire_person_mapping" IS '问卷参与人员映射表';
COMMENT ON COLUMN "public"."survey_questionnaire_person_mapping"."questionnaire_id" IS '问卷id';
COMMENT ON COLUMN "public"."survey_questionnaire_person_mapping"."person_id" IS '人员id';



-- ----------------------------
-- Table structure for survey_person_demographic_mapping
-- ----------------------------
DROP TABLE IF EXISTS "public"."survey_person_demographic_mapping";
CREATE TABLE "public"."survey_person_demographic_mapping" (
  "id"                  VARCHAR(36)  NOT NULL,
  "project_id"          VARCHAR(36) ,
  "person_id"           VARCHAR(36) ,
  "demographic_id"      VARCHAR(36) ,
  "content"             VARCHAR(100) ,
  "demographic_root_id" VARCHAR(36),
  CONSTRAINT "___pk___survey_person_demographic_mapping___" PRIMARY KEY (id)
);

COMMENT ON TABLE "public"."survey_person_demographic_mapping" IS '参与人员人口统计学映射表';
COMMENT ON COLUMN "public"."survey_person_demographic_mapping"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_person_demographic_mapping"."person_id" IS '人员id';
COMMENT ON COLUMN "public"."survey_person_demographic_mapping"."demographic_id" IS '人口统计学id';
COMMENT ON COLUMN "public"."survey_person_demographic_mapping"."content" IS '消息内容';
COMMENT ON COLUMN "public"."survey_person_demographic_mapping"."demographic_root_id" IS '人口统计根ID';

