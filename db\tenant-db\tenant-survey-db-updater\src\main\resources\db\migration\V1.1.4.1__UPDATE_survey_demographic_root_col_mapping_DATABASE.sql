DROP TABLE IF EXISTS survey_demographic_root_col_mapping;
CREATE TABLE survey_demographic_root_col_mapping (
	id varchar(36) NULL,
	project_id varchar(36) NOT NULL,
	demographic_root_id varchar(36) NOT NULL,
	col varchar(50) NOT NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) NULL,
	CONSTRAINT survey_demographic_root_col_mapping_pk PRIMARY KEY (id)
);
COMMENT ON TABLE public.survey_demographic_root_col_mapping IS '问卷人口信息行转列映射表';
COMMENT ON COLUMN public.survey_demographic_root_col_mapping.project_id IS '活动id';
COMMENT ON COLUMN public.survey_demographic_root_col_mapping.demographic_root_id IS '人口信息父ID';
COMMENT ON COLUMN public.survey_demographic_root_col_mapping.col IS '列名称';