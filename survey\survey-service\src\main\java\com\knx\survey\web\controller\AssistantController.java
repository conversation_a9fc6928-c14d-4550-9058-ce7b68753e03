package com.knx.survey.web.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.knx.common.base.enums.FileTypeEnum;
import com.knx.common.security.MultipleTenantDataSourceSessionContext;
import com.knx.feign.survey.RemoteSurveyCommonService;
import com.knx.survey.common.util.excelUtil.listener.MapBaseExcelListener;
import com.knx.survey.model.enums.LanguageTypeEnum;
import com.knx.common.base.service.exception.BusinessException;
import com.knx.common.base.web.response.WebResponse;
import com.knx.common.redis.RedisUtil;
import com.knx.common.security.FeignSessionContext;
import com.knx.feign.file.RemoteFileService;
import com.knx.survey.client.uerAcount.model.ProductOpenedTenantInfoVO;
import com.knx.survey.common.base.field.RedisKeyConstant;
import com.knx.survey.common.util.excelUtil.utils.EasyExcelParams;
import com.knx.survey.common.util.excelUtil.utils.ExcelUtil;
import com.knx.survey.common.util.excelUtil.utils.SheetData;
import com.knx.survey.error.SurveyErrorCode;
import com.knx.survey.model.enums.ReportStyleEnum;
import com.knx.survey.model.enums.StandardReportTypeEnum;
import com.knx.survey.model.report.prisma.PrismaReportData;
import com.knx.survey.model.report.prisma.StatisticsPrismaInfo;
import com.knx.survey.model.report.prisma.tencent.PrismaOrganizationDemographicApprovalData;
import com.knx.survey.model.report.prisma.tencent.PrismaOrgaztionOpenQuestionData;
import com.knx.survey.model.tenant.project.SurveyProject;
import com.knx.survey.model.tenant.questionnaire.SurveyQuestionnaire;
import com.knx.survey.report.perosn.strategy.PersonValidStrategy;
import com.knx.survey.report.perosn.strategy.PersonValidStrategyMap;
import com.knx.survey.service.api.domain.*;
import com.knx.survey.service.api.domain.report.tencent.IPrismaOrganizationDemographicApprovalDataService;
import com.knx.survey.service.api.domain.report.tencent.IPrismaOrgaztionOpenQuestionDataService;
import com.knx.survey.service.api.domain.tencent.ITencentParsingExcelService;
import com.knx.survey.service.impl.domain.SurveyIasSelectedQuestionMappingService;
import com.knx.survey.vo.ProjectVO;
import com.knx.survey.vo.person.RecalculatePersonValidVo;
import com.knx.survey.vo.prisma.ExportSelectQuestionVo;
import com.knx.survey.vo.prisma.PrismaHistoryDataVO;
import com.knx.survey.vo.prisma.PrismaNormVO;
import com.knx.survey.vo.report.AnalyzeAnswerVo;
import com.knx.survey.vo.report.InitDataReq;
import com.knx.survey.vo.report.RepairDataReq;
import com.knx.survey.vo.report.RepairGeneralCommentVo;
import com.knx.survey.vo.tencent.ImportDimensionScoreResultVo;
import com.knx.survey.vo.tencent.ImportIndexDimensionDataVo;
import com.knx.survey.vo.tencent.ImportOpenQuestionResultVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;


@Api(tags = {"辅助"})
@RestController
@RequestMapping("/survey/assistant")
@Slf4j
public class AssistantController {
    @Autowired
    private IAssistantService assistantService;
    @Autowired
    private ISurveyPersonOrganizationMappingService surveyPersonOrganizationMappingService;
    @Autowired
    private ISurveyPersonInvestigatorMappingService surveyPersonInvestigatorMappingService;
    @Autowired
    private RemoteFileService remoteFileService;
    @Autowired
    private ITenantService tenantService;
    @Autowired
    private ISurveyPageAnswerService surveyPageAnswerService;
    @Autowired
    private ISurveyPersonValidService surveyPersonValidService;
    @Autowired
    private IProjectService projectService;
    @Autowired
    private IQuestionnaireService questionnaireService;
    @Autowired
    private PersonValidStrategyMap personValidStrategyMap;
    /*    @Autowired
        private ThirdInfoSyncApi thirdInfoSyncApi;*/
    @Autowired
    private IFileService fileService;
    @Autowired
    private IQuestionService questionService;
    @Autowired
    private IMessageSendLogService messageSendLogService;
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ISurveyAnswerService answerService;

    @Autowired
    private ISurveyAnswerTimeService answerTimeService;

    @Autowired
    private IPrismaReportDataService prismaReportDataService;

    @Autowired
    @Lazy
    private ISurveyProjectUserPersonPermissionMappingService userPersonPermissionMappingService;

    @Autowired
    @Lazy
    private ISurveyOnLineReportService onLineReportService;

    @Autowired
    private RemoteSurveyCommonService surveyCommonService;

    @Autowired
    private SurveyIasSelectedQuestionMappingService iasSelectedQuestionMappingService;

    @Autowired
    private IUserService userService;

    @Autowired
    private ITencentParsingExcelService tencentParsingExcelService;

    @Autowired
    private IPrismaOrgaztionOpenQuestionDataService orgaztionOpenQuestionDataService;

    @Autowired
    private IPrismaOrganizationDemographicApprovalDataService prismaOrganizationDemographicApprovalDataService;

    @Autowired
    private ISurveyCustomDimensionService surveyCustomDimensionService;

    @Deprecated
    @GetMapping("/getSupportLanguageList")
    @ApiOperation(value = "获取支持的语言列表", notes = "获取支持的语言列表")
    public WebResponse getSupportLanguageList() {
        return WebResponse.returnSuccessData(assistantService.getSupportLanguageList());
    }

    @PostMapping("/sendInitDataMessage")
    @ApiOperation(value = "发送初始化数据消息消息", notes = "发送初始化数据消息消息")
    public WebResponse<Void> sendInitDataMessage(@RequestBody InitDataReq req) {
        assistantService.sendInitDataMessage(req);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/deleteSurveyPersonOrganizationMapping")
    @ApiOperation(value = "删除人员组织极速", notes = "删除人员组织关系", hidden = true)
    public WebResponse<Void> deleteSurveyPersonOrganizationMapping(@RequestBody RepairDataReq req) {
        for (String id : req.getIds()) {
            surveyPersonOrganizationMappingService.removeById(id);
        }
        return WebResponse.returnSuccess();
    }

    @PostMapping("/syncOrganizationDemographicApprovalData")
    @ApiOperation(value = "同步组织-人口标签维度赞成度数据到远程", notes = "同步组织-人口标签维度赞成度数据到远程")
    public WebResponse<Void> syncOrganizationDemographicApprovalData(@RequestParam("projectId") String projectIds,
                                                                     @RequestParam("remoteServerAddress") String remoteServerAddress,
                                                                     @RequestParam("token") String token) {
        List<String> projectList = Arrays.asList(projectIds.split(","));
        for (String projectId : projectList) {
            // 根据projectId查询所有数据ID
            List<String> ids = prismaOrganizationDemographicApprovalDataService.listIdsByProjectId(projectId);
            if (CollectionUtils.isEmpty(ids)) {
                return WebResponse.returnSuccess();
            }

            // 将ID分组，每10个一组
            List<List<String>> batches = Lists.partition(ids, 5);

            int count = 0;
            for (List<String> batch : batches) {
                log.info("syncOrganizationDemographicApprovalData index projectId:"+projectId+" " + (count++) + "/" + batches.size());
                // 根据ID批量查询数据
                List<PrismaOrganizationDemographicApprovalData> batchData =
                        prismaOrganizationDemographicApprovalDataService.listByIds(batch);

                if (CollectionUtils.isNotEmpty(batchData)) {
                    // 调用同步服务
                    tencentParsingExcelService.syncOrganizationDemographicApprovalData(batchData, remoteServerAddress, token);
                }
            }
        }
        return WebResponse.returnSuccess();
    }

    @PostMapping("/repairPersonDemographicMapping")
    @ApiOperation(value = "修复活动下人员分析因子", notes = "修复活动下人员分析因子", hidden = true)
    public WebResponse<Void> repairPersonDemographicMapping(@RequestBody RepairDataReq req) {
        assistantService.repairPersonDemographicMapping(req.getProjectId());
        return WebResponse.returnSuccess();
    }

    @PostMapping("/reCalculatePersonValid")
    @ApiOperation(value = "重新计算调研活动下人员有效性", notes = "重新计算调研活动下人员有效性")
    public WebResponse<Void> reCalculatePersonValid(@RequestBody RepairDataReq req) {
        SurveyQuestionnaire surveyQuestionnaire = questionnaireService.getOneByProjectId(req.getProjectId());
        PersonValidStrategy personValidStrategy = personValidStrategyMap.getPersonValidStrategyMap().get(surveyQuestionnaire.getReportType().name());
        projectService.recalculatePersonValid(req.getProjectId(), personValidStrategy);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/recoverInvestigatorData")
    @ApiOperation(value = "恢复调查者数据", notes = "恢复调查者数据", hidden = true)
    public WebResponse<Void> recoverInvestigatorData(@RequestBody RepairDataReq req) {
        assistantService.recoverInvestigatorData(req);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/recoverInvestigatorPersonData")
    @ApiOperation(value = "恢复调查者下测评者数据", notes = "恢复调查者下测评者数据", hidden = true)
    public WebResponse<Void> recoverInvestigatorPersonData(@RequestBody RepairDataReq req) {
        assistantService.recoverInvestigatorPersonData(req);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/recoverPersonAnswerData")
    @ApiOperation(value = "恢复测评者填答数据", notes = "恢复测评者填答数据", hidden = true)
    public WebResponse<Void> recoverPersonAnswerData(@RequestBody RepairDataReq req) {
        assistantService.recoverPersonAnswerData(req);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/updateSagReportFile")
    @ApiOperation(value = "更新报告文件数据", notes = "更新报告文件数据", hidden = true)
    public WebResponse<Void> updateSagReportFile(@RequestBody RepairDataReq req) {
        assistantService.updateSagReportFile(req);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/updatePersonAnswerStatus")
    @ApiOperation(value = "恢复测评者填答状态数据", notes = "恢复测评者填答状态数据", hidden = true)
    public WebResponse<Void> updatePersonAnswerStatus(@RequestBody RepairDataReq req) {
        assistantService.updatePersonAnswerStatus(req);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/deleteSurveyPersonInvestigatorMapping")
    @ApiOperation(value = "删除调查者测评者关系", notes = "删除调查者测评者关系", hidden = true)
    public WebResponse<Void> deleteSurveyPersonInvestigatorMapping(@RequestBody RepairDataReq req) {
        for (String id : req.getIds()) {
            surveyPersonInvestigatorMappingService.removeById(id);
        }
        return WebResponse.returnSuccess();
    }

    @PostMapping("/recoverSurveyPersonInvestigatorMapping")
    @ApiOperation(value = "恢复调查者测评者关系", notes = "恢复调查者测评者关系", hidden = true)
    public WebResponse<Void> recoverSurveyPersonInvestigatorMapping(@RequestBody RepairDataReq req) {
        for (String id : req.getIds()) {
            surveyPersonInvestigatorMappingService.recoverData(id);
        }
        return WebResponse.returnSuccess();
    }

    @GetMapping("/viewFile")
    @ApiOperation(value = "查看文件", notes = "查看文件")
    public void viewFile(@RequestParam("fileId") String fileId, @RequestParam("token") String token, HttpServletResponse response) {
        FeignSessionContext.setAuthorization(token);
        fileService.viewFile(fileId, response);
    }

    @Deprecated
    @GetMapping("/doTenantSurveyAnswerDownload")
    @ApiOperation(value = "租户填答数据下载", notes = "租户填答数据下载")
    public WebResponse<Void> doTenantSurveyAnswerDownload() {
        assistantService.doTenantSurveyAnswerDownload();
        return WebResponse.returnSuccess();
    }

    @Deprecated
    @PostMapping("/repairAnalyzeAnswers")
    @ApiOperation(value = "修复活动下已完成未解析答案的人员", notes = "修复活动下已完成未解析答案的人员")
    public WebResponse<Void> repairAnalyzeAnswers(@RequestParam("projectId") String projectId) {
        surveyPageAnswerService.repairAnalyzeAnswers(projectId);
        return WebResponse.returnSuccess();
    }

    @Deprecated
    @PostMapping("/doAnalyzeAnswers")
    @ApiOperation(value = "解析人员答案", notes = "解析人员答案")
    public WebResponse<Void> doAnalyzeAnswers(@RequestBody @Valid AnalyzeAnswerVo req) {
        surveyPageAnswerService.doAnalyzeAnswers(req);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/recoverSurveyQuestionData")
    @ApiOperation(value = "恢复题目数据", notes = "恢复题目数据", hidden = true)
    public WebResponse<Void> recoverSurveyQuestionData(@RequestBody RepairDataReq req) {
        assistantService.recoverSurveyQuestionData(req);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/deleteSurveyQuestionData")
    @ApiOperation(value = "删除题目数据", notes = "删除题目数据", hidden = true)
    public WebResponse<Void> deleteSurveyQuestionData(@RequestBody RepairDataReq req) {
        assistantService.deleteSurveyQuestionData(req);
        return WebResponse.returnSuccess();
    }

    @ApiOperation(value = "修复原始答案", notes = "修复原始答案", produces = "application/octet-stream", hidden = true)
    @PostMapping("/repairPersonAnswer")
    public WebResponse repairPersonAnswer(@RequestParam("excel") MultipartFile excel, @RequestParam("projectId") String projectId, @RequestParam("questionnaireId") String questionnaireId) {
        assistantService.repairPersonAnswer(projectId, questionnaireId, excel);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/repairReportGeneralComments")
    @ApiOperation(value = "修补总评", notes = "修补总评")
    public WebResponse repairReportGeneralComments(@RequestBody RepairGeneralCommentVo reqVo) {
        assistantService.repairReportGeneralComments(reqVo);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/repairReportDataGroupDetail")
    @ApiOperation(value = "修补报告团队明细", notes = "修补报告团队明细")
    public WebResponse repairReportDataGroupDetail(@RequestBody RepairGeneralCommentVo reqVo) {
        assistantService.repairReportDataGroupDetail(reqVo);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/repairReportScores")
    @ApiOperation(value = "修补报告分数", notes = "修补报告分数")
    public WebResponse repairReportScores(@RequestBody RepairGeneralCommentVo reqVo) {
        assistantService.repairReportScores(reqVo);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/resetReportGeneralComments")
    @ApiOperation(value = "删除修补总评锁", notes = "删除修补总评锁")
    public WebResponse resetReportGeneralComments(@RequestBody List<String> tenantIds) {
        assistantService.resetReportGeneralComments(tenantIds);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/deleteSurveyPersonValid")
    @ApiOperation(value = "删除人员有效性", notes = "删除人员有效性", hidden = true)
    public WebResponse<Void> deleteSurveyPersonValid(@RequestBody RepairDataReq req) {
        for (String id : req.getIds()) {
            surveyPersonValidService.removeById(id);
        }
        return WebResponse.returnSuccess();
    }

    /*@PostMapping("/thirdInfoSync")
    @ApiOperation(value = "发送调用第三方接口的中台信息", notes = "发送调用第三方接口的中台信息", hidden = true)
    public WebResponse thirdInfoSync( @RequestBody JSONObject req) {
        SyncThirdMessageDTO messageDTO = SyncThirdMessageDTO.builder().build();
        messageDTO.setTenantId(MultipleTenantDataSourceSessionContext.getTenantId());
        if(StringUtils.isBlank(req.getString("dataType"))){
            messageDTO.setDataType(DataSyncBusinessTypeEnum.Notice_Survey);
        } else {
            messageDTO.setDataType(EnumUtils.getEnum(DataSyncBusinessTypeEnum.class,req.getString("dataType"),null));
        }

        if(StringUtils.isBlank(req.getString("syncMode"))){
            messageDTO.setSyncMode(DataSyncModeEnum.NEW);
        } else {
            messageDTO.setSyncMode(EnumUtils.getEnum(DataSyncModeEnum.class,req.getString("syncMode"),null));
        }

        messageDTO.setOperatorUserId(SessionUser.SESSION_USER.get().getUserId());
        messageDTO.setData(req.getJSONObject("data").toJSONString());
        thirdInfoSyncApi.sync(messageDTO);
        return WebResponse.returnSuccess();
    }*/

    @PostMapping("/thirdInfoSync/callback")
    @ApiOperation(value = "中台信息回调", notes = "中台信息回调", hidden = true)
    public JSONObject thirdInfoSyncCallBack(@RequestBody JSONObject req) {
        log.info("接收到中台信息回调{} ", req.toJSONString());

        JSONObject res = JSONObject.parseObject("{\n" +
                "  success:true,\n" +
                "  code:0,\n" +
                "  message:\"数据同步成功\"\n" +
                "} \n");
        return res;
    }

    @PostMapping("/updatePrismaReportDataStatus")
    @ApiOperation(value = "修改PrismaReportData状态", notes = "修改PrismaReportData状态", hidden = true)
    public WebResponse<Void> updatePrismaReportDataStatus(@RequestBody RepairDataReq req) {
        assistantService.updatePrismaReportDataStatus(req);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/repairDemographicFullname")
    @ApiOperation(value = "修补总评", notes = "修补总评")
    public WebResponse repairAllProjectDemographicFullname(@RequestBody List<String> tenantIds) {
        assistantService.repairAllProjectDemographicFullname(tenantIds);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/sendNotSendReportMessage")
    @ApiOperation(value = "修补未发送的消息", notes = "修补未发送的消息")
    public WebResponse sendNotSendReportMessage(@RequestBody @NotNull List<String> projectIds) {
        for (String projectId : projectIds) {
            projectService.sendNotSendReportMessage(projectId);
        }
        return WebResponse.returnSuccess();
    }

    @PostMapping("/repairOptionId")
    @ApiOperation(value = "修补选项id", notes = "修补选项id")
    public WebResponse repairOptionId(@RequestBody @NotNull List<String> questionIds) {
        if (CollectionUtils.isEmpty(questionIds)) {
            return WebResponse.returnSuccess();
        }

        for (String questionId : questionIds) {
            questionService.repairOptionId(questionId);
        }
        return WebResponse.returnSuccess();
    }

    @PostMapping("/sendReportMessage")
    @ApiOperation(value = "指定文件发送邮件", notes = "指定文件发送邮件")
    public WebResponse sendReportMessage(@RequestBody @NotNull List<String> sagReportFileIds) {
        projectService.sendReportMessage(sagReportFileIds);
        return WebResponse.returnSuccess();
    }


    @PostMapping("/syncEarliestMessageStatus")
    @ApiOperation(value = "指定文件发送邮件", notes = "指定文件发送邮件")
    public WebResponse syncEarliestMessageStatus() {
        messageSendLogService.syncEarliestMessageStatus();
        return WebResponse.returnSuccess();
    }

    @PostMapping("/getStatisticsPrismaReportDataInfo")
    @ApiOperation(value = "获取调研细分报告统计信息", notes = "获取调研细分报告统计信息")
    public void getStatisticsPrismaReportDataInfo(HttpServletResponse response) throws IOException {
        EasyExcelParams easyExcelParams = new EasyExcelParams();
        easyExcelParams.setExcelNameWithoutExt("调研细分报告统计信息");
        easyExcelParams.setResponse(response);
        SheetData sheetData = new SheetData();
        //表头
        List<List<String>> header = new ArrayList<>();
        List<String> headerTenantId = new ArrayList<>();
        headerTenantId.add("租户ID");
        header.add(headerTenantId);
        List<String> headerTenantName = new ArrayList<>();
        headerTenantName.add("租户名称");
        header.add(headerTenantName);
        List<String> headerTenantLoginName = new ArrayList<>();
        headerTenantLoginName.add("租户登录名称");
        header.add(headerTenantLoginName);

        List<String> headerNumber = new ArrayList<>();
        headerNumber.add("总计");
        header.add(headerNumber);
        List<String> headerInitNumber = new ArrayList<>();
        headerInitNumber.add("待生成");
        header.add(headerInitNumber);
        List<String> headerScoreFailNumber = new ArrayList<>();
        headerScoreFailNumber.add("计算分数失败");
        header.add(headerScoreFailNumber);
        List<String> headerScoreSuccessNumber = new ArrayList<>();
        headerScoreSuccessNumber.add("计算分数成功");
        header.add(headerScoreSuccessNumber);
        List<String> headerReportFailNumber = new ArrayList<>();
        headerReportFailNumber.add("生成报告失败");
        header.add(headerReportFailNumber);
        List<String> headerReportSuccessNumber = new ArrayList<>();
        headerReportSuccessNumber.add("生成报告成功");
        header.add(headerReportSuccessNumber);
        List<String> headerUploadNumber = new ArrayList<>();
        headerUploadNumber.add("手动上传");
        header.add(headerUploadNumber);
        List<String> headerHistoryNumber = new ArrayList<>();
        headerHistoryNumber.add("历史对比");
        header.add(headerHistoryNumber);
        List<String> headerNotPrintNumber = new ArrayList<>();
        headerNotPrintNumber.add("报告待打印");
        header.add(headerNotPrintNumber);
        sheetData.setHead(header);
        // 数据
        List<List<Object>> dataList = new ArrayList<>();
        //已开通测评调研云的租户Id
        List<ProductOpenedTenantInfoVO> tenantInfoList = tenantService.getSagTenantInfoList();
        for (ProductOpenedTenantInfoVO tenantInfoVO : tenantInfoList) {
            try {
                List<Object> data = new ArrayList<>();
                data.add(tenantInfoVO.getTenantId());
                data.add(tenantInfoVO.getTenantName());
                data.add(tenantInfoVO.getTenantLoginName());
                String redisKey = RedisKeyConstant.STATISTICS_PRISMA_REPORT_DATA_INFO_REDIS_KEY + tenantInfoVO.getTenantId();
                String cacheString = redisUtil.get(redisKey);
                if (StringUtils.isNotBlank(cacheString)) {
                    StatisticsPrismaInfo statisticsPrismaInfo = JSONObject.parseObject(cacheString, StatisticsPrismaInfo.class);
                    if (statisticsPrismaInfo != null) {
                        data.add(statisticsPrismaInfo.getNumber());
                        data.add(statisticsPrismaInfo.getInitNumber());
                        data.add(statisticsPrismaInfo.getScoreFailNumber());
                        data.add(statisticsPrismaInfo.getScoreSuccessNumber());
                        data.add(statisticsPrismaInfo.getReportFailNumber());
                        data.add(statisticsPrismaInfo.getReportSuccessNumber());
                        data.add(statisticsPrismaInfo.getUploadNumber());
                        data.add(statisticsPrismaInfo.getHistoryNumber());
                        data.add(statisticsPrismaInfo.getNotPrintNumber());
                    }
                } else {
                    data.add("");
                    data.add("");
                    data.add("");
                    data.add("");
                    data.add("");
                    data.add("");
                    data.add("");
                    data.add("");
                    data.add("");
                }
                dataList.add(data);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        sheetData.setData(dataList);
        sheetData.setSheetName("调研细分报告统计信息");
        List<SheetData> sheetDataList = Collections.singletonList(sheetData);
        easyExcelParams.setSheetDatas(sheetDataList);
        ExcelUtil.dynamicExportExcel2007(easyExcelParams);
    }

    @PostMapping("/reprintReportFile")
    @ApiOperation(value = "重新打印报告文件", notes = "重新打印报告文件", hidden = true)
    public WebResponse<Void> reprintReportFile(@RequestBody RepairDataReq req) {
        assistantService.reprintReportFile(req);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/checkOverFixAtAnswerTimeout")
    @ApiOperation(value = "查询AT固定问券填答超时的人，并结束填答", notes = "查询AT固定问券填答超时的人，并结束填答", hidden = true)
    public WebResponse<Void> checkOverFixAtAnswerTimeout() {
        answerTimeService.checkOverFixAtAnswerTimeout();
        return WebResponse.returnSuccess();
    }

    @PostMapping("/checkOverAtReEnterPersons")
    @ApiOperation(value = "查询AT重新进入超时的人员数据，如果已经超时判定填答结束", notes = "查询AT重新进入超时的人员数据，如果已经超时判定填答结束", hidden = true)
    public WebResponse<Void> checkOverAtReEnterPersons() {
        answerTimeService.checkOverAtReEnterPersons();
        return WebResponse.returnSuccess();
    }

    @PostMapping("/recalculatePersonValid")
    @ApiOperation(value = "重新计算有效性", notes = "重新计算有效性", hidden = true)
    public WebResponse<Void> recalculatePersonValid(@RequestBody RecalculatePersonValidVo personValidVo) {
        List<SurveyQuestionnaire> questionnaires = questionnaireService.listByProjectId(personValidVo.getProjectId());
        if (CollectionUtils.isEmpty(questionnaires)) {
            return WebResponse.returnSuccess();
        }
        PersonValidStrategy personValidStrategy = personValidStrategyMap.getPersonValidStrategyMap().get(questionnaires.get(0).getReportType().name());
        if (personValidStrategy != null) {
            List<String> answeredPersonIds = personValidVo.getPersonIds();
            projectService.recalculatePersonValid(personValidVo.getProjectId(), answeredPersonIds, personValidStrategy);
        }
        return WebResponse.returnSuccess();
    }

    @PostMapping("/testFileName")
    @ApiOperation(value = "测试生成文件名称", notes = "测试生成文件名称", hidden = true)
    public WebResponse<String> testFileName(@RequestParam String prismaDataId,
                                            @RequestParam(required = false) LanguageTypeEnum languageType,
                                            @RequestParam(required = false) ReportStyleEnum reportStyle,
                                            @RequestParam(required = false) FileTypeEnum fileType,
                                            @RequestParam(required = false) FileTypeEnum subFileType) {
        PrismaReportData prd = prismaReportDataService.getById(prismaDataId);
        if (prd == null) {
            throw new BusinessException("prismaDataId错误", SurveyErrorCode.INVALID_PARAM);
        }
        if (reportStyle == null) {
            throw new BusinessException("reportStyle错误", SurveyErrorCode.INVALID_PARAM);
        }
        String fileName = null;
        if (reportStyle.getTypeEnum().isTencent()) {
            if (fileType == FileTypeEnum.ZIP) {
                if (subFileType == FileTypeEnum.PPTX) {
                    fileName = fileService.generateTencentPptZipFileName(prd);
                }

                if (subFileType == FileTypeEnum.XLSX) {
                    fileName = fileService.generateTencentExcelZipFileName(prd);
                }
            } else {
                if (languageType == null) {
                    throw new BusinessException("languageType错误", SurveyErrorCode.INVALID_PARAM);
                }

                fileName = fileService.generateTencentPptFileName(prd, reportStyle, languageType);
            }
        } else if (reportStyle.getTypeEnum().isNetease()) {
            if(fileType == FileTypeEnum.XLSX){
                fileName = fileService.generateNeteaseExcelFileName(prd);
            } else if(fileType == FileTypeEnum.PPTX){
                fileName = fileService.generateNeteasePptFileName(prd);
            } else if(fileType == FileTypeEnum.ZIP){
                if (subFileType == FileTypeEnum.PPTX) {
                    fileName = fileService.generateNeteasePptZipFileName(prd);
                }

                if (subFileType == FileTypeEnum.XLSX) {
                    fileName = fileService.generateNeteaseExcelZipFileName(prd);
                }
            }
        } else if(reportStyle.getTypeEnum() == StandardReportTypeEnum.INVESTIGATION_RESEARCH_CUSTOM
                || reportStyle.getTypeEnum() == StandardReportTypeEnum.INVESTIGATION_RESEARCH
                || reportStyle.getTypeEnum() == StandardReportTypeEnum.CULTURE_INVESTIGATION_RESEARCH ){
            if (fileType == FileTypeEnum.XLSX) {
                fileName = fileService.generateIasExcelOrPptFileName(prd, "敬满", languageType);
            } else if (fileType == FileTypeEnum.PPTX) {
                fileName = fileService.generateIasPptFileName(prd, languageType, reportStyle);
            } else if (fileType == FileTypeEnum.ZIP) {
                fileName = fileService.generateIasZipFileName(Lists.newArrayList(prd),"数据报表");
            }
        } else if (reportStyle.getTypeEnum().isEpson()) {

        }


        return WebResponse.returnSuccessData(fileName);
    }

    @PostMapping("/testMultiFileName")
    @ApiOperation(value = "测试生成文件名称", notes = "测试生成文件名称", hidden = true)
    public WebResponse<String> testMultiFileName(@RequestBody List<String> prismaDataIds) {
        String fileName = fileService.generateIasZipFileName(prismaReportDataService.listByIds(prismaDataIds),"数据报表");
        return WebResponse.returnSuccessData(fileName);
    }

    @PostMapping("/resolveRedisUserPersonPermission")
    @ApiOperation(value = "处理掉缓存的redis权限", notes = "处理掉缓存的redis权限", hidden = true)
    public WebResponse resolveRedisUserPersonPermission(String projectId, String personId) {
        userPersonPermissionMappingService.resolveRedisUserPersonPermission(projectId,personId);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/checkOnlineReportStatus")
    @ApiOperation(value = "检查在线报表状态", notes = "检查在线报表状态", hidden = true)
    public WebResponse checkOnlineReportStatus(String onlineReportId) {
        onLineReportService.checkAllComplete(onlineReportId);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/checkOnlineReportStatus/message")
    @ApiOperation(value = "检查在线报表状态", notes = "检查在线报表状态", hidden = true)
    public WebResponse messageCheckOnlineReportStatus(String onlineReportId) {
        onLineReportService.sendCheckStatusMessage(onlineReportId);
        return WebResponse.returnSuccess();
    }

    @PostMapping("/delCache")
    @ApiOperation(value = "删除key", notes = "删除key", hidden = true)
    public WebResponse delCache(String key) {
        redisUtil.del(key);
        return WebResponse.returnSuccess();
    }

    /**
     * 获取最小计算数量
     *
     * @return
     */
    @ApiOperation(value = "获取最小计算数量")
    @GetMapping("/getMinCalculateNum")
    WebResponse<Integer> getMinCalculateNum(){
        return WebResponse.returnSuccessData(surveyCommonService.getMinCalculateNum(MultipleTenantDataSourceSessionContext.getTenantId()).getData());
    }

    /**
     * 设置最小计算数量
     *
     * @param minCalculateNum
     * @return
     */
    @ApiOperation(value = "设置最小计算数量")
    @PostMapping("/setMinCalculateNum")
    WebResponse setMinCalculateNum(Integer minCalculateNum){
        surveyCommonService.setMinCalculateNum(MultipleTenantDataSourceSessionContext.getTenantId(),minCalculateNum);
        return WebResponse.returnSuccess();
    }


    @ApiOperation(value = "导入分发题", notes = "导入分发题")
    @PostMapping(value = "/importSelectQuestion")
    WebResponse<Void> importSelectQuestion(@RequestParam("excel") MultipartFile excel, @RequestParam("projectId") String projectId) {
        SurveyProject project = projectService.getById(projectId);
        if(project == null){
            throw new BusinessException(SurveyErrorCode.PROJECT_IS_NOT_EXIST);
        }

        SurveyQuestionnaire  questionnaire = questionnaireService.getOneByProjectId(projectId);
        if(questionnaire == null){
            throw new BusinessException(SurveyErrorCode.QUESTIONNAIRE_IS_NOT_EXIST);
        }

        //导入调研分发
        if(questionnaire.getReportType().isEmployee()){
            //分发题本数据
            List<ExportSelectQuestionVo> selectQuestionVos = ExcelUtil.readExcel(excel,ExportSelectQuestionVo.class,"题本关联");

            //分发数据
            List<Map<Integer, String>> selectOrgDemDatas = ExcelUtil.readMap(excel,"组合类型",new MapBaseExcelListener());

            //导入数据
            iasSelectedQuestionMappingService.importPartSelectQuestion(selectQuestionVos,selectOrgDemDatas,project,questionnaire);
        }

        return WebResponse.returnSuccess();
    }


    @ApiModelProperty(value = "获取一人一码填答链接")
    @GetMapping("/removeSimpleUserCache")
    public WebResponse removeSimpleUserCache(@RequestParam(required = false) String tenantId) {
        userService.removeSimpleUserCache(tenantId);
        return WebResponse.returnSuccess();
    }

    @ApiOperation(value = "导入维度分数数据", notes = "导入并保存维度分数数据")
    @PostMapping("/importTencentDimensionScore")
    public WebResponse importTencentDimensionScore(@RequestParam("localDirectoryPath") String localDirectoryPath,
                                                   @RequestParam(required = false, name = "remoteServerAddress") String remoteServerAddress,
                                                   @RequestParam(required = false, name = "token") String token) {
        List<ProjectVO> remoteProjectList = new ArrayList<>();
        Map<String, List<PrismaHistoryDataVO>> projectIdHistorysMap = new HashMap<>();
        Map<String, List<PrismaNormVO>> projectIdPrismaNormsMap = new HashMap<>();
        if (StringUtils.isNotBlank(remoteServerAddress) || StringUtils.isNotBlank(token)) {
            remoteProjectList = tencentParsingExcelService.getRemoteProjects(remoteServerAddress, token);
            for (ProjectVO projectVO : remoteProjectList) {
                projectIdHistorysMap.put(projectVO.getId(), tencentParsingExcelService.getRemotePrismaHistoryDatas(remoteServerAddress, token, projectVO.getId()));
                projectIdPrismaNormsMap.put(projectVO.getId(), tencentParsingExcelService.getRemotePrismaNorms(remoteServerAddress, token, projectVO.getId()));
            }
        }

        File directory = FileUtil.file(localDirectoryPath);
        if (!directory.isDirectory()) {
            return WebResponse.returnSuccess();
        }

        // 递归查找所有子目录中的Excel文件
        List<File> excelFiles = FileUtil.loopFiles(directory, file -> 
            file.isFile() && file.getName().contains(".xlsx")
        );

        Multimap<String, File> projectFilesMap = HashMultimap.create();
        for (File file : excelFiles) {
            String fileName = file.getName();
            if (StringUtils.isBlank(fileName)) {
                throw new BusinessException(SurveyErrorCode.UPLOAD_FILE_ERROR);
            }

            // 获取组织编码
            String[] parts = fileName.split("_");

            // 获取组织编码
            String orgCode = null;
            int orgIndex = 0;
            // 从第一个下划线后开始遍历
            for (int i = 1; i < parts.length; i++) {
                String part = parts[i];
                // 判断是否为纯字母或数字
                if (part.matches("[a-zA-Z0-9]+")) {
                    orgCode = part;
                    orgIndex = i;
                    break;
                }
            }

            if (StringUtils.isBlank(orgCode)) {
                throw new BusinessException(fileName + "无法匹配出组织code", SurveyErrorCode.PARAM_ERROR);
            }

            // 获取活动
            String projectName = ArrayUtil.join(ArrayUtil.sub(parts, 0, orgIndex), "_");
            projectFilesMap.put(projectName, file);
        }

        for (String projectName : projectFilesMap.keySet()) {
            tencentParsingExcelService.parsingExcel(projectName, new ArrayList<>(projectFilesMap.get(projectName)), remoteProjectList, projectIdHistorysMap, projectIdPrismaNormsMap);
        }
        return WebResponse.returnSuccess();
    }

    @ApiOperation(value = "解析腾讯开放题数据", notes = "解析腾讯开放题数据")
    @PostMapping("/analyseTencentOpenQuestion")
    public WebResponse analyseTencentOpenQuestion(@RequestParam("projectId") String projectId) {
        HashMultimap<String, ImportOpenQuestionResultVo> orgDatasMap = tencentParsingExcelService.analyseTencentOpenQuestion(projectId);
        if (!orgDatasMap.isEmpty()) {
            orgaztionOpenQuestionDataService.saveOpenQuestionDataFromVO(projectId, orgDatasMap);
        }
        return WebResponse.returnSuccess();
    }

    @PostMapping("/saveOrganizationDemographicApprovalData")
    @ApiOperation(value = "保存组织-人口标签维度赞成度数据", notes = "保存组织-人口标签维度赞成度数据")
    public WebResponse<Void> saveOrganizationDemographicApprovalData(@RequestBody List<PrismaOrganizationDemographicApprovalData> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return WebResponse.returnSuccess();
        }
        // Group by projectId, organization, and mainlandType
        Map<String, List<PrismaOrganizationDemographicApprovalData>> groupedData = datas.stream()
            .collect(Collectors.groupingBy(data ->
                data.getProjectId() + "|" + data.getOrganization() + "|" + data.getMainlandType().name()
            ));
        for (Map.Entry<String, List<PrismaOrganizationDemographicApprovalData>> entry : groupedData.entrySet()) {
            String[] keys = entry.getKey().split("\\|");
            String projectId = keys[0];
            String organization = keys[1];
            String mainlandType = keys[2];
            prismaOrganizationDemographicApprovalDataService.deleteAndSaveBatch(projectId, organization, mainlandType, entry.getValue());
        }
        return WebResponse.returnSuccess();
    }

    @PostMapping("/countOrganizationDemographicApprovalDataByProjectId")
    @ApiOperation(value = "根据项目ID统计组织-人口标签维度赞成度数据中data的数量", notes = "根据项目ID统计组织-人口标签维度赞成度数据中data的数量")
    public WebResponse<Integer> countOrganizationDemographicApprovalDataByProjectId(@RequestParam("projectId") String projectId) {
        // 根据projectId查询所有数据ID
        List<String> ids = prismaOrganizationDemographicApprovalDataService.listIdsByProjectId(projectId);
        if (CollectionUtils.isEmpty(ids)) {
            return WebResponse.returnSuccessData(0);
        }

        int totalDataCount = 0;
        log.info("开始统计项目 {} 的PrismaOrganizationDemographicApprovalData数据，共 {} 条记录", projectId, ids.size());

        // 逐条查询数据并统计data字段的数量，避免一次性加载大量JSON数据
        for (int i = 0; i < ids.size(); i++) {
            String id = ids.get(i);
            try {
                // 每次只查询一条记录
                List<PrismaOrganizationDemographicApprovalData> dataList =
                        prismaOrganizationDemographicApprovalDataService.listByIds(Arrays.asList(id));

                if (CollectionUtils.isNotEmpty(dataList)) {
                    PrismaOrganizationDemographicApprovalData data = dataList.get(0);
                    if (data.getData() != null) {
                        totalDataCount += data.getData().size();
                    }
                }

                // 每处理100条记录输出一次进度日志
                if ((i + 1) % 100 == 0) {
                    log.info("已处理 {}/{} 条记录，当前统计总数: {}", i + 1, ids.size(), totalDataCount);
                }
            } catch (Exception e) {
                log.error("处理记录ID {} 时发生异常: {}", id, e.getMessage(), e);
                // 继续处理下一条记录
            }
        }

        log.info("项目 {} 的PrismaOrganizationDemographicApprovalData数据统计完成，总data数量: {}", projectId, totalDataCount);
        return WebResponse.returnSuccessData(totalDataCount);
    }

    @ApiOperation("删除重复的自定义维度")
    @PostMapping("/questionnaire/dimension/removeDuplicate")
    public WebResponse removeDuplicateDimensions(@RequestParam String questionnaireId) {
        int deletedCount = surveyCustomDimensionService.removeDuplicateDimensions(questionnaireId);
        return WebResponse.returnSuccess();
    }

    @ApiOperation(value = "导入维度分数数据", notes = "导入并保存维度分数数据")
    @PostMapping("/remoteAnalyseTencentOpenQuestion")
    public WebResponse remoteAnalyseTencentOpenQuestion(@RequestParam(required = false, name = "remoteServerAddress") String remoteServerAddress,
                                                   @RequestParam(required = false, name = "token") String token) {

        List<String> projectPrefixs = Lists.newArrayList("CDG",
                "CSIG",
                "IEG",
                "PCG",
                "S1",
                "S2",
                "S3",
                "TEG",
                "WXG",
                "腾讯集团");

        List<ProjectVO> remoteProjectList = new ArrayList<>();
        if (StringUtils.isNotBlank(remoteServerAddress) || StringUtils.isNotBlank(token)) {
            remoteProjectList = tencentParsingExcelService.getRemoteProjects(remoteServerAddress, token);
        }

        remoteProjectList = remoteProjectList.stream()
                .filter(d->!d.getProjectName().getZh_CN().contains("原始"))
                .filter(d->!d.getProjectName().getZh_CN().contains("测试"))
                .filter(d-> Lists.newArrayList("23","24").stream().anyMatch(p->d.getProjectName().getZh_CN().contains(p)))
                .filter(d->{
            return projectPrefixs.stream().anyMatch(p->d.getProjectName().getZh_CN().contains(p));
        }).collect(Collectors.toList());

        for(ProjectVO projectVO : remoteProjectList){
            log.info("Starting remote analysis for project id: {}", projectVO.getId());
            tencentParsingExcelService.remoteAnalyseTencentOpenQuestion(remoteServerAddress,token,projectVO.getId());
            log.info("Completed remote analysis for project id: {}", projectVO.getId());
        }
        return WebResponse.returnSuccess();
    }

    @ApiOperation(value = "导入维度分数数据", notes = "导入并保存维度分数数据")
    @PostMapping("/checkTencentDimensionScore")
    public WebResponse checkTencentDimensionScore(@RequestParam("localDirectoryPath") String localDirectoryPath,
                                                   @RequestParam(required = false, name = "remoteServerAddress") String remoteServerAddress,
                                                   @RequestParam(required = false, name = "token") String token) throws InterruptedException {
        List<ProjectVO> remoteProjectList = new ArrayList<>();
        Map<String, List<PrismaHistoryDataVO>> projectIdHistorysMap = new HashMap<>();
        Map<String, List<PrismaNormVO>> projectIdPrismaNormsMap = new HashMap<>();
        if (StringUtils.isNotBlank(remoteServerAddress) || StringUtils.isNotBlank(token)) {
            remoteProjectList = tencentParsingExcelService.getRemoteProjects(remoteServerAddress, token);
        }

        List<File> excelFiles = FileUtil.loopFiles(localDirectoryPath, file ->
                file.isFile() && file.getName().contains(".xlsx")
        );
        ExecutorService executor = Executors.newFixedThreadPool(5);

        List<String> errors = checkExcelInParallel(excelFiles,remoteProjectList);

        return WebResponse.returnSuccessData(errors);
    }

    public List<String> checkExcelInParallel(List<File> excelFiles, List<ProjectVO> remoteProjectList) throws InterruptedException {
        // 创建固定大小为 5 的线程池
        ExecutorService executorService = Executors.newFixedThreadPool(5);

        // 存储所有任务
        List<Callable<List<String>>> tasks = new ArrayList<>();

        // 将 excelFiles 拆分成 5 份
        int totalSize = excelFiles.size();
        int numThreads = 5;
        int chunkSize = (int) Math.ceil((double) totalSize / numThreads);

        for (int i = 0; i < numThreads; i++) {
            int start = i * chunkSize;
            int end = Math.min(start + chunkSize, totalSize);

            // 处理空段
            if (start >= totalSize) {
                break;
            }

            List<File> subList = new ArrayList<>(excelFiles.subList(start, end));

            String tenantId = MultipleTenantDataSourceSessionContext.getTenantId();
            // 创建任务
            Callable<List<String>> task = () -> {
                MultipleTenantDataSourceSessionContext.setTenantId(tenantId);
                // 每个线程调用 checkExcel
                return tencentParsingExcelService.checkExcel(subList, remoteProjectList);
            };

            tasks.add(task);
        }

        // 提交所有任务并获取 Future 列表
        List<Future<List<String>>> futures = executorService.invokeAll(tasks);

        // 关闭线程池（不再接收新任务，但等待已提交任务完成）
        executorService.shutdown();

        // 合并所有结果
        List<String> allErrors = new ArrayList<>();
        for (Future<List<String>> future : futures) {
            try {
                allErrors.addAll(future.get());
            } catch (ExecutionException e) {
                // 处理任务执行异常
                System.err.println("Task execution failed: " + e.getCause().getMessage());
                // 可以选择抛出异常或记录日志
            }
        }

        return allErrors;
    }
}
