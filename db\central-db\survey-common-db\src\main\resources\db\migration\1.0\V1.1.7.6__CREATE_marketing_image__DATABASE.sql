DROP TABLE IF EXISTS "public"."marketing_image";
CREATE TABLE marketing_image (
	id varchar(36) NOT NULL,
	name varchar(50) NULL,
	file_url varchar(36) NULL,
	sort int4 default 999,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__marketing_image___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."marketing_image"."name" IS '名称';
COMMENT ON COLUMN "public"."marketing_image"."file_url" IS '图片地址';
COMMENT ON COLUMN "public"."marketing_image"."sort" IS '排序号';
COMMENT ON TABLE  "public"."marketing_image" IS '营销图表';
