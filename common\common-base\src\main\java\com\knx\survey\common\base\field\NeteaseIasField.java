package com.knx.survey.common.base.field;

import com.knx.survey.model.I18NString;
import com.knx.survey.model.SagI18NString;

public class NeteaseIasField {

    public static I18NString NAME_NAME = new SagI18NString("名称", "Name");

    public static I18NString APPROVAL_ABBR_NAME = new SagI18NString("赞成", "Agreement");

    public static I18NString NEUTRAL_ABBR_NAME = new SagI18NString("中立", "Neutral");

    public static I18NString DISAPPROVAL_ABBR_NAME = new SagI18NString("不赞成", "Disagreement");

    public static I18NString APPROVAL_NAME = new SagI18NString("赞成百分比", "Approval");

    public static I18NString NEUTRAL_NAME = new SagI18NString("中立百分比", "Neutrality");

    public static I18NString DISAPPROVAL_NAME = new SagI18NString("不赞成百分比", "Disapproval");

    public static I18NString INDEX_NAME = new SagI18NString("指数", "Index");

    public static I18NString EEI_NAME = new SagI18NString("敬业度指数", "EEI");

    public static I18NString EEI_ABBR_NAME = new SagI18NString("敬业度", "Engagement");

    public static I18NString ESI_NAME = new SagI18NString("满意度指数", "ESI");

    public static I18NString ESI_ABBR_NAME = new SagI18NString("满意度", "Satisfaction");

    public static I18NString EMPTY_NAME = new SagI18NString("", "");

    public static I18NString COLON_NAME = new SagI18NString("：", ": ");

    public static I18NString COMMA_NAME = new SagI18NString("，", ", ");

    public static I18NString HIGHEST_NAME = new SagI18NString("最高", "");

    public static I18NString LOWEST_NAME = new SagI18NString("最低", "");

    public static I18NString DEPARTMENT_NAME = new SagI18NString("部门", "Department");

    public static I18NString DEMOGRAPHIC_NAME = new SagI18NString("标签", "Demographic");

    public static I18NString INVITE_NUMBER_NAME = new SagI18NString("发放人数", "Invite Number");

    public static I18NString FINISH_NUMBER_NAME = new SagI18NString("填答人数", "Finish Number");

    public static I18NString FINISH_RATE_NAME = new SagI18NString("填答率", "Valid Rate");

    public static I18NString VALID_NUMBER_NAME = new SagI18NString("有效应答数", "Valid Number");

    public static I18NString VALID_ANSWER_NUMBER_NAME = new SagI18NString("有效填答人数", "Valid Number");

    public static I18NString VALID_RATE_NAME = new SagI18NString("有效应答率", "Valid Rate");


    public static I18NString COMPANY_WHOLE = new SagI18NString("活动整体", "");

    public static I18NString SUPERIOR_DEPARTMENT_NAME = new SagI18NString("上级部门", "Superior Department");

    public static I18NString ANALYSIS_OBJECT_NAME = new SagI18NString("分析主体", "Analysis Object");

    public static I18NString ROUND_BRACKET_LEFT_NAME = new SagI18NString("（", "(");

    public static I18NString ROUND_BRACKET_RIGHT_NAME = new SagI18NString("）", ")");

    public static I18NString DRIVING_FACTOR_NAME = new SagI18NString("驱动因素", "");

    public static I18NString QUESTION_NAME = new SagI18NString("题目", "Item");

    public static I18NString ABSOLUTE_SCORE_NAME = new SagI18NString("绝对分值", "");

    public static I18NString CORRELATION_NAME = new SagI18NString("相关系数", "Correlation");
}
