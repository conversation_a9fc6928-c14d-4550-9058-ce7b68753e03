package com.knx.survey.common.util;

import com.knx.common.redis.RedisUtil;
import com.knx.survey.common.base.field.RedisKeyConstant;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ExportCacheUtil.java
 * @Description TODO
 * @createTime 2022年01月18日 14:03:00
 */
@Slf4j
public class ExportCacheUtil {

    private final static Integer TIME_LIMIT = 30;

    public static void putCache(RedisUtil redisUtil, String requestId, String fileId) {
        redisUtil.set(getRedisKey(requestId), fileId);
        redisUtil.expire(getRedisKey(requestId), TIME_LIMIT * 60);
    }

    public static void putNameCache(RedisUtil redisUtil, String requestId, String fileId) {
        redisUtil.set(getFileNameRedisKey(requestId), fileId);
        redisUtil.expire(getFileNameRedisKey(requestId), TIME_LIMIT * 60);
    }

    public static String getFileId(RedisUtil redisUtil, String requestId) {
        return redisUtil.get(getRedisKey(requestId));
    }

    public static String getNameCache(RedisUtil redisUtil, String requestId) {
        return redisUtil.get(getFileNameRedisKey(requestId));
    }

    private static String getRedisKey(String requestId) {
        return RedisKeyConstant.EXCEL_EXPORT_REDIS_KEY + requestId;
    }

    private static String getFileNameRedisKey(String requestId) {
        return RedisKeyConstant.EXCEL_NAME_EXPORT_REDIS_KEY + requestId;
    }
}
