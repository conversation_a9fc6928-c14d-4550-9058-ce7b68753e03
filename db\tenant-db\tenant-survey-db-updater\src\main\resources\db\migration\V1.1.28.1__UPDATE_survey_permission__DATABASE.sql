DROP TABLE IF EXISTS "public"."survey_permission";
CREATE TABLE survey_permission (
                                   id VARCHAR ( 36 ) NOT NULL,
                                   name json DEFAULT NULL,
                                   only_view_self BOOL DEFAULT FALSE,
                                   add_master_manager BOOL DEFAULT FALSE,
                                   add_sub_manager_view_progress BOOL DEFAULT FALSE,
                                   add_sub_manager_create_report BOOL DEFAULT FALSE,
                                   standard_products json DEFAULT NULL,
                                   combination_products json DEFAULT NULL,
                                   exclusive_products json DEFAULT NULL,
                                   create_by VARCHAR ( 36 ) NULL,
                                   create_time TIMESTAMP ( 3 ) NULL DEFAULT now( ),
                                   update_time TIMESTAMP ( 3 ) NULL DEFAULT now( ),
                                   last_update_by VARCHAR ( 36 ) NULL,
                                   is_deleted BOOL NULL DEFAULT FALSE,
                                   app_id VARCHAR ( 36 ) NULL,
                                   CONSTRAINT "___pk__survey_permission___" PRIMARY KEY ( id )
);
COMMENT ON TABLE "public"."survey_permission" IS '权限表';
COMMENT ON COLUMN "public"."survey_permission"."name" IS '权限名称';
COMMENT ON COLUMN "public"."survey_permission"."only_view_self" IS '仅查看本人创建的活动及报告';
COMMENT ON COLUMN "public"."survey_permission"."add_master_manager" IS '添加活动主管理员';
COMMENT ON COLUMN "public"."survey_permission"."add_sub_manager_view_progress" IS '添加子管理员查看进度(仅调研)';
COMMENT ON COLUMN "public"."survey_permission"."add_sub_manager_create_report" IS '添加子管理员生成报告(仅调研)';
COMMENT ON COLUMN "public"."survey_permission"."standard_products" IS '标准产品';
COMMENT ON COLUMN "public"."survey_permission"."combination_products" IS '组合产品';
COMMENT ON COLUMN "public"."survey_permission"."exclusive_products" IS '专属产品';
