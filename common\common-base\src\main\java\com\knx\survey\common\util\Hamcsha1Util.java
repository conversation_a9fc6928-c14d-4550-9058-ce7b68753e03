package com.knx.survey.common.util;

import lombok.SneakyThrows;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;
import java.util.Random;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2021/10/14 10:40
 */
public class Hamcsha1Util {

    private static final String MAC_NAME = "HmacSHA1";
    private static final String ENCODING = "UTF-8";

    //二行制转字符串
    public static String byte2hex(byte[] b) {

        StringBuilder hs = new StringBuilder();
        String stmp;
        for (int n = 0; b != null && n < b.length; n++) {
            stmp = Integer.toHexString(b[n] & 0XFF);
            if (stmp.length() == 1) {
                hs.append('0');
            }
            hs.append(stmp);
        }
        return hs.toString().toUpperCase();
    }

    @SneakyThrows
    public static String hmacSHA1Encrypt(String encryptText, String encryptKey) {
        byte[] data = encryptKey.getBytes(ENCODING);
        // 根据给定的字节数组构造一个密钥,第二参数指定一个密钥算法的名称
        SecretKey secretKey = new SecretKeySpec(data, MAC_NAME);
        // 生成一个指定 Mac 算法 的 Mac 对象
        Mac mac = Mac.getInstance(MAC_NAME);
        // 用给定密钥初始化 Mac 对象
        mac.init(secretKey);

        byte[] text = encryptText.getBytes(ENCODING);
        // 完成 Mac 操作
        return byte2hex(mac.doFinal(text));
    }


    public static void main(String[] args) throws Exception {
        String s = genSign("xlz7xBBgeSwc9y3XJzml0g", "GbEQdaoYEncUmuAU5Fgb7w");
        String s2 = genCSIDTSign("2023092105063932262", "d37c0a99fa3efe5526f94a07c839270f");
        System.out.println(s);
    }

    /**
     * 生成签名字段
     *
     * @param apiKey
     * @param secretKey
     * @return
     * @throws Exception
     */
    public static String genSign(String apiKey, String secretKey) throws Exception {
        long now = System.currentTimeMillis() / 1000;
        int rdm = Math.abs(new Random().nextInt());
        String plainText = String.format("a=%s&b=%d&c=%d", apiKey, now, rdm);
        byte[] hmacDigest = HmacSha1(plainText, secretKey);
        byte[] signContent = new byte[hmacDigest.length + plainText.getBytes().length];
        System.arraycopy(hmacDigest, 0, signContent, 0, hmacDigest.length);
        System.arraycopy(plainText.getBytes(), 0, signContent, hmacDigest.length,
                plainText.getBytes().length);
        return encodeToBase64(signContent).replaceAll("[\\s*\t\n\r]", "");
    }

    /**
     * 生成 base64 编码
     *
     * @param binaryData
     * @return
     */
    public static String encodeToBase64(byte[] binaryData) {
        String encodedStr = Base64.getEncoder().encodeToString(binaryData);
        return encodedStr;
    }

    /**
     * 生成 hmacsha1 签名
     *
     * @param binaryData
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] HmacSha1(byte[] binaryData, String key) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), "HmacSHA1");
        mac.init(secretKey);
        byte[] HmacSha1Digest = mac.doFinal(binaryData);
        return HmacSha1Digest;
    }

    /**
     * 生成 hmacsha1 签名
     *
     * @param plainText
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] HmacSha1(String plainText, String key) throws Exception {
        return HmacSha1(plainText.getBytes(), key);
    }


    public static String genCSIDTSign(String apiKey, String secretKey) throws Exception {
        long now = System.currentTimeMillis() / 1000;
        StringBuilder rdm = new StringBuilder();
        for(int i = 0; i < 10; i++){
            rdm.append(Math.abs(new Random().nextInt(10)));
        }
        String plainText = String.format("a=%s&b=%d&c=%s", apiKey, now, rdm);
        byte[] hmacDigest = HmacSha1(plainText, secretKey);
        byte[] signContent = new byte[hmacDigest.length + plainText.getBytes().length];
        System.arraycopy(hmacDigest, 0, signContent, 0, hmacDigest.length);
        System.arraycopy(plainText.getBytes(), 0, signContent, hmacDigest.length,
                plainText.getBytes().length);
        String ss = encodeToBase64(hmacDigest);
        return encodeToBase64(signContent).replaceAll("[\\s*\t\n\r]", "");
    }
}
