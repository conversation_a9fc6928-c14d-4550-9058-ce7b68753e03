package com.knx.survey.common.base.field;

public class RedisKeyConstant {

    public static final String SAG = "sag";

    public static final String COLON = ":";

    public static final Integer REDIS_JOB_EXPIRE_TIME = 24 * 60 * 60;

    public static final Integer REDIS_EXPIRE_TIME = 2 * 60 * 60;

    public static final Integer REDIS_EXPIRE_ONE_DAY_TIME = 24 * 60 * 60;

    public static final String CHECK_PROJECT_REDIS_KEY = SAG + COLON + "check_project";

    public static final String CREATE_REPORT_REDIS_KEY = SAG + COLON + "create_report";

    public static final String TENANT_THIRD_TASK_REDIS_KEY = SAG + COLON + "tenant_third_task";

    public static final String REPORT_PRINT_REDIS_KEY = SAG + COLON + "report_print";

    public static final String SYNC_MESSAGE_STATUS_REDIS_KEY = SAG + COLON + "sync_message_status";

    public static final String PERSON_NAIRE_AT_TIME_REDIS_KEY = SAG + COLON + "person_naire_at_time";

    public static final String SYNC_TENANT_INDUSTRY_REDIS_KEY = SAG + COLON + "sync_tenant_industry";

    public static final String CHECK_GROUP_REPORT_PAY_REDIS_KEY = SAG + COLON + "check_group_report_pay";
    public static final String REPORT_PRINT_ERROR_REDIS_KEY = SAG + COLON + "report_print_error";

    public static final String PRISMA_REPORT_DATA_CALCULATE_SCORE_REDIS_KEY = SAG + COLON + "prisma_report_data_calculate_score";

    public static final String PRISMA_COMPARE_REPORT_DATA_CALCULATE_SCORE_REDIS_KEY = SAG + COLON + "prisma_compare_report_data_calculate_score";

    public static final String PRISMA_REPORT_DATA_CREATE_REPORT_REDIS_KEY = SAG + COLON + "prisma_report_data_create_report";

    public static final String PRISMA_REPORT_PRINT_REDIS_KEY = SAG + COLON + "prisma_report_print";

    public static final String PRISMA_REPORT_PRINT_ERROR_REDIS_KEY = SAG + COLON + "prisma_report_print_error";

    public static final String PROJECT_PUBLIC_CODE_SHORT_URL_REDIS_KEY = SAG + COLON + "project_public_code_short_url";

    public static final String PROJECT_ORG_CAPTCHA_CODE_SHORT_URL_REDIS_KEY = SAG + COLON + "project_org_captcha_code_short_url";

    public static final String PRISMA_WORD_CLOUD_PRINT_REDIS_KEY = SAG + COLON + "prisma_word_cloud_print";

    public static final String PRISMA_DEFAULT_CODE = "KFSHD-prisma";

    public static final String CUSTOMIZE = "CUSTOMIZE";

    public static final String CUSTOMIZE_QUESTION_CODE_SEQUENCE_REDIS_KEY = SAG + COLON + "customize_question_code_sequence";

    public static final String PROJECT_CAPTCHA_CODE_SHORT_URL_REDIS_KEY = SAG + COLON + "project_captcha_code_short_url";

    public static final String EXCEL_EXPORT_REDIS_KEY = SAG + COLON + "sag_export_cache_request_";

    public static final String EXCEL_NAME_EXPORT_REDIS_KEY = SAG + COLON + "sag_export_name_cache_request_";

    public static final String SHORT_URL_REDIS_KEY = SAG + COLON + "short_url_";

    public static final String ORDER_NO_REDIS_KEY = SAG + COLON + "order_no";

    public static final String VIEW_REPORT_DEDUCT_AMOUNT_REDIS_KEY = SAG + COLON + "view_report_deduct_amount";

    public static final String ORDER_YEAR_AMOUNT_KEY = SAG + COLON + "order_year_amount";

    public static final String TENANT_SURVEY_ANSWER_DOWNLOAD_REDIS_KEY = SAG + COLON + "tenant_survey_answer_download";

    public static final String TENANT_SURVEY_ANSWER_DOWNLOAD_SMS_REDIS_KEY = SAG + COLON + "tenant_survey_answer_download_sms_";

    public static final String TENANT_SMS_REDIS_KEY = SAG + COLON + "tenant_sms_%s_%s";

    public static final String SURVEY_PROJECT_HAS_EXTRA_QUESTION_REDIS_KEY = SAG + COLON + "project_has_extra_question_";

    public static final String GENERAL_COMMENTS_REDIS_KEY = SAG + COLON + "general_comments";

    public static final String SURVEY_PERSON_ANSWER_IMPORT_REDIS_KEY = SAG + COLON + "survey_person_answer_import_";

    public static final String SURVEY_MIGRATION_SYNC_PROJECT_REDIS_KEY = SAG + COLON + "survey_migration_sync_project_";

    public static final String SURVEY_PERSON_INVITE_IMPORT_REDIS_KEY = SAG + COLON + "survey_person_invite_import_";

    public static final String STATISTICS_PRISMA_REPORT_DATA_INFO_REDIS_KEY = SAG + COLON + "statistics_prisma_report_data_info_";

    public static final String SURVEY_INVESTIGATOR_NORM_SETTING_IMPORT_REDIS_KEY = SAG + COLON + "survey_investigator_norm_setting_import_";

    public static final String SURVEY_INVESTIGATOR_NORM_SETTING_UPDATE_REDIS_KEY = SAG + COLON + "survey_investigator_norm_setting_update_";

    public static final String PERSON_QUESTION_SORT = SAG + COLON + "person_question_sort";

    public static final String ANSWER_STATUS_CHECK_KEY = SAG + COLON + "answer_status_check";

    public static final String SEND_REPORT_EMAIL_REDIS_KEY = SAG + COLON + "send-report-email";

    /**
     * sso填答租户开关缓存
     */
    public static String SSO_ANSWER_CACHE_KEY = SAG + COLON + "sso:answer:";

    /**
     * 服务实例缓存
     */
    public static String SERVICE_INSTANCE_CACHE_KEY = SAG + COLON + "service:";

    /**
     * 活动用户与填答人员权限关联
     */
    public static final String PROJECT_USER_PERSON_PERMISSION_REDIS_KEY = SAG + COLON + "project_user_person_permission";

    /**
     * 活动用户与填答人员权限关联
     */
    public static final String USER_PERSON_PERMISSION_REDIS_KEY = SAG + COLON + "user_person_permission";

    /**
     * 是否开通消息发送租户开关缓存
     */
    public static String OPEN_MESSAGE_SEND_CACHE_KEY = SAG + COLON + "open:message:send:";

    /**
     * 是否开通消息发送租户开关缓存
     */
    public static String REPAIR_REPORT_DATA_GROUP_DETAIL_CACHE_KEY = SAG + COLON + "repair_report_data_group_detail";

    /**
     * 是否开通邮箱发送租户开关缓存
     */
    public static String OPEN_MAIL_SEND_CACHE_KEY = SAG + COLON + "open:mail:send:";
}
