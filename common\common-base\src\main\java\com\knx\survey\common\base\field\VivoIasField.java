package com.knx.survey.common.base.field;

import com.knx.survey.model.I18NString;
import com.knx.survey.model.SagI18NString;

public class VivoIasField {
    public static I18NString EMPTY_NAME = new SagI18NString("", "");
    public static I18NString EEI_NAME_VIVO = new SagI18NString("敬业指标", "EEI");
    public static I18NString INDEX_NAME_VIVO = new SagI18NString("指标", "INDEX");
    public static I18NString DIMENSION = new SagI18NString("维度", "DIMENSION");
    public static I18NString QUESTION_NAME = new SagI18NString("题目", "ITEM");
    public static I18NString SUPERIOR_DEPARTMENT_NAME = new SagI18NString("上级组织名称", "Superior Department Name");
    public static I18NString N1_N2_DEPARTMENT_NAME = new SagI18NString("N-1/N-2组织名称", "N1/N2 Department Name");
    public static I18NString COMPANY_NAME_VIVO = new SagI18NString("公司值", "Company");
    public static I18NString VALID_ANSWER_NUMBER_PERCENTAGE_NAME = new SagI18NString("有效人数及占比", "Effective Responses & Percentage");
    public static I18NString CORRELATION_NAME = new SagI18NString("相关系数", "Correlation");
    public static I18NString IS_LOWEST_ITEM = new SagI18NString("是否最低分", "Among Lowest 10 Scores");
    public static I18NString IS_LAST = new SagI18NString("是", "Y");
    public static I18NString SCORE_TOP_10_NAME = new SagI18NString("得分前10", "Score Top 10");
    public static I18NString SCORE_BOTTOM_10_NAME = new SagI18NString("得分后10", "Score Bottom 10");
    public static I18NString CORRELATION_TOP_10_NAME = new SagI18NString("相关前10", "Correlation Top 10");
    public static I18NString LAST_10_NAME = new SagI18NString("后10", " Bottom 10");
    public static I18NString TOP_10_NAME = new SagI18NString("前10", " Top 10");
    public static I18NString FOCUS_NAME = new SagI18NString("关注度", "Focus");

    public static I18NString EEI_LEVEL_NAME = new SagI18NString("敬业度", "EEI");

    public static I18NString ESI_LEVEL_NAME = new SagI18NString("满意度", "ESI");

    public static I18NString CAESURA_SIGN = new SagI18NString("、", ", "); ;
    public static I18NString COLON_NAME = new SagI18NString("：", ": ");

    /**
     * 选项
     */
    public static I18NString OPTION_NAME = new SagI18NString("选项", "Option");

    /**
     * 数量
     */
    public static I18NString NUM_NAME = new SagI18NString("数量", "Frequency");
}
