DROP TABLE IF EXISTS project_report_dimension;
CREATE TABLE project_report_dimension
(
    "id"                              VARCHAR(36)  NOT NULL,
    "project_id"                      VARCHAR(36)  NOT NULL,
    "parent_name"                     VARCHAR(50)  NOT NULL,
    "code"                            VARCHAR(50)  NOT NULL,
    "is_select"                       bool ,
    "questionnaire_id"                VARCHAR(36)  NOT NULL,
    "report_template_id"              VARCHAR(36)  NOT NULL,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___project_report_dimension___" PRIMARY KEY ("id")
);

COMMENT ON TABLE  "public"."project_report_dimension" IS '活动报告维度表';
COMMENT ON COLUMN "public"."project_report_dimension"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."project_report_dimension"."code" IS '子维度编码';
COMMENT ON COLUMN "public"."project_report_dimension"."is_select" IS '是否可选';
COMMENT ON COLUMN "public"."project_report_dimension"."questionnaire_id" IS '问卷id';
COMMENT ON COLUMN "public"."project_report_dimension"."report_template_id" IS '报告模板id';