package com.knx.survey.common.util;

import com.fasterxml.jackson.databind.util.StdDateFormat;
import com.knx.survey.common.base.field.SurveyField;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/7/28 17:44
 */

@Slf4j
public class DateUtil {
    private static String formatDateTime = "yyyy-MM-dd HH:mm:ss";

    private static String formatSimpleDateTime = "yyyy-MM-dd HH:mm";

    private static String formatDate = "yyyy-MM-dd";
    private static String shortFormatDate = "MM-dd";
    private static String formatTimeZone = "yyyy-MM-dd'T'HH:mm:ss.SSS Z";
    private static String formatTime2Millisecond = "yyyy-MM-dd HH:mm:ss.SSS";
    private static String formatYYMMDDDate = "yyyyMMdd";
    private static String formatYYMMDate = "yyyy-MM";
    private static String formatDateOne = "yyyy/MM/dd";
    private static String formatDateZhcn = "yyyy年MM月dd日";
    private static String formatDateYearMonth = "yyyy-MM";
    private static String formatDateYear = "yyyy";
    private static String formatDateTwo = "yyyy.MM.dd";
    private static String formatDatePureDigital = "yyyyMMddHHmmss";
    private static String formatPureDatetimePatternNoSecond = "yyyyMMddHHmm";

    /**
     * 将时区日期格式 yyyy-MM-dd'T'HH:mm:ss.SSS Z 解析为Date
     *
     * @param dateString
     * @return
     */

    public static Date parseDateOrTimeZone(String dateString) {

        if (StringUtils.isBlank(dateString)) {
            return null;
        }

        Date date = null;
        try {
            dateString = dateString.replace("Z", " UTC");
            date = new SimpleDateFormat(formatTimeZone).parse(dateString);
        } catch (Exception e) {
            log.debug("时区格式解析错误:[{}]", dateString);
        }


        if (date == null) {
            try {
                date = new SimpleDateFormat(formatDate).parse(dateString);
            } catch (Exception e) {
                log.debug("年月日格式解析错误:[{}]", dateString);
            }
        }

        if (date == null) {
            try {
                date = new SimpleDateFormat(formatDateYearMonth).parse(dateString);
            } catch (Exception e) {
                log.debug("年月格式解析错误:[{}]", dateString);
            }
        }
        return date;
    }

    /**
     * 将时区日期格式 yyyy-MM-dd'T'HH:mm:ss.SSS Z 解析为Date
     *
     * @param dateString
     * @return
     */

    public static Date parseDateTimeOrTimeZone(String dateString) {

        Date date = null;
        try {
            date = new StdDateFormat().withTimeZone(TimeZone.getDefault()).parse(dateString);
        } catch (ParseException e) {
            log.info("时区格式解析错误:[{}]", dateString);
        }

        if (date == null) {
            try {
                date = new SimpleDateFormat(formatDateTime).parse(dateString);
            } catch (ParseException e) {
                log.info("年月日时分秒格式解析错误:[{}]", dateString);
            }
        }

        if (date == null) {
            try {
                date = new SimpleDateFormat(formatSimpleDateTime).parse(dateString);
            } catch (ParseException e) {
//                e.printStackTrace();
                log.info("年月日时分格式解析错误:[{}]", dateString);
            }
        }
        return date;
    }

    /**
     * 格式化为 yyyy-MM-dd HH:mm:ss
     *
     * @param date
     * @return
     */
    public static String formatNormTime(Date date) {
        String format = "";
        if (date == null) {
            return format;
        }
        try {
            format = new SimpleDateFormat(formatDateTime).format(date);
        } catch (Exception e) {
        }
        return format;
    }


    /**
     * 格式化为 yyyy-MM-dd
     *
     * @param date
     * @return
     */
    public static String formatNoMinutesAndSeconds(Date date) {
        String format = "";
        if (date == null) {
            return format;
        }
        try {
            format = new SimpleDateFormat(formatDate).format(date);
        } catch (Exception e) {
        }
        return format;
    }

    /**
     * 格式化为 yyyy/MM/dd
     *
     * @param date
     * @return
     */
    public static String formatNoMinutesAndSecondsOne(Date date) {
        if (date == null) {
            return StringUtils.EMPTY;
        }
        String format = new SimpleDateFormat(formatDateOne).format(date);
        return format;
    }

    /**
     * 格式化为 yyyy.MM.dd
     *
     * @param date
     * @return
     */
    public static String formatNoMinutesAndSecondsTwo(Date date) {
        if (date == null) {
            return StringUtils.EMPTY;
        }
        String format = new SimpleDateFormat(formatDateTwo).format(date);
        return format;
    }

    /**
     * 格式化为 yyyy
     *
     * @param date
     * @return
     */
    public static String formatYear(Date date) {
        if (date == null) {
            return StringUtils.EMPTY;
        }
        String format = new SimpleDateFormat(formatDateYear).format(date);
        return format;
    }


    /**
     * 格式化为 yyyy-MM-dd
     *
     * @param date
     * @return
     */
    public static String formatMonthAndDay(Date date) {
        String format = new SimpleDateFormat(shortFormatDate).format(date);
        return format;
    }

    /**
     * 格式化为 yyyy-MM-dd HH:mm:ss.SSS
     *
     * @param date
     * @return
     */
    public static String formatTime2Millisecond(Date date) {
        String format = new SimpleDateFormat(formatTime2Millisecond).format(date);
        return format;
    }

    /**
     * 格式化为 yyyyMMdd
     *
     * @param date
     * @return
     */
    public static String formatYYYYMMDDDate(Date date) {
        String format = new SimpleDateFormat(formatYYMMDDDate).format(date);
        return format;
    }

    /**
     * 格式化为 yyyyMMdd
     *
     * @param date
     * @return
     */
    public static String formatYYYYMMDate(Date date) {
        if (date == null) {
            return SurveyField.EMPTY_STRING;
        }
        String format = new SimpleDateFormat(formatYYMMDate).format(date);
        return format;
    }

    /**
     * 格式化为 yyyyMMdd
     *
     * @param date
     * @return
     */
    public static String formatYYYYMMDDZhcn(Date date) {
        String format = new SimpleDateFormat(formatDateZhcn).format(date);
        return format;
    }

    /**
     * 格式化为 yyyyMMddHHmmss
     */
    public static String formatPureDigitalDate(Date date) {
        if (date == null) {
            return SurveyField.EMPTY_STRING;
        }
        String format = new SimpleDateFormat(formatDatePureDigital).format(date);
        return format;
    }

    /**
     * 格式化为 yyyyMMddHHmm
     *
     * @param date
     * @return
     */
    public static String formatPureDatetimePatternNoSecond(Date date) {
        if (date == null) {
            return SurveyField.EMPTY_STRING;
        }
        String format = new SimpleDateFormat(formatPureDatetimePatternNoSecond).format(date);
        return format;
    }


    public static Date asDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date asDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDate asLocalDate(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static LocalDateTime asLocalDateTime(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static Date formateDateBegin(Date date) {
        String dateStr = formatNoMinutesAndSeconds(date) + " 00:00:00";
        return parseDateTimeOrTimeZone(dateStr);
    }

    public static Date formateDateEnd(Date date) {
        String dateStr = formatNoMinutesAndSeconds(date) + " 23:59:59";
        return parseDateTimeOrTimeZone(dateStr);
    }

    public static Date getYearStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(calendar.get(Calendar.YEAR), 0, 1, 0, 0, 0);
        return calendar.getTime();
    }

    public static Date getYearEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(calendar.get(Calendar.YEAR), 11, 31, 23, 59, 59);
        return calendar.getTime();
    }

    public static Date getMonthStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 1, 0, 0, 0);
        return calendar.getTime();
    }

    public static Date getMonthEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(getMonthStart(date));
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.SECOND, -1);
        return calendar.getTime();
    }

    /**
     * 获取指定日期后的时间
     *
     * @param date
     * @param days
     * @return
     */
    public static Date addDays(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, days);
        return calendar.getTime();
    }


    public static void main(String[] args) {
        String s = formatMonthAndDay(new Date());
        System.out.println(s);
        Date date = parseDateOrTimeZone("1995-06-24 00:00:00");
        System.out.println(date);
        Date date2 = parseDateOrTimeZone("30");
        System.out.println(date2);
        System.out.println(formatPureDigitalDate(new Date()));
    }

}
