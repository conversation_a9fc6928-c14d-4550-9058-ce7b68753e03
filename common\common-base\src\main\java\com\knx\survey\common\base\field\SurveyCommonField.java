package com.knx.survey.common.base.field;

import com.knx.survey.model.enums.IasReportDescriptionModule;
import com.knx.survey.model.enums.QuestionTypeEnum;
import com.knx.survey.model.enums.StandardReportTypeEnum;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;

import java.util.*;

/**
 * User: 林超
 * Date: 2020/3/24
 * Time: 11:03 AM
 */
public class SurveyCommonField extends SagField {
    public static final String STANDARD_QUESTION_ID = "standard_question_id";
    public static final String STANDARD_QUESTIONNAIRE_ID = "standard_questionnaire_id";
    public static final String CREATE_TIME = "createTime";
    public static final String CREATE_BY = "createBy";
    public static final String UPDATE_TIME = "updateTime";
    public static final String LAST_UPDATE_BY = "lastUpdateBy";
    public static final String _COPY = "_Copy";
    public static final String EMPTY = "";
    public static final String[] COPY_IGNORE_PROPERTIES = new String[]{SurveyCommonField.CREATE_TIME, SurveyCommonField.CREATE_BY,
            SurveyCommonField.UPDATE_TIME, SurveyCommonField.LAST_UPDATE_BY};

    public static final String ABILITY_ASSESSMENT_QUESTIONNAIRE = "TZTZ-EAA-AT";

    public static final String PARENT_ID_DEFAULE_VALUE = "0";

    public static final String ADAPTATION_DETAILS_CONFIG = "adaptationDetailsConfig";
    public static final String DETAILED_SCORE_CONFIG = "detailedScoreConfig";
    public static final String FIT_CONFIG = "fitConfig";
    public static final String FRONT_COVER_CONFIG = "frontCoverConfig";
    public static final String DIRECTORY_CONFIG = "directoryConfig";
    public static final String OVERVIEW_CONFIG = "overviewConfig";
    public static final String PAYMENT_CONFIG = "paymentConfig";
    public static final String RESPONSE_STYLE_CONFIG = "responseStyleConfig";
    public static final String DEVELOPMENT_SUGGESTIONS_CONFIG = "developmentSuggestionsConfig";
    public static final String INTERVIEW_SUGGESTIONS_CONFIG = "interviewSuggestionsConfig";
    public static final String TRAINING_SUGGESTIONS_CONFIG = "trainingSuggestionsConfig";
    public static final String LEADERSHIP_STYLE_CONFIG = "leadershipStyleConfig";
    public static final String DECISION_MAKING_STYLE_CONFIG = "decisionMakingStyleConfig";
    public static final String EPA = "epa";
    public static final String AT = "at";
    public static final String TIP = "tip";
    public static final String AMA = "ama";
    public static final String CSI = "csi";
    public static final String CA = "ca";
    public static final String CSI_STANDARD = "csis";
    public static final String PERSON_ID = "?personId=";
    public static final String INVESTIGATOR_ID = "?investigatorId=";
    public static final String GROUP_ID = "?groupId=";
    public static final String PROJECT_ID = "&projectId=";
    public static final String HIGH = "高";
    public static final String MEDIUM = "中";
    public static final String LOW = "低";
    public static final String HIGH_RISK = "高风险";
    public static final String MEDIUM_RISK = "中风险";
    public static final String LOW_RISK = "低风险";
    public static final String LOW_LEVEL = "低水平";
    public static final String MEDIUM_LEVEL = "中水平";
    public static final String HIGH_LEVEL = "高水平";
    public static final String TOTAL_SCORE = "总分";
    public static final String NAME = "姓名";
    public static final String OPERATE = "操作";
    public static final String TIMETOFILLIN = "填答时间";
    public static final String ALL_ACTIVITIES = "全部活动";
    public static final String VIEW_REPORT = "查看报告";
    public static final String Fit = "适配度";
    public static final String ADAPTATION_LEVEL = "适配水平";
    public static final String BEST_MATCH_TRAIT = "最匹配特质";
    public static final String LEAST_MATCH_TRAIT = "最不匹配特质";
    public static final Integer NUM_ACCOUNT = 50;


    public static final String PRISMA_REPORT_ID = "?prismaReportId=";
    public static final String PRISMA_REPORT_LANGUAGE = "&language=";
    public static final String QUESTIONNAIRE_ID = "&questionnaireId=";
    public static final String AT_MODEL = "&atModel=";

    //应答风格code，特质差异和Ca报告需要进行剔除
    public static final String RESPONSE_STYLE_CODE = "YDFG-5L-EPA-025";

    public static final String SAMPLE_TEST = "sample test";

    public static final String UNDERSCORE = "_";

    public static final String GROUP_STRING = "GROUP";

    public static final String COMMA = ",";

    public static final String _360_XWBX = "360-XWBX";

    public static final String _360 = "360";
    public static final String PRISMA = "PRISMA";
    public static final String OTHER = "OTHER";

    public static final String COPY = "复制";

    public static final String EN_COPY = "COPY";

    public static final String TRUE = "是";

    public static final String FALSE = "否";

//    public static final String PREFACE = "preface";
//    public static final String BACK_COVER = "backCover";
//    public static final String FITNESS_LEVEL = "fitnessLevel";
//    public static final String TRAIT_DIFFERENCE = "traitDifference";
//    public static final String PERSONAL_DEVELOPMENT = "personalDevelopment";
//    public static final String EPILOGUE = "epilogue";
//    public static final String APPENDIX = "appendix";
//    public static final String POLICY_DECISION = "policyDecision";

    public static final String ASSESSMENT = "ASSESSMENT";
    public static final String PLATFORM_ANNOUNCEMENT = "平台公告";
    public static final String TENANT_LIST = "租户列表";

    public static final String ANSWER_DOWNLOAD = "answerDownload";

    public static final String HYPHEN = "-";

    public static final List<QuestionTypeEnum> SCALE_QUESTION_TYPE = new ArrayList<>();
    public static final List<QuestionTypeEnum> FORCED_ELECTION_QUESTION_TYPE = new ArrayList<>();
    public static final List<QuestionTypeEnum> OTHER_QUESTION_TYPE = new ArrayList<>();

    public static final String LEADERSHIP_STYLE = "leadershipStyle";
    public static final String DECISION_MAKING_STYLE = "decisionMakingStyle";
    public static final String LAP_GROUP_TRAIT_DIFFERENCE_PARENTS = "caLpaGroupTraitDifferenceParents";
    public static final String OVERVIEW = "OVERVIEW";
    public static final String PARENT_DIMENSIONS ="parentDimensions";
    public static final Map<StandardReportTypeEnum,List<IasReportDescriptionModule>> SPECIAL_REPORT_TYPE_MAP =new HashMap<>();
    static {
        SPECIAL_REPORT_TYPE_MAP.put(StandardReportTypeEnum.TENCENT_INVESTIGATION_RESEARCH_CUSTOM,
                Arrays.asList(IasReportDescriptionModule.IAS_METHODOLOGY,
                        IasReportDescriptionModule.CALCULATION_LOGIC_EXPLANATION,
                        IasReportDescriptionModule.IAS_REALIZE_CONDITION));
        SPECIAL_REPORT_TYPE_MAP.put(StandardReportTypeEnum.NETEASE_INVESTIGATION_RESEARCH_CUSTOM,
                Arrays.asList(IasReportDescriptionModule.IAS_METHODOLOGY,
                        IasReportDescriptionModule.NOUNS_INTERPRETATION,
                        IasReportDescriptionModule.NORMAL_DESCRIPTION,
                        IasReportDescriptionModule.FOUR_WINDOW_DESCRIPTION));
        SPECIAL_REPORT_TYPE_MAP.put(StandardReportTypeEnum.EPSON_INVESTIGATION_RESEARCH_CUSTOM,
                Arrays.asList(IasReportDescriptionModule.IAS_METHODOLOGY,
                        IasReportDescriptionModule.RELEVANT_ANALYTICAL_NOTES,
                        IasReportDescriptionModule.FOUR_WINDOW_DESCRIPTION));
        SPECIAL_REPORT_TYPE_MAP.put(StandardReportTypeEnum.INVESTIGATION_RESEARCH_CUSTOM,
                Arrays.asList(IasReportDescriptionModule.COVER,
                        IasReportDescriptionModule.IAS_METHODOLOGY,
                        IasReportDescriptionModule.FOUR_WINDOW_DESCRIPTION,
                        IasReportDescriptionModule.NOUNS_INTERPRETATION,
                        IasReportDescriptionModule.RELEVANT_ANALYTICAL_NOTES,
                        IasReportDescriptionModule.CALCULATION_LOGIC_EXPLANATION,
                        IasReportDescriptionModule.NORMAL_DESCRIPTION,
                        IasReportDescriptionModule.IAS_REALIZE_CONDITION,
                        IasReportDescriptionModule.FOUR_WINDOW_DESCRIPTION,
                        IasReportDescriptionModule.ADVANTAGE_DISADVANTAGE,
                        IasReportDescriptionModule.BACK_COVER));
        SPECIAL_REPORT_TYPE_MAP.put(StandardReportTypeEnum.INVESTIGATION_RESEARCH,
                Arrays.asList(IasReportDescriptionModule.COVER,
                        IasReportDescriptionModule.IAS_METHODOLOGY,
                        IasReportDescriptionModule.FOUR_WINDOW_DESCRIPTION,
                        IasReportDescriptionModule.NOUNS_INTERPRETATION,
                        IasReportDescriptionModule.RELEVANT_ANALYTICAL_NOTES,
                        IasReportDescriptionModule.CALCULATION_LOGIC_EXPLANATION,
                        IasReportDescriptionModule.NORMAL_DESCRIPTION,
                        IasReportDescriptionModule.IAS_REALIZE_CONDITION,
                        IasReportDescriptionModule.FOUR_WINDOW_DESCRIPTION,
                        IasReportDescriptionModule.ADVANTAGE_DISADVANTAGE,
                        IasReportDescriptionModule.BACK_COVER));
        SPECIAL_REPORT_TYPE_MAP.put(StandardReportTypeEnum.DP_INVESTIGATION_RESEARCH_CUSTOM,
                Arrays.asList(IasReportDescriptionModule.COVER,
                        IasReportDescriptionModule.IAS_METHODOLOGY,
                        IasReportDescriptionModule.FOUR_WINDOW_DESCRIPTION,
                        IasReportDescriptionModule.NOUNS_INTERPRETATION,
                        IasReportDescriptionModule.RELEVANT_ANALYTICAL_NOTES,
                        IasReportDescriptionModule.CALCULATION_LOGIC_EXPLANATION,
                        IasReportDescriptionModule.NORMAL_DESCRIPTION,
                        IasReportDescriptionModule.IAS_REALIZE_CONDITION,
                        IasReportDescriptionModule.FOUR_WINDOW_DESCRIPTION,
                        IasReportDescriptionModule.ADVANTAGE_DISADVANTAGE,
                        IasReportDescriptionModule.ACTION_PLAN,
                        IasReportDescriptionModule.BACK_COVER));

    }

    static {
        SCALE_QUESTION_TYPE.add(QuestionTypeEnum.SCALE);
    }

    static {
        FORCED_ELECTION_QUESTION_TYPE.add(QuestionTypeEnum.FORCED_ELECTION);
        FORCED_ELECTION_QUESTION_TYPE.add(QuestionTypeEnum.FOUR_FORCED_ELECTION);
    }

    static {
        OTHER_QUESTION_TYPE.add(QuestionTypeEnum.SINGLE);
        OTHER_QUESTION_TYPE.add(QuestionTypeEnum.JUDGE);
        OTHER_QUESTION_TYPE.add(QuestionTypeEnum.MULTIPLE_CHOICE);
        OTHER_QUESTION_TYPE.add(QuestionTypeEnum.SORTING_PROBLEM);
        OTHER_QUESTION_TYPE.add(QuestionTypeEnum.PROPORTION);
        OTHER_QUESTION_TYPE.add(QuestionTypeEnum.PROPORTION_MULTIPLE);
        OTHER_QUESTION_TYPE.add(QuestionTypeEnum.MATRIX_CHOICE);
        OTHER_QUESTION_TYPE.add(QuestionTypeEnum.MATRIX_DROP_DOWN);
        OTHER_QUESTION_TYPE.add(QuestionTypeEnum.ESSAY_QUESTION);
        OTHER_QUESTION_TYPE.add(QuestionTypeEnum.DESCRIBE);
        //OTHER_QUESTION_TYPE.add(QuestionTypeEnum.PAGE_SPLIT);
        OTHER_QUESTION_TYPE.add(QuestionTypeEnum.FATHER_AND_SON);
    }
}
