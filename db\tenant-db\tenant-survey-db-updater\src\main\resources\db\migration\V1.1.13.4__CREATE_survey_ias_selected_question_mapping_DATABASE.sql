DROP TABLE IF EXISTS survey_ias_selected_question_mapping;
CREATE TABLE survey_ias_selected_question_mapping
(
    "id"                              VARCHAR(36)  NOT NULL,
    "project_id"                      VARCHAR(36)  NOT NULL,
    "question_id"                     json  NOT NULL,
    "organization_id"                 VARCHAR(36)  ,
    "organization_code"               VARCHAR(50) ,
    "demographic_id"                  json  ,
    "key"                             VARCHAR(1024),
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___survey_ias_selected_question_mapping___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."survey_ias_selected_question_mapping" IS '调研分发题和组织人口信息学的mapping';
COMMENT ON COLUMN "public"."survey_ias_selected_question_mapping"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_ias_selected_question_mapping"."question_id" IS '问题id';
COMMENT ON COLUMN "public"."survey_ias_selected_question_mapping"."organization_id" IS '组织id';
COMMENT ON COLUMN "public"."survey_ias_selected_question_mapping"."organization_code" IS '组织编码';
COMMENT ON COLUMN "public"."survey_ias_selected_question_mapping"."demographic_id" IS '人口信息学id';
COMMENT ON COLUMN "public"."survey_ias_selected_question_mapping"."key" IS '记录唯一编码';