/*人员统计信息默认值表*/
DROP TABLE IF EXISTS "public"."survey_person_demographic_preset";
CREATE TABLE  "public"."survey_person_demographic_preset"
(
    "id"                            VARCHAR(36) NOT NULL,
    "person_id"                     VARCHAR(36),
    "standard_demographic_id"       VARCHAR(36),
    "project_id"                    VARCHAR(36),
    "value"                         VARCHAR(100),
    "is_editable"                   BOOL DEFAULT FALSE,
    "create_by"                     VARCHAR(36),
    "create_time"                   TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                   TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                VARCHAR(36),
    "is_deleted"                    BOOL         DEFAULT FALSE,
    "app_id"                        VARCHAR(36),
    CONSTRAINT  "___pk__survey_person_demographic_preset___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_person_demographic_preset"."person_id" IS '人员ID';
COMMENT ON COLUMN "public"."survey_person_demographic_preset"."standard_demographic_id" IS '标准人口统计学ID ';
COMMENT ON COLUMN "public"."survey_person_demographic_preset"."project_id" IS '活动ID ';
COMMENT ON COLUMN "public"."survey_person_demographic_preset"."value" IS '人口统计学值 ';
COMMENT ON COLUMN "public"."survey_person_demographic_preset"."is_editable" IS '是否可编辑';
COMMENT ON TABLE "public"."survey_person_demographic_preset" IS '人员统计信息默认值表';

