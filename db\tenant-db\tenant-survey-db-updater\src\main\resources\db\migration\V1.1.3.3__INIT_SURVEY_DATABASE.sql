
DROP TABLE IF EXISTS survey_organization_parent_mapping;
CREATE TABLE survey_organization_parent_mapping
(
    "id"                              VARCHAR(36)  NOT NULL,
    "project_id"                              VARCHAR(36)  NOT NULL,
    "organization_id"                 VARCHAR(50)  NOT NULL,
    "parent_organization_id"          VARCHAR(50)  NOT NULL,
    "distance"                        int4 NOT NULL,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___survey_organization_parent_mapping___" PRIMARY KEY ("id")
);

COMMENT ON TABLE  "public"."survey_organization_parent_mapping" IS '敬业度调研组织mapping表';
COMMENT ON COLUMN "public"."survey_organization_parent_mapping"."organization_id" IS '组织id';
COMMENT ON COLUMN "public"."survey_organization_parent_mapping"."parent_organization_id" IS '父组织id';
COMMENT ON COLUMN "public"."survey_organization_parent_mapping"."distance" IS '与父组织距离';

DROP TABLE IF EXISTS survey_organization;
CREATE TABLE survey_organization
(
    "id"                              VARCHAR(36)  NOT NULL,
    "project_id"                              VARCHAR(36)  NOT NULL,
    "code"                            VARCHAR(50)  NOT NULL,
    "description"                     VARCHAR(100)  ,
    "name"                            VARCHAR(50)  NOT NULL,
    "distance"                        int4 NOT NULL,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___survey_organization___" PRIMARY KEY ("id")
);

COMMENT ON TABLE  "public"."survey_organization" IS '敬业度调研组织mapping表';
COMMENT ON COLUMN "public"."survey_organization"."code" IS '组织编码';
COMMENT ON COLUMN "public"."survey_organization"."description" IS '组织描述';
COMMENT ON COLUMN "public"."survey_organization"."name" IS '组织名称';