DROP TABLE IF EXISTS prisma_correlation_coefficient_combination;
CREATE TABLE prisma_correlation_coefficient_combination
(
    "id"                              VARCHAR(36)  NOT NULL,
    "prisma_report_data_id"           VARCHAR(36)  NOT NULL,
    "name"                            json NOT NULL,
    "question_ids"                    json DEFAULT NULL,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___prisma_correlation_coefficient_combination___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."prisma_correlation_coefficient_combination" IS 'prisma相关系数组合表';
COMMENT ON COLUMN "public"."prisma_correlation_coefficient_combination"."prisma_report_data_id" IS 'prisma报告ID';
COMMENT ON COLUMN "public"."prisma_correlation_coefficient_combination"."name" IS '组合名称';
COMMENT ON COLUMN "public"."prisma_correlation_coefficient_combination"."question_ids" IS '组合下题目ID列表';

CREATE INDEX ___idx_prisma_correlation_coefficient_combination_prisma_report_data_id___ ON public.prisma_correlation_coefficient_combination(prisma_report_data_id);


DROP TABLE IF EXISTS prisma_correlation_coefficient_combination_detail;
CREATE TABLE prisma_correlation_coefficient_combination_detail
(
    "id"                              VARCHAR(36)  NOT NULL,
    "prisma_correlation_coefficient_combination_id"  VARCHAR(36)  NOT NULL,
    "type"                            VARCHAR(50) NOT NULL,
    "dimension_code"                  VARCHAR(50) DEFAULT NULL,
    "question_id"                     VARCHAR(36) DEFAULT NULL,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___prisma_correlation_coefficient_combination_detail___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."prisma_correlation_coefficient_combination_detail" IS 'prisma相关系数组合明细表';
COMMENT ON COLUMN "public"."prisma_correlation_coefficient_combination_detail"."prisma_correlation_coefficient_combination_id" IS 'prisma相关系数组合ID';
COMMENT ON COLUMN "public"."prisma_correlation_coefficient_combination_detail"."dimension_code" IS '维度编码';
COMMENT ON COLUMN "public"."prisma_correlation_coefficient_combination_detail"."question_id" IS '题目ID';

CREATE INDEX ___idx_prisma_correlation_coefficient_combination_detail_prisma_correlation_coefficient_combination_id___ ON public.prisma_correlation_coefficient_combination_detail(prisma_correlation_coefficient_combination_id);
