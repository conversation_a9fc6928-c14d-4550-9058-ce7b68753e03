package com.knx.bean.model.enums;

import lombok.Getter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

/**
 * User: 林超
 * Date: 2020/6/6
 * Time: 2:11 下午
 * <p>
 * 肯豆交易类型
 */
@Getter
@FieldNameConstants
@ToString(callSuper = true)
public enum KnxBeanTransactionTypeEnum {
    RECHARGE("充值"),
    CANCEL_RECHARGE("取消充值"),
    CREATE_PROJECT("创建活动"),
    VIEW_REPORT("查看报告"),
    DOWNLOAD_REPORT("下载报告"),
    RECHARGE_CARD("充值卡"),
    SMS("发短信"),
    EMAIL("邮件"),
    BUY_PRISMA_NORM("购买常模"),
    PACKAGE_YEAR_REPORT("包年工具"),
    REPORT_INTERPRETATION("报告解读"),
    GIVE_K<PERSON>("赠送肯豆"),
    MESSAGE_RECHARGE("短信充值"),
    CANCEL_MESSAGE_RECHARGE("取消短信充值");
    private String name;

    private KnxBeanTransactionTypeEnum(String name) {
        this.name = name;
    }

}
