package com.knx.bean.service.impl;

import com.knx.bean.client.RemoteCacheUserService;
import com.knx.bean.client.RemoteTenantService;
import com.knx.bean.dao.mapper.KnxCardMapper;
import com.knx.bean.model.dto.client.SystemUserSimpleVO;
import com.knx.bean.model.dto.client.TenantDTO;
import com.knx.bean.model.dto.client.UserDTO;
import com.knx.bean.model.entity.KnxCard;
import com.knx.bean.model.vo.CardResultExcelVO;
import com.knx.bean.model.vo.CardResultVO;
import com.knx.bean.model.vo.CardSelectVO;
import com.knx.bean.service.api.IKnxCardService;
import com.knx.bean.service.field.KnxBeanField;
import com.knx.bean.service.util.CodeUtil;
import com.knx.common.base.utils.KnxListUtils;
import com.knx.common.base.web.response.PageRequest;
import com.knx.common.util.DateUtil;
import com.knx.survey.common.base.BaseServiceImpl;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/10/16 13:41
 */

@Service
@Slf4j
@Transactional
public class KnxCardService extends BaseServiceImpl<KnxCardMapper, KnxCard> implements IKnxCardService {

    @Autowired
    private RemoteTenantService remoteTenantService;

    @Autowired
    private RemoteCacheUserService remoteCacheUserService;

    @Autowired
    private KnxCardMapper knxCardMapper;

    @Override
    @SneakyThrows
    public PageRequest pageList(CardSelectVO cardSelectVO) {
        PageRequest<KnxCard> pageRequest = cardSelectVO.getPageRequest();
        selectData(cardSelectVO);
        PageRequest page = knxCardMapper.pageList(pageRequest, cardSelectVO);
        List<KnxCard> records = page.getRecords();
        records = records.stream().sorted(Comparator.comparing(KnxCard::getCreateTime).reversed()).collect(Collectors.toList());
        List<CardResultVO> collect = getCardResultVOS(records);
        page.setRecords(collect);
        return page;
    }

    private List<CardResultVO> getCardResultVOS(List<KnxCard> records) {
        List<String> tenantIdList = records.stream().map(KnxCard::getTenantId).collect(Collectors.toList());
        List<String> createByIds = records.stream().map(KnxCard::getCreateBy).collect(Collectors.toList());
        List<TenantDTO> tenantList = new ArrayList<>();
        if (!KnxListUtils.isListEmpty(tenantIdList)) {
            tenantList = remoteTenantService.knxTenantBatchListByIds(tenantIdList).getData();
        }
        List<SystemUserSimpleVO> users = remoteCacheUserService.getSystemUserByIds(createByIds).getData();
        List<CardResultVO> list = new ArrayList<>();
        for (KnxCard knxCard : records) {
            CardResultVO cardResultVO = new CardResultVO();
            BeanUtils.copyProperties(knxCard, cardResultVO);
            String create = knxCard.getCreateBy();
            SystemUserSimpleVO user = users.stream().filter(u -> Objects.equals(u.getId(), create)).findFirst().orElse(null);
            if (user != null) {
                cardResultVO.setCreateName(user.getUsername());
            }

            String tenantId = knxCard.getTenantId();
            TenantDTO tenant = tenantList.stream().filter(t -> Objects.equals(t.getId(), tenantId)).findFirst().orElse(null);
            if (tenant != null) {
                cardResultVO.setTenantName(tenant.getName());
            }

            Date expireDate = knxCard.getExpireDate();
            int i = expireDate.compareTo(new Date());

            cardResultVO.setIsExpired(i < 0);

            list.add(cardResultVO);
        }
        return list.stream().sorted(Comparator.comparing(CardResultVO::getCreateTime).reversed()).collect(Collectors.toList());
    }

    @Override
    public List<CardResultExcelVO> listExcel(CardSelectVO cardSelectVO) {
        selectData(cardSelectVO);
        List<KnxCard> knxCards = knxCardMapper.listKnxCard(cardSelectVO);
        List<CardResultVO> cardResultVOS = getCardResultVOS(knxCards);
        List<CardResultExcelVO> cardResultExcelVOS = new ArrayList<>();
        for (CardResultVO cardResultVO : cardResultVOS) {
            CardResultExcelVO cardResultExcelVO = new CardResultExcelVO();

            Boolean isExpired = cardResultVO.getIsExpired();
            if (Boolean.TRUE.equals(isExpired)) {
                cardResultExcelVO.setIsExpired(KnxBeanField.YES);
            } else {
                cardResultExcelVO.setIsExpired(KnxBeanField.NO);
            }
            Boolean isUsed = cardResultVO.getIsUsed();
            if (Boolean.TRUE.equals(isUsed)) {
                cardResultExcelVO.setIsUsed(KnxBeanField.YES);
            } else {
                cardResultExcelVO.setIsUsed(KnxBeanField.NO);
            }
            cardResultExcelVO.setTenantName(cardResultVO.getTenantName());
            cardResultExcelVO.setCreateName(cardResultVO.getCreateName());
            cardResultExcelVO.setCardPassword(cardResultVO.getCardPassword());
            Integer amount = cardResultVO.getAmount().intValue();
            cardResultExcelVO.setAmount(amount);
            cardResultExcelVO.setCardType(cardResultVO.getCardType());
            Date date = cardResultVO.getExpireDate();
            String expireDate = DateUtil.formatNoMinutesAndSeconds(date);
            cardResultExcelVO.setExpireDate(expireDate);
            Date createTime = cardResultVO.getCreateTime();
            String createDate = DateUtil.formatNoMinutesAndSeconds(createTime);
            cardResultExcelVO.setCreateTime(createDate);
            cardResultExcelVOS.add(cardResultExcelVO);
        }
        return cardResultExcelVOS;
    }

    private void selectData(CardSelectVO cardSelectVO) {
        Date startCreateDate = cardSelectVO.getStartCreateDate();
        Date endCreateDate = cardSelectVO.getEndCreateDate();
        Date date = new Date();
        String today = DateUtil.formatNormTime(date);
        cardSelectVO.setToday(today);
        if (startCreateDate != null) {
            String normTime = DateUtil.formatNoMinutesAndSeconds(startCreateDate);
            cardSelectVO.setStartCreateDateString(normTime);
        }
        if (endCreateDate != null) {
            String normTime = DateUtil.formatNoMinutesAndSeconds(endCreateDate);
            cardSelectVO.setEndCreateDateString(normTime);
        }

        String tenantName = cardSelectVO.getTenantName();
        if (tenantName != null) {
            List<TenantDTO> tenants = remoteTenantService.listLikeName(tenantName).getData();
            List<String> tenantIds = tenants.stream().map(TenantDTO::getId).collect(Collectors.toList());
            if (!KnxListUtils.isListEmpty(tenantIds)) {
                cardSelectVO.setTenantIds(tenantIds);
            }
        }
    }

    @Override
    public String checkoutCardPassword(String password) {
        String message = "";
        KnxCard knxCard = lambdaQuery().eq(KnxCard::getCardPassword, password).one();
        if (knxCard == null) {
            message += KnxBeanField.CARD_PWD_DOES_NOT_EXIST_MESSAGE;
        } else {
            if (Boolean.TRUE.equals(knxCard.getIsUsed())) {
                message += KnxBeanField.CARD_PWD_USED_MESSAGE;
            }
            Date expireDate = knxCard.getExpireDate();
            int i = expireDate.compareTo(new Date());
            if (i < 0) {
                message += KnxBeanField.CARD_PWD_HAS_EXPIRED_MESSAGE;
            }
        }

        if (!Objects.equals(message, "")) {
            message = message.substring(0, message.length() - 1);
        }
        return message;
    }

    @Override
    public List<KnxCard> saveBatchCard(List<KnxCard> knxCards) {
        List<KnxCard> cards = new ArrayList<>();
        for (KnxCard knxCard : knxCards) {
            Integer num = knxCard.getNum();
            knxCard.setIsUsed(false);
            knxCard.setIsRecharge(false);
            for (int i = 0; i < num; i++) {
                KnxCard card = new KnxCard();
                String password = CodeUtil.generateShortUuid();
                BeanUtils.copyProperties(knxCard, card);
                card.setCardPassword(password);
                cards.add(card);
            }
        }
        saveBatch(cards);
        return cards;
    }

    @Override
    public Integer updateBatchByPassword(List<KnxCard> knxCards) {
        return knxCardMapper.updateBatchByPassword(knxCards);

    }

    @Override
    public Integer updateRechargeBatchByPassword(List<KnxCard> knxCards) {
        return knxCardMapper.updateRechargeBatchByPassword(knxCards);

    }

    @Override
    public Boolean batchCheckoutCardPassword(List<String> passwords) {
        for (String password : passwords) {
            String message = checkoutCardPassword(password);
            if (!Objects.equals(message, "")) {
                return false;
            }
        }
        return true;
    }

    @Override
    public Boolean batchCheckoutCardPasswordIsRecharge(List<String> passwords) {
        List<KnxCard> knxCards = lambdaQuery().in(KnxCard::getCardPassword, passwords).list();
        for (KnxCard knxCard : knxCards) {
            //knxCard.getIsRecharge()为ture代表已充值
            if (Boolean.TRUE.equals(knxCard.getIsRecharge())) {
                return false;
            }
        }
        return true;
    }


    @Override
    public List<KnxCard> listByCardPassword(List<String> cardPasswords) {
        if (KnxListUtils.isListEmpty(cardPasswords)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(KnxCard::getCardPassword, cardPasswords).list();
    }
}
