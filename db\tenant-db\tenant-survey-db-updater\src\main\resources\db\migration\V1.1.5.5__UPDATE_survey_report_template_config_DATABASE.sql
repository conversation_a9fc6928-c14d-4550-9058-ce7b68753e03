alter TABLE public.survey_question add column "custom_dimension_description" varchar (500) default '';


DROP TABLE IF EXISTS survey_custom_dimension;
CREATE TABLE survey_custom_dimension
(
    "id"                              VARCHAR(36)  NOT NULL,
    "project_id"                      VARCHAR(36)  NOT NULL,
    "questionnaire_id"                VARCHAR(36)  NOT NULL,
    "name"                            VARCHAR(50)  NOT NULL,
    "type"                            VARCHAR(50)  NOT NULL,
    "proportion"                      int4        ,
    "description"                     VARCHAR(50)  ,
    "create_by"                       VARCHAR(36)  NOT NULL,
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___survey_custom_dimension___" PRIMARY KEY ("id")
);

COMMENT ON TABLE  "public"."survey_custom_dimension" IS '自定义维度';
COMMENT ON COLUMN "public"."survey_custom_dimension"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_custom_dimension"."questionnaire_id" IS '问卷id';
COMMENT ON COLUMN "public"."survey_custom_dimension"."type" IS '维度类型';
COMMENT ON COLUMN "public"."survey_custom_dimension"."proportion" IS '维度比重';
COMMENT ON COLUMN "public"."survey_custom_dimension"."name" IS '维度名称';