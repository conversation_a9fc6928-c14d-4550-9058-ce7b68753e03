package com.knx.bean.service.util;

import com.knx.common.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/6/8 15:17
 */
@Component
@Slf4j
public class KnxBeanUtils {
    public static final String REQ_NO_DATE_FORMAT = "yyyyMMddHHmmss";
    public static final Integer DAY_SECONDS = 86400;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 生成订单号
     *
     * @return
     */
    public String generateReqNo() {
        String time = DateFormatUtils.format(new Date(), REQ_NO_DATE_FORMAT);
        Long num = redisUtil.incr(RedisKeys.getSeqNumberKey());
        if (num == 1) {
            int secondsLeftToday = (int) (DAY_SECONDS - DateUtils.getFragmentInSeconds(Calendar.getInstance(), Calendar.DATE));
            redisUtil.expire(RedisKeys.getSeqNumberKey(), secondsLeftToday);
        }
        return time + padding(num);
    }

    /**
     * 补零
     *
     * @param num
     * @return
     */
    public String padding(Long num) {
        String str = String.valueOf(num);
        StringBuilder nums = new StringBuilder(str);
        if (str.length() > 8) {
            return nums.toString();
        }
        for (int i = 0; i <= 9 - str.length(); i++) {
            nums.insert(0, 0);
        }
        return nums.toString();
    }
}
