package com.knx.survey.common.util;

import com.knx.common.base.utils.KnxListUtils;
import com.knx.survey.common.base.field.SurveyField;
import com.knx.survey.model.common.demographic.SurveyStandardDemographic;
import com.knx.survey.model.common.demographic.SurveyStandardDemographicQuestion;
import com.knx.survey.model.common.mapping.SurveyStandardQuestionnaireDimensionMapping;
import com.knx.survey.model.common.mapping.SurveyStandardQuestionnaireDimensionQuestionMapping;
import com.knx.survey.model.common.questionnaire.SurveyStandardQuestionnaireDimension;
import com.knx.survey.model.enums.QuestionTypeEnum;
import com.knx.survey.model.tenant.mapping.SurveyPackageQuestionMapping;
import com.knx.survey.model.tenant.mapping.SurveyQuestionnaireDimensionMapping;
import com.knx.survey.model.tenant.mapping.SurveyQuestionnaireDimensionQuestionMapping;
import com.knx.survey.model.tenant.norm.SurveyNorm;
import com.knx.survey.model.tenant.norm.SurveyNormValue;
import com.knx.survey.model.tenant.question.SurveyPackage;
import com.knx.survey.model.tenant.question.SurveyQuestion;
import com.knx.survey.model.tenant.questionnaire.SurveyQuestionnaire;
import com.knx.survey.model.tenant.questionnaire.SurveyQuestionnaireDimension;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * User: 林超
 * Date: 2020/3/25
 * Time: 10:10 AM
 */
public class IDUtil {
    /**
     * 返回没有分隔符的uuid
     *
     * @return
     */
    public static String getSimpleUUID() {
        String s = UUID.randomUUID().toString();
        return s.replace("-", "");
    }

    /**
     * 问卷复制业务
     * 泛型入参
     * t maybe
     * {@link SurveyQuestionnaire}?
     * {@link SurveyQuestionnaireDimension}?
     * {@link SurveyQuestion}?
     *
     * @param t
     * @param <T>
     */
    public static <T> String replaceNewQuestionnaireIds(T t, @Nullable String questionnaireId) {

        String newQuestionnaireId = StringUtils.isEmpty(questionnaireId) ? IDUtil.getSimpleUUID() : questionnaireId;

        if (t.getClass() == SurveyQuestionnaire.class) {
            //问卷表，只有一条记录吧，那就设置一次
            ((SurveyQuestionnaire) t).setId(newQuestionnaireId);
        } else if (t instanceof List && !CollectionUtils.isEmpty((Collection<?>) t)) {
            Object o = ((List) t).get(0);
            if (o.getClass() == SurveyQuestionnaireDimension.class) {
                for (SurveyQuestionnaireDimension questionnaireDimension : ((List<SurveyQuestionnaireDimension>) t)) {
                    //todo   去掉standard
                    questionnaireDimension.setQuestionnaireId(newQuestionnaireId);
                }
            } else if (o.getClass() == SurveyQuestion.class) {
                for (SurveyQuestion question : ((List<SurveyQuestion>) t)) {
                    question.setQuestionnaireId(newQuestionnaireId);
                }
            }
        }

        return newQuestionnaireId;
    }

    /**
     * 问卷题目表id换成新的
     * 同时维度映射表的standardQuestionnaireQuestionId换成新生成的题目id
     * <p>
     * {@link SurveyQuestion}
     * {@link SurveyQuestionnaireDimensionQuestionMapping}
     *
     * @param questionnaireQuestions
     * @param questionnaireDimensionQuestionMappings
     */
    public static void replaceQuestionIdsAndRelatedIds(List<SurveyQuestion> questionnaireQuestions,
                                                       List<SurveyQuestionnaireDimensionQuestionMapping> questionnaireDimensionQuestionMappings) {

        replaceQuestionIdsAndRelatedIds(questionnaireQuestions, questionnaireDimensionQuestionMappings, null, null, null);
        /*if (!CollectionUtils.isEmpty(questionnaireQuestions)) {
            questionnaireQuestions.forEach(questionnaireQuestion -> {
                String oldId = questionnaireQuestion.getId();//旧值
                String newId = IDUtil.getUUID();//新值
                questionnaireQuestion.setId(newId);
                if (!CollectionUtils.isEmpty(questionnaireDimensionQuestionMappings)) {
                    questionnaireDimensionQuestionMappings.forEach(questionnaireDimensionQuestionMapping -> {
                        questionnaireDimensionQuestionMapping.setId(IDUtil.getUUID());
                        if (oldId.equals(questionnaireDimensionQuestionMapping.getStandardQuestionnaireQuestionId())) {
                            questionnaireDimensionQuestionMapping.setStandardQuestionnaireQuestionId(newId);
                        }

                    });
                }
            });
        }*/
    }


    /**
     * @param questionnaireQuestions
     * @param questionnaireDimensionQuestionMappings
     * @param standardNormValues
     * @param norms
     */
    public static void replaceQuestionIdsAndRelatedIds(List<SurveyQuestion> questionnaireQuestions,
                                                       List<SurveyQuestionnaireDimensionQuestionMapping> questionnaireDimensionQuestionMappings,
                                                       List<SurveyNormValue> standardNormValues,
                                                       List<SurveyNorm> norms, List<SurveyPackageQuestionMapping> surveyPackageQuestionMappings) {

        if (!CollectionUtils.isEmpty(questionnaireQuestions)) {
            //取出分页题
            List<SurveyQuestion> pageSplitQuestions = questionnaireQuestions.stream().filter(data -> data.getType() == QuestionTypeEnum.PAGE_SPLIT).collect(Collectors.toList());
            questionnaireQuestions.removeAll(pageSplitQuestions);
            for (SurveyQuestion pageSplitQuestion : KnxListUtils.nvlList(pageSplitQuestions)) {
                pageSplitQuestion.setId(IDUtil.getSimpleUUID());
            }
            //先过滤出子题目
            List<SurveyQuestion> sonQuestions = questionnaireQuestions.stream()
                    .filter(data -> null != data.getParentId())
                    .collect(Collectors.toList());
            Map<String, String> sonAndFatherIdMappings = new HashMap<>();
            for (SurveyQuestion sonQuestion : KnxListUtils.nvlList(sonQuestions)) {
                sonAndFatherIdMappings.put(sonQuestion.getId(), sonQuestion.getParentId());
            }

            questionnaireQuestions.removeAll(sonQuestions);

            questionnaireQuestions.forEach(questionnaireQuestion -> {
                String oldId = questionnaireQuestion.getId();//旧值
                String newId = IDUtil.getSimpleUUID();//新值
                questionnaireQuestion.setId(newId);
                if (!CollectionUtils.isEmpty(questionnaireDimensionQuestionMappings)) {
                    questionnaireDimensionQuestionMappings.forEach(questionnaireDimensionQuestionMapping -> {
                        // questionnaireDimensionQuestionMapping.setId(IDUtil.getUUID());
                        if (oldId.equals(questionnaireDimensionQuestionMapping.getQuestionnaireQuestionId())) {
                            questionnaireDimensionQuestionMapping.setQuestionnaireQuestionId(newId);
                        }

                    });
                }

                if (!CollectionUtils.isEmpty(sonAndFatherIdMappings)) {
                    Set<Map.Entry<String, String>> entries = sonAndFatherIdMappings.entrySet();
                    for (Map.Entry<String, String> entry : entries) {
                        if (entry.getValue().equals(oldId)) {
                            entry.setValue(newId);
                        }
                    }
                }

                //String oldStandardQuestionId = questionnaireQuestion.getStandardQuestionId();
                if (!CollectionUtils.isEmpty(standardNormValues)) {
                    standardNormValues.forEach(standardNormValue -> {
                        standardNormValue.setId(IDUtil.getSimpleUUID());
                        if (oldId.equals(standardNormValue.getQuestionId())) {
                            String oldNormId = standardNormValue.getNormId();
                            standardNormValue.setId(IDUtil.getSimpleUUID());
                            String newNormId = IDUtil.getSimpleUUID();
                            standardNormValue.setNormId(newNormId);
                            norms.forEach(norm -> {
                                if (norm.getId().equals(oldNormId)) {
                                    norm.setId(newNormId);
                                }
                            });
                        }
                    });
                }

                // 替换题目组和题目映射表
                if (!CollectionUtils.isEmpty(surveyPackageQuestionMappings)) {
                    surveyPackageQuestionMappings.stream().forEach(surveyPackageQuestionMapping -> {
                        if (oldId.equals(surveyPackageQuestionMapping.getStandardQuestionId())) {
                            surveyPackageQuestionMapping.setQuestionId(newId);
                        }
                    });
                }
            });

            //处理一下子题目id
            for (SurveyQuestion sonQuestion : KnxListUtils.nvlList(sonQuestions)) {
                sonAndFatherIdMappings.forEach((varKey, varValue) -> {
                    if (sonQuestion.getId().equals(varKey)) {
                        sonQuestion.setParentId(varValue);
                    }
                });
                String newId = IDUtil.getSimpleUUID();
                // 替换题目组和题目映射表
                if (!CollectionUtils.isEmpty(surveyPackageQuestionMappings)) {
                    surveyPackageQuestionMappings.stream().forEach(surveyPackageQuestionMapping -> {
                        if (sonQuestion.getId().equals(surveyPackageQuestionMapping.getStandardQuestionId())) {
                            surveyPackageQuestionMapping.setQuestionId(newId);
                        }
                    });
                }

                sonQuestion.setId(newId);
            }
            questionnaireQuestions.addAll(sonQuestions);
            questionnaireQuestions.addAll(pageSplitQuestions);
        }
    }


    /**
     * {@link SurveyStandardQuestionnaireDimension}
     * {@link SurveyStandardQuestionnaireDimensionQuestionMapping}
     * {@link SurveyStandardQuestionnaireDimensionMapping}
     *
     * @param questionnaireDimensions
     * @param questionnaireDimensionQuestionMappings
     * @param questionnaireDimensionMappings
     */
    public static void replaceDimensionIds(List<SurveyQuestionnaireDimension> questionnaireDimensions,
                                           List<SurveyQuestionnaireDimensionQuestionMapping> questionnaireDimensionQuestionMappings,
                                           List<SurveyQuestionnaireDimensionMapping> questionnaireDimensionMappings, Map<String, String> newAndOldPackgeIdMap) {
        if (!CollectionUtils.isEmpty(questionnaireDimensions)) {
            questionnaireDimensions.forEach(questionnaireDimension -> {
                String oldQuestionnaireDimensionId = questionnaireDimension.getId();
                String newQuestionnaireDimensionId = IDUtil.getSimpleUUID();
                questionnaireDimension.setId(newQuestionnaireDimensionId);

                if (!CollectionUtils.isEmpty(questionnaireDimensionQuestionMappings)) {
                    questionnaireDimensionQuestionMappings.forEach(questionnaireDimensionQuestionMapping -> {
                        //替换维度与题目关系映射表中的
                        if (oldQuestionnaireDimensionId.equals(questionnaireDimensionQuestionMapping.getQuestionnaireDimensionId())) {
                            questionnaireDimensionQuestionMapping.setQuestionnaireDimensionId(newQuestionnaireDimensionId);
                        }
                    });
                }

                //questionnaireDimension.getGroupType().equalsIgnoreCase(DimensionGroupTypeEnum.COMBINATION.getDesc())
                //维度表groupType是否是 组合维度
                //todo 层级较多情况？
                if (!CollectionUtils.isEmpty(questionnaireDimensionMappings)) {
                    questionnaireDimensionMappings.forEach(questionnaireDimensionMapping -> {
                        String oldParentId = questionnaireDimensionMapping.getParentId();
                        String oldChildId = questionnaireDimensionMapping.getChildId();
                        if (oldParentId.equalsIgnoreCase(oldQuestionnaireDimensionId)) {
                            questionnaireDimensionMapping.setParentId(newQuestionnaireDimensionId);
                        }
                        if (!StringUtils.isEmpty(oldChildId)) {
                            questionnaireDimensionMapping.setChildId(questionnaireDimensionMapping.getChildId());
                        }
                    });
                }

                // 替换题目组ID
                if (null != newAndOldPackgeIdMap) {
                    if (questionnaireDimension.getPackageId() != null && newAndOldPackgeIdMap.get(questionnaireDimension.getPackageId()) != null) {
                        questionnaireDimension.setPackageId(newAndOldPackgeIdMap.get(questionnaireDimension.getPackageId()));
                    }
                }
            });
        }

    }


    /**
     * @param surveyStandardDemographicList
     */
    public static void replaceDemographicIds(List<SurveyStandardDemographic> surveyStandardDemographicList, List<SurveyStandardDemographicQuestion> oldStandardDemographicQuestionList) {
        Map<String, String> newAndOldMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(surveyStandardDemographicList)) {
            surveyStandardDemographicList.stream().forEach(surveyStandardDemographic -> {
                String oldDemographicId = surveyStandardDemographic.getId();
                String newDemographicId = IDUtil.getSimpleUUID();
                surveyStandardDemographic.setId(newDemographicId);
                surveyStandardDemographic.setStandardDemographicId(oldDemographicId);
                newAndOldMap.put(oldDemographicId, newDemographicId);

                if (!CollectionUtils.isEmpty(oldStandardDemographicQuestionList)) {
                    oldStandardDemographicQuestionList.stream().forEach(surveyStandardDemographicQuestion -> {
                        if (surveyStandardDemographicQuestion.getStandardDemographicId().equals(oldDemographicId)) {
                            surveyStandardDemographicQuestion.setStandardDemographicId(newDemographicId);
                            surveyStandardDemographicQuestion.setId(IDUtil.getSimpleUUID());
                        }
                    });
                }
            });

            // 父子级别id替换
            surveyStandardDemographicList.stream().forEach(surveyStandardDemographic -> {
                if (!CollectionUtils.isEmpty(newAndOldMap) && !surveyStandardDemographic.getParentId().equals(SurveyField.PARENT_ID_DEFAULE_VALUE)) {
                    if (newAndOldMap.get(surveyStandardDemographic.getParentId()) != null) {
                        surveyStandardDemographic.setParentId(newAndOldMap.get(surveyStandardDemographic.getParentId()));
                    }
                }
            });
        }

    }


    /**
     * @param surveyPackages
     * @param surveyPackageQuestionMappings
     */
    public static Map<String, String> replacePackageIds(List<SurveyPackage> surveyPackages, List<SurveyPackageQuestionMapping> surveyPackageQuestionMappings) {
        Map<String, String> newAndOldPackgeIdMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(surveyPackages)) {
            surveyPackages.stream().forEach(surveyPackage -> {
                String oldDemographicId = surveyPackage.getId();
                String newDemographicId = IDUtil.getSimpleUUID();
                surveyPackage.setId(newDemographicId);
                newAndOldPackgeIdMap.put(oldDemographicId, newDemographicId);
                if (!CollectionUtils.isEmpty(surveyPackageQuestionMappings)) {
                    surveyPackageQuestionMappings.stream().forEach(surveyPackageQuestionMapping -> {
                        if (oldDemographicId.equals(surveyPackageQuestionMapping.getPackageId())) {
                            surveyPackageQuestionMapping.setPackageId(newDemographicId);
                        }
                    });
                }
            });
        }
        return newAndOldPackgeIdMap;
    }

}
