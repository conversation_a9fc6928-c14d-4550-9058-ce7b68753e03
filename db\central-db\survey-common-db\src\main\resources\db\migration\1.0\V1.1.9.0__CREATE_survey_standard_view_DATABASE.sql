DROP TABLE IF EXISTS "public"."survey_standard_view";
CREATE TABLE survey_standard_view (
	id varchar(36) NOT NULL,
    type varchar(50) NULL,
    report_type varchar(50) NULL,
    cos_url varchar(500) NULL,
    tenant_url varchar(500) NULL,
    is_group bool NULL DEFAULT false,
    remark varchar(500) NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__survey_standard_view___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."survey_standard_view"."type" IS '类型';
COMMENT ON COLUMN "public"."survey_standard_view"."report_type" IS '报告类型';
COMMENT ON COLUMN "public"."survey_standard_view"."cos_url" IS '腾讯云地址';
COMMENT ON COLUMN "public"."survey_standard_view"."tenant_url" IS '租户端地址';
COMMENT ON COLUMN "public"."survey_standard_view"."is_group" IS '是否是团队报告';
COMMENT ON COLUMN "public"."survey_standard_view"."remark" IS '备注';
COMMENT ON TABLE  "public"."survey_standard_view" IS '视频表';
