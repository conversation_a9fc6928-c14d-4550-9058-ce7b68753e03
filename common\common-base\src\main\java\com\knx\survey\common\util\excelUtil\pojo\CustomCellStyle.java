package com.knx.survey.common.util.excelUtil.pojo;

import lombok.Builder;
import lombok.Data;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.xssf.usermodel.XSSFColor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OneCellStyle.java
 * @Description TODO
 * @createTime 2022年09月08日 10:22:00
 */
@Data
@Builder
public class CustomCellStyle {

    /**
     * 倾斜
     */
    private Boolean italic;

    /**
     * 加粗
     */
    private Boolean bold;

    /**
     * 背景色
     */
    private XSSFColor bColor;

    /**
     * 水平位置
     */
    private HorizontalAlignment alignment;

    /**
     * 高度
     */
    private Integer height;

    /**
     * 宽度
     */
    private Integer width;

    /**
     * 字号
     */
    private Integer fontSize;

    /**
     * 字体
     */
    private String fontName;

    /**
     * 颜色
     */
    private XSSFColor fontColor;

    /**
     * 边框
     */
    private Boolean isShowBorder = true;

    /**
     * 格式化内容
     */
    private String formatNum;


    private CellStyle cellStyle;

}
