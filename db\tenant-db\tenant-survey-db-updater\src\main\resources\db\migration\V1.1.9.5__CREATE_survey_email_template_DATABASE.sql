DROP TABLE IF EXISTS survey_email_template;
CREATE TABLE survey_email_template
(
    "id"                              VARCHAR(36)  NOT NULL,
    "name"                            VARCHAR(36)  NOT NULL,
    "content"                         TEXT ,
    "is_standard"                     BOOL  DEFAULT false,
    "is_default"                      BOOL  DEFAULT false,
    "create_by"                       VARCHAR(36)  NOT NULL,
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL   DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___survey_email_template___" PRIMARY KEY ("id")
);

COMMENT ON TABLE  "public"."survey_email_template" IS '活动邀请邮件内容模板';
COMMENT ON COLUMN "public"."survey_email_template"."name" IS '模板名称';
COMMENT ON COLUMN "public"."survey_email_template"."content" IS '邮件内容';
COMMENT ON COLUMN "public"."survey_email_template"."is_standard" IS '是否是标准邮件内容';
COMMENT ON COLUMN "public"."survey_email_template"."is_default" IS '是否是默认邮件内容';