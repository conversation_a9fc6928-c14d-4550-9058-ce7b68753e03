ALTER TABLE public.survey_email_template ADD purpose varchar(50) NULL DEFAULT 'COMMON';
COMMENT ON COLUMN public.survey_email_template.purpose IS '用途';

ALTER TABLE public.survey_email_template ADD is_invite_default bool NULL DEFAULT false;
COMMENT ON COLUMN public.survey_email_template.is_invite_default IS '是否默认填答邀请模板';

UPDATE public.survey_email_template set purpose ='ANSWER' where id ='1448490496611201025';

INSERT INTO public.survey_email_template ("id", "name", "content", "is_standard", "is_default", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "purpose", "is_invite_default") VALUES ('1510190135856201731', '系统标准邀请填答模板', '您好：<br> 诚挚邀请您推荐与您在日常工作中存在协作关系的同事，对您进行评估。<br> 请您在{{开始日期}}和{{结束日期}}之间登录{{链接}}完成推荐<font color="#FF0000">（建议使用Google Chrome浏览器）。</font><br> 感谢您的参与！', 't', 'f', '1456493513867919362', '2022-04-01 19:41:23', '2022-04-01 19:41:27', '1381929408155222017', 'f', '1284443709327511553', 'INVITE_ANSWER', 't');

