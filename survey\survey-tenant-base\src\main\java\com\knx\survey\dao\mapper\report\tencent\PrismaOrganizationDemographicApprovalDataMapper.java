package com.knx.survey.dao.mapper.report.tencent;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.knx.common.dynamic.annotation.MultiTenant;
import com.knx.survey.model.report.prisma.tencent.PrismaOrganizationDemographicApprovalData;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * PrismaOrganizationDemographicApprovalDataMapper
 * <p>
 * 提供对PrismaOrganizationDemographicApprovalData实体的基础CRUD操作。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-08
 */
@MultiTenant
public interface PrismaOrganizationDemographicApprovalDataMapper extends BaseMapper<PrismaOrganizationDemographicApprovalData> {

    @Delete("DELETE FROM prisma_organization_demographic_approval_data " +
           "WHERE project_id = #{projectId} " +
           "AND organization = #{organization} " +
           "AND mainland_type = #{mainlandType}")
    void deleteByProjectAndOrgAndMainlandType(
        @Param("projectId") String projectId,
        @Param("organization") String organization, 
        @Param("mainlandType") String mainlandType);
    
    @Select("SELECT id FROM prisma_organization_demographic_approval_data WHERE project_id = #{projectId}")
    List<String> listIdsByProjectId(@Param("projectId") String projectId);
}
