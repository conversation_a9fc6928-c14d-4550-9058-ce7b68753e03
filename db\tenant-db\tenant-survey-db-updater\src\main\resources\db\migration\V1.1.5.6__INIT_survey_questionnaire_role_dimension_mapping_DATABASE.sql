/*角色维度绑定mapping*/
DROP TABLE IF EXISTS survey_questionnaire_role_dimension_mapping;
CREATE TABLE survey_questionnaire_role_dimension_mapping
(
    "id"                                     VARCHAR(36)  NOT NULL,
    "project_id"                             VARCHAR(36)  NOT NULL,
    "questionnaire_id"                       VARCHAR(36)  NOT NULL,
    "role_id"                                VARCHAR(36)  NOT NULL,
    "custom_dimension"                       VARCHAR(100) NULL ,
    "dimension_id"                           VARCHAR(36)  NULL ,
    "create_by"                              VARCHAR(36),
    "create_time"                            TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                            TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                         VARCHAR(36),
    "is_deleted"                             BOOL         DEFAULT false,
    "app_id"                                 VARCHAR(36),
    CONSTRAINT  "___pk___survey_questionnaire_role_dimension_mapping___" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."survey_questionnaire_role_dimension_mapping" IS '角色维度绑定表';
COMMENT ON COLUMN "public"."survey_questionnaire_role_dimension_mapping"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_questionnaire_role_dimension_mapping"."questionnaire_id" IS '问券id';
COMMENT ON COLUMN "public"."survey_questionnaire_role_dimension_mapping"."role_id" IS '角色id';
COMMENT ON COLUMN "public"."survey_questionnaire_role_dimension_mapping"."dimension_id" IS '维度id';
COMMENT ON COLUMN "public"."survey_questionnaire_role_dimension_mapping"."custom_dimension" IS '360自定义维度';