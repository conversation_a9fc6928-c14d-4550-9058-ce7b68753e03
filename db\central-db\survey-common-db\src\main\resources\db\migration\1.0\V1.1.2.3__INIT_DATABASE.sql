DROP TABLE IF EXISTS "public"."survey_standard_prisma_norm";
CREATE TABLE survey_standard_prisma_norm (
	id varchar(36) NOT NULL,
	name json NULL,
	code varchar(36) NULL,
	year varchar(10) NULL,
    industry varchar(50) NULL,
    quantile varchar(20) NULL,
	amount decimal NULL,
	status varchar(10) NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__survey_standard_prisma_norm___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."survey_standard_prisma_norm"."name" IS '常模名称';
COMMENT ON COLUMN "public"."survey_standard_prisma_norm"."code" IS '常模编码';
COMMENT ON COLUMN "public"."survey_standard_prisma_norm"."amount" IS '常模价格，0表示免费';
COMMENT ON COLUMN "public"."survey_standard_prisma_norm"."year" IS '年份';
COMMENT ON COLUMN "public"."survey_standard_prisma_norm"."industry" IS '行业';
COMMENT ON COLUMN "public"."survey_standard_prisma_norm"."quantile" IS '分位';
COMMENT ON COLUMN "public"."survey_standard_prisma_norm"."status" IS '状态';
COMMENT ON TABLE  "public"."survey_standard_prisma_norm" IS '标准prisma常模表';


DROP TABLE IF EXISTS "public"."survey_standard_norm_question_mapping";
CREATE TABLE survey_standard_norm_question_mapping (
	id varchar(36) NOT NULL,
	norm_id varchar(36) NULL,
	question_code varchar(36) NULL,
	value numeric(53,2) NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__survey_standard_norm_question_mapping___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."survey_standard_norm_question_mapping"."norm_id" IS '常模ID';
COMMENT ON COLUMN "public"."survey_standard_norm_question_mapping"."question_code" IS '问题编码';
COMMENT ON COLUMN "public"."survey_standard_norm_question_mapping"."value" IS '常模值';
COMMENT ON TABLE  "public"."survey_standard_norm_question_mapping" IS '标准prisma常模问题mapping';