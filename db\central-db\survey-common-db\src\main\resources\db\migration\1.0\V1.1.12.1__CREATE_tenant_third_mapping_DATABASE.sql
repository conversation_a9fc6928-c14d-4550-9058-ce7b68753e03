DROP TABLE IF EXISTS "public"."tenant_third_mapping";
CREATE TABLE tenant_third_mapping (
	id varchar(36) NOT NULL,
	third_type varchar(50) NOT NULL,
	tenant_id varchar(36) NOT NULL,
	is_enable bool DEFAULT false,
	is_bind bool DEFAULT false,
	access_token varchar(255) NULL,
	access_token_expires_at varchar(50) NULL,
	refresh_token varchar(255) NULL,
	refresh_token_expires_at varchar(50) NULL,
	scope varchar(255) NULL,
	third_app_id varchar(255) NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__tenant_third_mapping___" PRIMARY KEY (id)
);
COMMENT ON TABLE  "public"."tenant_third_mapping" IS '租户第三方关系表';
COMMENT ON COLUMN "public"."tenant_third_mapping"."third_type" IS '第三方类型';
COMMENT ON COLUMN "public"."tenant_third_mapping"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."tenant_third_mapping"."is_enable" IS '是否启用';
COMMENT ON COLUMN "public"."tenant_third_mapping"."is_bind" IS '是否绑定';
COMMENT ON COLUMN "public"."tenant_third_mapping"."access_token" IS '访问token';
COMMENT ON COLUMN "public"."tenant_third_mapping"."access_token_expires_at" IS '访问token过期时间';
COMMENT ON COLUMN "public"."tenant_third_mapping"."refresh_token" IS '刷新token';
COMMENT ON COLUMN "public"."tenant_third_mapping"."refresh_token_expires_at" IS '刷新token过期时间';
COMMENT ON COLUMN "public"."tenant_third_mapping"."scope" IS '第三方供应商拥有权限';
COMMENT ON COLUMN "public"."tenant_third_mapping"."third_app_id" IS '第三方appId';


CREATE INDEX ___idx_tenant_third_mapping_tenant_id___ ON public.tenant_third_mapping(tenant_id);
