package com.knx.survey.common.util.excelUtil.utils;

import com.alibaba.excel.write.handler.WriteHandler;
import com.knx.survey.model.enums.SagFileBusinessTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Data
public class SheetData {

    /**
     * sheet名
     */
    private String sheetName;

    /**
     * 数据
     */
    private List data;

    /**
     * 数据模型类型
     */
    private Class dataModelClazz;

    /**
     * 动态表头
     */
    private List<List<String>> head;

    /**
     * Sheet级别处理
     */
    private List<WriteHandler> handlers;

    /**
     * 设置列宽（10000比较合适）
     */
    private Map columnWidthMap;


    private String sheetKey;

    //用于区分数据明细表
    private SagFileBusinessTypeEnum businessType;

    /**
     * 隐藏列
     */
    private List<Integer> columnHidden = Collections.EMPTY_LIST;

    /**
     * 是否使用类对象设置表头
     */
    private Boolean isUseModelClazzHeader = true;

    /**
     * 下拉框数据
     */
    private Map<Integer,String[]> mapDropDownData;

    /**
     * 检查不允许为空的属性
     *
     * @return this
     */
    public SheetData checkValid() {
        Assert.isTrue(ObjectUtils.allNotNull(sheetName), "导出excel参数不合法!");
        return this;
    }

    public void setSingleWriteHandler(WriteHandler writeHandler) {
        this.handlers = Collections.singletonList(writeHandler);
    }
}
