/*标准题目表*/
DROP TABLE IF EXISTS "public"."survey_standard_question";
CREATE TABLE  "public"."survey_standard_question"
(
    "id"             VARCHAR(36) NOT NULL,
    "name"           JSON,
    "type"           VARCHAR(50),
    "survey_type"    VARCHAR(50),
    "timing"         INT4,
    "level"          VARCHAR(50),
    "is_require"     BOOL,
    "option_id"      VARCHAR(36),
    "options_value"  JSON,
    "parent_id"      VARCHAR(36),
    "create_by"      VARCHAR(36),
    "create_time"    TIMESTAMP(3) DEFAULT NOW(),
    "update_time"    TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by" VARCHAR(36),
    "is_deleted"     BOOL         DEFAULT FALSE,
    "app_id"         VARCHAR(36),
    CONSTRAINT  "___pk__standard_question___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_standard_question"."name" IS '题目名称';
COMMENT ON COLUMN "public"."survey_standard_question"."type" IS '题目类型 ';
COMMENT ON COLUMN "public"."survey_standard_question"."survey_type" IS '调研类型 ';
COMMENT ON COLUMN "public"."survey_standard_question"."timing" IS '题目计时';
COMMENT ON COLUMN "public"."survey_standard_question"."level" IS '题目难易长度 简单 中等 困难 ';
COMMENT ON COLUMN "public"."survey_standard_question"."is_require" IS '是否必填 ';
COMMENT ON COLUMN "public"."survey_standard_question"."option_id" IS '选项ID ';
COMMENT ON COLUMN "public"."survey_standard_question"."options_value" IS '选项值 ';
COMMENT ON COLUMN "public"."survey_standard_question"."parent_id" IS '题目父级ID 为0代表父级';
COMMENT ON TABLE "public"."survey_standard_question" IS '标准题目表';


/*标准问卷表*/
DROP TABLE IF EXISTS "public"."survey_standard_questionnaire";
CREATE TABLE  "public"."survey_standard_questionnaire"
(
    "id"                          VARCHAR(36)       NOT NULL,
    "name"                        VARCHAR(100)      NOT NULL,
    "standard_report_template_id" VARCHAR(36)       NOT NULL,
    "code"                        VARCHAR(100)      NOT NULL,
    "scope_type"                  VARCHAR(50),
    "type"                        VARCHAR(50),
    "category_id"                 VARCHAR(36)       NOT NULL,
    "is_service_statement"        BOOL              DEFAULT FALSE,
    "status"                      VARCHAR(50),
    "question_num_in_one_page"    INT4              NOT NULL,
    "answer_description"          text,
    "duration"                    INT4              NOT NULL,
    "create_by"                   VARCHAR(36),
    "create_time"                 TIMESTAMP(3)      DEFAULT NOW(),
    "update_time"                 TIMESTAMP(3)      DEFAULT NOW(),
    "last_update_by"              VARCHAR(36),
    "is_deleted"                  BOOL              DEFAULT FALSE,
    "app_id"                      VARCHAR(36),
    CONSTRAINT  "___pk__standard_questionnaire___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_standard_questionnaire"."name" IS '问卷名称 ';
COMMENT ON COLUMN "public"."survey_standard_questionnaire"."standard_report_template_id" IS '标准报告模板ID ';
COMMENT ON COLUMN "public"."survey_standard_questionnaire"."code" IS '问卷code';
COMMENT ON COLUMN "public"."survey_standard_questionnaire"."scope_type" IS '标准问卷公开范围 公开 部分';
COMMENT ON COLUMN "public"."survey_standard_questionnaire"."type" IS '标准问卷类型';
COMMENT ON COLUMN "public"."survey_standard_questionnaire"."category_id" IS '标准问卷分类ID';
COMMENT ON COLUMN "public"."survey_standard_questionnaire"."is_service_statement" IS '标准问卷作答时间';
COMMENT ON COLUMN "public"."survey_standard_questionnaire"."status" IS '标准问卷状态';
COMMENT ON COLUMN "public"."survey_standard_questionnaire"."question_num_in_one_page" IS '标准问卷每页题数';
COMMENT ON COLUMN "public"."survey_standard_questionnaire"."answer_description" IS '标准问卷作答说明';
COMMENT ON COLUMN "public"."survey_standard_questionnaire"."duration" IS '标准问卷作答时间';
COMMENT ON TABLE "public"."survey_standard_questionnaire" IS '标准问卷表';


/*标准问卷分类表*/
DROP TABLE IF EXISTS "public"."survey_standard_questionnaire_category";
CREATE TABLE  "public"."survey_standard_questionnaire_category"
(
    "id"             VARCHAR(36)        NOT NULL,
    "name"           VARCHAR(100)       NOT NULL,
    "code"           VARCHAR(100),
    "create_by"      VARCHAR(36),
    "create_time"    TIMESTAMP(3)       DEFAULT NOW(),
    "update_time"    TIMESTAMP(3)       DEFAULT NOW(),
    "last_update_by" VARCHAR(36),
    "is_deleted"     BOOL               DEFAULT FALSE,
    "app_id"         VARCHAR(36),
     CONSTRAINT  "___pk__survey_standard_questionnaire_category___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_standard_questionnaire_category"."name" IS '问卷分类名称';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_category"."code" IS '问卷分类code';
COMMENT ON TABLE "public"."survey_standard_questionnaire_category" IS '标准问卷分类表';


/*标准问卷题目表*/
DROP TABLE IF EXISTS "public"."survey_standard_questionnaire_question";
CREATE TABLE  "public"."survey_standard_questionnaire_question"
(
    "id"                        VARCHAR(36) NOT NULL,
    "name"                      JSON,
    "type"                      VARCHAR(50),
    "standard_questionnaire_id" VARCHAR(36),
    "standard_question_id"      VARCHAR(36),
    "is_require"                BOOL,
    "options_value"             JSON,
    "parent_id"                 VARCHAR(36),
    "create_by"                 VARCHAR(36),
    "create_time"               TIMESTAMP(3) DEFAULT NOW(),
    "update_time"               TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"            VARCHAR(36),
    "is_deleted"                BOOL         DEFAULT FALSE,
    "sort"                      INT4,
    "app_id"                    VARCHAR(36),
    CONSTRAINT  "___pk__standard_questionnaire_question___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_standard_questionnaire_question"."name" IS '问卷题目名称';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_question"."type" IS '问卷题目类型';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_question"."standard_questionnaire_id" IS '标准问卷ID';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_question"."standard_question_id" IS '标准题目ID';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_question"."is_require" IS '是否必填';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_question"."options_value" IS '题目选项值';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_question"."parent_id" IS '父级ID 为0 代表父级';
COMMENT ON TABLE "public"."survey_standard_questionnaire_question" IS '标准问卷分类表';


/*标准常模值表*/
DROP TABLE IF EXISTS "public"."survey_standard_norm_value";
CREATE TABLE "public"."survey_standard_norm_value"
(
    "id"                   VARCHAR(36) NOT NULL,
    "standard_norm_id"     VARCHAR(36),
    "standard_question_id" VARCHAR(36),
    "value"                VARCHAR(100),
    "create_by"            VARCHAR(36),
    "create_time"          TIMESTAMP(3) DEFAULT NOW(),
    "update_time"          TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"       VARCHAR(36),
    "is_deleted"           BOOL         DEFAULT FALSE,
    "app_id"               VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_norm_value___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_standard_norm_value"."standard_norm_id" IS '标准常模ID';
COMMENT ON COLUMN "public"."survey_standard_norm_value"."standard_question_id" IS '标准题目ID';
COMMENT ON COLUMN "public"."survey_standard_norm_value"."value" IS '常模值';
COMMENT ON TABLE "public"."survey_standard_norm_value" IS '标准常模值表';


/*标准常模表*/
DROP TABLE IF EXISTS "public"."survey_standard_norm";
CREATE TABLE "public"."survey_standard_norm"
(
    "id"             VARCHAR(36) NOT NULL,
    "name"           JSON,
    "create_by"      VARCHAR(36),
    "create_time"    TIMESTAMP(3) DEFAULT NOW(),
    "update_time"    TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by" VARCHAR(36),
    "is_deleted"     BOOL         DEFAULT FALSE,
    "app_id"         VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_norm___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_standard_norm"."name" IS '常模名称';
COMMENT ON TABLE "public"."survey_standard_norm" IS '标准常模表';


/*常模公司映射表*/
DROP TABLE IF EXISTS "public"."survey_standard_norm_tenant_mapping";
CREATE TABLE "public"."survey_standard_norm_tenant_mapping"
(
    "standard_norm_id" VARCHAR(36),
    "tenant_id"        VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_norm_tenant_mapping___" PRIMARY KEY ("standard_norm_id", "tenant_id")
);
COMMENT ON COLUMN "public"."survey_standard_norm_tenant_mapping"."standard_norm_id" IS '常模ID';
COMMENT ON COLUMN "public"."survey_standard_norm_tenant_mapping"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."survey_standard_norm_tenant_mapping" IS '标准常模表';


/*题目包表*/
DROP TABLE IF EXISTS "public"."survey_standard_package";
CREATE TABLE "public"."survey_standard_package"
(
    "id"                   VARCHAR(36) NOT NULL,
    "name"                 JSON,
    "algorithm_type"       VARCHAR(50),
    "algorithm"            JSON,
    "count"                INT4,
    "question_type_filter" VARCHAR(50),
    "create_by"            VARCHAR(36),
    "create_time"          TIMESTAMP(3) DEFAULT NOW(),
    "update_time"          TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"       VARCHAR(36),
    "is_deleted"           BOOL         DEFAULT FALSE,
    "app_id"               VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_package___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_standard_package"."name" IS '题目包名称';
COMMENT ON COLUMN "public"."survey_standard_package"."algorithm_type" IS '题目包算法类型 ';
COMMENT ON COLUMN "public"."survey_standard_package"."algorithm" IS '题目包算法详情';
COMMENT ON COLUMN "public"."survey_standard_package"."count" IS '题目包所含题目数';
COMMENT ON COLUMN "public"."survey_standard_package"."question_type_filter" IS '题目类型过滤枚举值 量表 迫选 其他';
COMMENT ON TABLE "public"."survey_standard_package" IS '标准常模表';


/*题目包题目映射表*/
DROP TABLE IF EXISTS "public"."survey_standard_package_question_mapping";
CREATE TABLE "public"."survey_standard_package_question_mapping"
(
    "standard_package_id"  VARCHAR(36) NOT NULL,
    "standard_question_id" VARCHAR(36) NOT NULL,
    "standard_option_id"   VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_package_question_mapping___" PRIMARY KEY ("standard_package_id", "standard_question_id")
);
COMMENT ON COLUMN "public"."survey_standard_package_question_mapping"."standard_package_id" IS '题目包ID';
COMMENT ON COLUMN "public"."survey_standard_package_question_mapping"."standard_question_id" IS '标题题目ID';
COMMENT ON COLUMN "public"."survey_standard_package_question_mapping"."standard_option_id" IS '选项ID';
COMMENT ON TABLE "public"."survey_standard_package_question_mapping" IS '题目包题目映射表';


/*标准问卷维度表*/
DROP TABLE IF EXISTS "public"."survey_standard_questionnaire_dimension";
CREATE TABLE "public"."survey_standard_questionnaire_dimension"
(
    "id"                        VARCHAR(36) NOT NULL,
    "name"                      JSON,
    "type"                      VARCHAR(50),
    "group_type"                VARCHAR(50),
    "standard_questionnaire_id" VARCHAR(36),
    "standard_dimension_id"     VARCHAR(36),
    "sort"                      INT4,
    "code"                      VARCHAR(50),
    "standard_package_id"       VARCHAR(36),
    "create_by"                 VARCHAR(36),
    "create_time"               TIMESTAMP(3) DEFAULT NOW(),
    "update_time"               TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"            VARCHAR(36),
    "is_deleted"                BOOL         DEFAULT FALSE,
    "app_id"                    VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_questionnaire_dimension___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_standard_questionnaire_dimension"."name" IS '问卷维度名称';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_dimension"."type" IS '问卷维度来源类型 标准维度 自定义维度';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_dimension"."group_type" IS ' 问卷维度类型 子维度 大维度 ';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_dimension"."standard_questionnaire_id" IS '标准问卷ID';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_dimension"."standard_dimension_id" IS '标准维度ID';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_dimension"."sort" IS '排序值';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_dimension"."standard_package_id" IS '标准题目包ID';
COMMENT ON TABLE "public"."survey_standard_questionnaire_dimension" IS '标准问卷维度表';


/*标准维度关系表*/
DROP TABLE IF EXISTS "public"."survey_standard_questionnaire_dimension_mapping";
CREATE TABLE "public"."survey_standard_questionnaire_dimension_mapping"
(
    "parent_id" VARCHAR(36),
    "child_id"  VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_questionnaire_dimension_mapping___" PRIMARY KEY ("parent_id", "child_id")
);
COMMENT ON COLUMN "public"."survey_standard_questionnaire_dimension_mapping"."parent_id" IS '父级ID';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_dimension_mapping"."child_id" IS '子级ID';
COMMENT ON TABLE "public"."survey_standard_questionnaire_dimension_mapping" IS '标准问卷维度映射表';


/*标准问卷维度和题目映射表*/
DROP TABLE IF EXISTS "public"."survey_standard_questionnaire_dimension_question_mapping";
CREATE TABLE "public"."survey_standard_questionnaire_dimension_question_mapping"
(
    "standard_questionnaire_dimension_id" VARCHAR(36) REFERENCES survey_standard_questionnaire_dimension (id),
    "standard_questionnaire_question_id"  VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_questionnaire_dimension_question_mapping___" PRIMARY KEY ("standard_questionnaire_dimension_id","standard_questionnaire_question_id")
);
COMMENT ON COLUMN "public"."survey_standard_questionnaire_dimension_question_mapping"."standard_questionnaire_dimension_id" IS '标准问卷维度ID';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_dimension_question_mapping"."standard_questionnaire_question_id" IS '标准问卷题目ID';
COMMENT ON TABLE "public"."survey_standard_questionnaire_dimension_question_mapping" IS '标准问卷维度题目映射表';


/*标准人口统计学表*/
DROP TABLE IF EXISTS "public"."survey_standard_demographic";
CREATE TABLE "public"."survey_standard_demographic"
(
    "id"                            VARCHAR(36) NOT NULL,
    "standard_demographic_group_id" VARCHAR(36),
    "name"                          JSON,
    "parent_id"                     VARCHAR(36),
    "sort"                          INT4,
    "basic_label"                   VARCHAR(100),
    "create_by"                     VARCHAR(36),
    "create_time"                   TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                   TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                VARCHAR(36),
    "is_deleted"                    BOOL         DEFAULT FALSE,
    "app_id"                        VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_demographic___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_standard_demographic"."standard_demographic_group_id" IS '标准人口统计学分组ID';
COMMENT ON COLUMN "public"."survey_standard_demographic"."name" IS '标准人口统计学名称';
COMMENT ON COLUMN "public"."survey_standard_demographic"."parent_id" IS '标准人口统计学父级';
COMMENT ON COLUMN "public"."survey_standard_demographic"."sort" IS '标准人口统计学排序';
COMMENT ON COLUMN "public"."survey_standard_demographic"."basic_label" IS '标准人口统计学基础标签';
COMMENT ON TABLE "public"."survey_standard_demographic" IS '标准人口统计学表';


/*标准人口统计分组表*/
DROP TABLE IF EXISTS "public"."survey_standard_demographic_group";
CREATE TABLE "public"."survey_standard_demographic_group"
(
    "id"             VARCHAR(36) NOT NULL,
    "name"           JSON,
    "create_by"      VARCHAR(36),
    "create_time"    TIMESTAMP(3) DEFAULT NOW(),
    "update_time"    TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by" VARCHAR(36),
    "is_deleted"     BOOL         DEFAULT FALSE,
    "app_id"         VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_demographic_group___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_standard_demographic_group"."name" IS '标准人口统计分组名称';
COMMENT ON TABLE "public"."survey_standard_demographic_group" IS '标准人口统计学表';


/*标准人口统计题目表*/
DROP TABLE IF EXISTS "public"."survey_standard_demographic_question";
CREATE TABLE "public"."survey_standard_demographic_question"
(
    "id"                      VARCHAR(36) NOT NULL,
    "name"                    JSON,
    "standard_demographic_id" VARCHAR(36),
    "is_require"              BOOL,
    "type"                    VARCHAR(100),
    "rule"                    VARCHAR(100),
    "sort"                    INT4,
    "create_by"               VARCHAR(36),
    "create_time"             TIMESTAMP(3) DEFAULT NOW(),
    "update_time"             TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"          VARCHAR(36),
    "is_deleted"              BOOL         DEFAULT FALSE,
    "app_id"                  VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_demographic_question____" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_standard_demographic_question"."name" IS '标准人口统计学题目名称';
COMMENT ON COLUMN "public"."survey_standard_demographic_question"."standard_demographic_id" IS '标准人口统计学ID';
COMMENT ON COLUMN "public"."survey_standard_demographic_question"."is_require" IS '是否必填';
COMMENT ON COLUMN "public"."survey_standard_demographic_question"."type" IS '标准人口统计学类型';
COMMENT ON COLUMN "public"."survey_standard_demographic_question"."rule" IS '标准人口统计学规则';
COMMENT ON COLUMN "public"."survey_standard_demographic_question"."sort" IS '排序';
COMMENT ON TABLE "public"."survey_standard_demographic_question" IS '标准人口统计学题目表';


/*标准维度*/
DROP TABLE IF EXISTS "public"."survey_standard_dimension";
CREATE TABLE "public"."survey_standard_dimension"
(
    "id"                  VARCHAR(36) NOT NULL,
    "name"                JSON,
    "survey_type"         VARCHAR(50),
    "group_type"          VARCHAR(50),
    "code"                VARCHAR(50),
    "standard_package_id" VARCHAR(36),
    "algorithm_type"      VARCHAR(50),
    "description"         VARCHAR(50),
    "create_by"           VARCHAR(36),
    "create_time"         TIMESTAMP(3) DEFAULT NOW(),
    "update_time"         TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"      VARCHAR(36),
    "is_deleted"          BOOL         DEFAULT FALSE,
    "app_id"              VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_dimension___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_standard_dimension"."name" IS '维度名称';
COMMENT ON COLUMN "public"."survey_standard_dimension"."survey_type" IS '调研类型';
COMMENT ON COLUMN "public"."survey_standard_dimension"."group_type" IS '标准维度类型 子维度 大维度';
COMMENT ON COLUMN "public"."survey_standard_dimension"."code" IS '维度code';
COMMENT ON COLUMN "public"."survey_standard_dimension"."standard_package_id" IS '标准题目包ID';
COMMENT ON COLUMN "public"."survey_standard_dimension"."algorithm_type" IS '计算方式 只有大维度才有 ';
COMMENT ON COLUMN "public"."survey_standard_dimension"."description" IS '维度描述';
COMMENT ON TABLE "public"."survey_standard_dimension" IS '标准维度表';


/*标准维度关系表*/
DROP TABLE IF EXISTS "public"."survey_standard_dimension_mapping";
CREATE TABLE "public"."survey_standard_dimension_mapping"
(
    "parent_id" VARCHAR(36),
    "child_id"  VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_dimension_mapping___" PRIMARY KEY ("parent_id", "child_id")
);
COMMENT ON COLUMN "public"."survey_standard_dimension_mapping"."parent_id" IS '父级ID';
COMMENT ON COLUMN "public"."survey_standard_dimension_mapping"."child_id" IS '子级ID';
COMMENT ON TABLE "public"."survey_standard_dimension_mapping" IS '标准维度父子级映射表';


/*标准维度和题目*/
DROP TABLE IF EXISTS "public"."survey_standard_dimension_question_mapping";
CREATE TABLE "public"."survey_standard_dimension_question_mapping"
(
    "standard_dimension_id" VARCHAR(36) REFERENCES survey_standard_dimension (id),
    "standard_question_id"  VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_dimension_question_mapping___" PRIMARY KEY ("standard_dimension_id", "standard_question_id")
);
COMMENT ON COLUMN "public"."survey_standard_dimension_question_mapping"."standard_dimension_id" IS '标准维度ID';
COMMENT ON COLUMN "public"."survey_standard_dimension_question_mapping"."standard_question_id" IS '标准题目ID';
COMMENT ON TABLE "public"."survey_standard_dimension_question_mapping" IS '标准维度题目映射表';


/*标准问卷分配公司*/
DROP TABLE IF EXISTS "public"."survey_standard_questionnaire_tenant_mapping";
CREATE TABLE "public"."survey_standard_questionnaire_tenant_mapping"
(
    "standard_questionnaire_id" VARCHAR(36),
    "tenant_id"                 VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_questionnaire_tenant_mapping___" PRIMARY KEY ("standard_questionnaire_id", "tenant_id")
);
COMMENT ON COLUMN "public"."survey_standard_questionnaire_tenant_mapping"."standard_questionnaire_id" IS '标准问卷ID';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_tenant_mapping"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."survey_standard_questionnaire_tenant_mapping" IS '标准问卷分配公司表';


/*标准维度和公司的映射表*/
DROP TABLE IF EXISTS "public"."survey_standard_dimension_tenant_mapping";
CREATE TABLE "public"."survey_standard_dimension_tenant_mapping"
(
    "standard_dimension_id" VARCHAR(36),
    "tenant_id"             VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_dimension_tenant_mapping___" PRIMARY KEY ("standard_dimension_id", "tenant_id")
);
COMMENT ON COLUMN "public"."survey_standard_dimension_tenant_mapping"."standard_dimension_id" IS '标准维度ID';
COMMENT ON COLUMN "public"."survey_standard_dimension_tenant_mapping"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."survey_standard_dimension_tenant_mapping" IS '标准维度和公司的映射表';


/*选项表 */
DROP TABLE IF EXISTS "public"."survey_standard_option";
CREATE TABLE "public"."survey_standard_option"
(
    "id"             VARCHAR(36) NOT NULL,
    "survey_type"    VARCHAR(50),
    "question_type"  VARCHAR(50),
    "name"           JSON,
    "options"        JSON,
    "create_by"      VARCHAR(36),
    "create_time"    TIMESTAMP(3) DEFAULT NOW(),
    "update_time"    TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by" VARCHAR(36),
    "is_deleted"     BOOL         DEFAULT FALSE,
    "app_id"         VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_option___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_standard_option"."survey_type" IS '调研类型';
COMMENT ON COLUMN "public"."survey_standard_option"."question_type" IS '题目类型 单选 多选 量表';
COMMENT ON COLUMN "public"."survey_standard_option"."name" IS '选项名称';
COMMENT ON COLUMN "public"."survey_standard_option"."options" IS '选项值';
COMMENT ON TABLE "public"."survey_standard_option" IS '选项表';


/*标准报告模板表*/
DROP TABLE IF EXISTS "public"."survey_standard_sag_report_template";
CREATE TABLE "public"."survey_standard_sag_report_template"
(
    "id"                   VARCHAR(36) NOT NULL,
    "name"                 JSON,
    "code"                 VARCHAR(50),
    "sample_url"           VARCHAR(100),
    "type"                 VARCHAR(50),
    "standard_report_type" VARCHAR(50),
    "standard_report_id"   VARCHAR(36),
    "status"               VARCHAR(50),
    "tenant_id"            VARCHAR(36),
    "create_by"            VARCHAR(36),
    "create_time"          TIMESTAMP(3),
    "update_time"          TIMESTAMP(3),
    "last_update_by"       VARCHAR(36),
    "is_deleted"           BOOL DEFAULT FALSE,
    "app_id"               VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_sag_report_template___" PRIMARY KEY (id)
);
ALTER TABLE "public"."survey_standard_sag_report_template"
    ADD CONSTRAINT "___uk_code___" UNIQUE ("code");

COMMENT ON COLUMN "public"."survey_standard_sag_report_template"."name" IS '报告名称';
COMMENT ON COLUMN "public"."survey_standard_sag_report_template"."code" IS '报告模板code';
COMMENT ON COLUMN "public"."survey_standard_sag_report_template"."sample_url" IS '报告模板路径';
COMMENT ON COLUMN "public"."survey_standard_sag_report_template"."type" IS '报告类型(定制和标准)';
COMMENT ON COLUMN "public"."survey_standard_sag_report_template"."standard_report_type" IS '报告类型（4种报告）';
COMMENT ON COLUMN "public"."survey_standard_sag_report_template"."standard_report_id" IS '标准报告id，定制报告才有';
COMMENT ON COLUMN "public"."survey_standard_sag_report_template"."status" IS '报告状态，新建默认OFF';
COMMENT ON COLUMN "public"."survey_standard_sag_report_template"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."survey_standard_sag_report_template" IS '标准报告模板表';


/*标准报告模板配置表*/
DROP TABLE IF EXISTS "public"."survey_standard_sag_report_template_config";
CREATE TABLE "public"."survey_standard_sag_report_template_config"
(
    "id"                 VARCHAR(36) NOT NULL,
    "report_template_id" VARCHAR(36),
    "type"               VARCHAR(50),
    "data"               JSON,
    "create_by"          VARCHAR(36),
    "create_time"        TIMESTAMP(3),
    "update_time"        TIMESTAMP(3),
    "last_update_by"     VARCHAR(36),
    "is_deleted"         BOOL,
    "app_id"             VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_sag_report_template_config___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."survey_standard_sag_report_template_config"."report_template_id" IS '报告模板ID';
COMMENT ON COLUMN "public"."survey_standard_sag_report_template_config"."type" IS '报告模块 适应度详情 详细得分 适应度 适应度水平 封面 总览概述';
COMMENT ON COLUMN "public"."survey_standard_sag_report_template_config"."data" IS '编辑模块数据 key：模板名/模块名/付费，val：名字/数据/具体付费';
COMMENT ON TABLE "public"."survey_standard_sag_report_template_config" IS '标准报告模板配置表';


/*标准报告维度表*/
DROP TABLE IF EXISTS "public"."survey_standard_sag_report_dimension";
CREATE TABLE "public"."survey_standard_sag_report_dimension"
(
    "id"                                VARCHAR(36) NOT NULL,
    "name"                              JSON,
    "description"                       JSON,
    "close_left_description"            JSON,
    "close_right_description"           JSON,
    "code"                              VARCHAR(50),
    "close_left_describe_advantage"     JSON,
    "close_left_describe_disadvantage"  JSON,
    "close_right_describe_advantage"    JSON,
    "close_right_describe_disadvantage" JSON,
    "sort"                              INT4,
    "table_name"                        VARCHAR(100),
    "create_by"                         VARCHAR(36),
    "create_time"                       TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                       TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                    VARCHAR(36),
    "is_deleted"                        BOOL         DEFAULT FALSE,
    "app_id"                            VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_sag_report_dimension___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."name" IS '维度名字';
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."description" IS '维度解释';
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."close_left_description" IS '分数靠左解释';
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."close_right_description" IS '分数靠右解释';
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."code" IS '维度唯一编码';
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."close_left_describe_advantage" IS '维度靠左优势描述';
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."close_left_describe_disadvantage" IS '维度靠左劣势描述';
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."close_right_describe_advantage" IS '维度靠右优势描述';
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."close_right_describe_disadvantage" IS '维度靠右劣势描述';
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."sort" IS '维度排序';
COMMENT ON COLUMN "public"."survey_standard_sag_report_dimension"."table_name" IS '表名';
COMMENT ON TABLE "public"."survey_standard_sag_report_dimension" IS '报告维度';


/*标准报告分值表*/
DROP TABLE IF EXISTS "public"."survey_standard_sag_report_score";
CREATE TABLE "public"."survey_standard_sag_report_score"
(
    "id"             VARCHAR(36) NOT NULL,
    "type"           VARCHAR(50),
    "code"           VARCHAR(50),
    "name"           JSON,
    "high_score"     NUMERIC(53, 2),
    "low_score"      NUMERIC(53, 2),
    "description"    JSON,
    "comment"        JSON,
    "advantage"      JSON,
    "development"    JSON,
    "table_name"     VARCHAR(100),
    "create_by"      VARCHAR(36),
    "create_time"    TIMESTAMP(3),
    "update_time"    TIMESTAMP(3),
    "last_update_by" VARCHAR(36),
    "is_deleted"     BOOL,
    "app_id"         VARCHAR(36),
    CONSTRAINT  "___pk__survey_standard_sag_report_score___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."survey_standard_sag_report_score"."type" IS '标准报告类型 工作成就动机评测 能力评测 职业性格评测 危机评测 人才画像评测';
COMMENT ON COLUMN "public"."survey_standard_sag_report_score"."code" IS '维度编码';
COMMENT ON COLUMN "public"."survey_standard_sag_report_score"."name" IS '维度名字';
COMMENT ON COLUMN "public"."survey_standard_sag_report_score"."high_score" IS '分段高分值';
COMMENT ON COLUMN "public"."survey_standard_sag_report_score"."low_score" IS '分段低分值';
COMMENT ON COLUMN "public"."survey_standard_sag_report_score"."description" IS '描述';
COMMENT ON COLUMN "public"."survey_standard_sag_report_score"."comment" IS '评语';
COMMENT ON COLUMN "public"."survey_standard_sag_report_score"."advantage" IS '优势';
COMMENT ON COLUMN "public"."survey_standard_sag_report_score"."development" IS '发展';
COMMENT ON COLUMN "public"."survey_standard_sag_report_score"."table_name" IS '表名';
COMMENT ON TABLE "public"."survey_standard_sag_report_score" IS '标准报告分值表';


/*标准常模转换表*/
DROP TABLE IF EXISTS "public"."survey_standard_sag_norm_transform";
CREATE TABLE "public"."survey_standard_sag_norm_transform"
(
    "id"          INT4 NOT NULL,
    "type"        VARCHAR(50),
    "low_tenths"  NUMERIC(53, 2),
    "high_tenths" NUMERIC(53, 2),
    "table_name"  VARCHAR(100),
    CONSTRAINT  "___pk__survey_standard_sag_norm_transform___" PRIMARY KEY ("id","type")
);
COMMENT ON COLUMN "public"."survey_standard_sag_norm_transform"."id" IS '分值';
COMMENT ON COLUMN "public"."survey_standard_sag_norm_transform"."type" IS '报告类型';
COMMENT ON COLUMN "public"."survey_standard_sag_norm_transform"."low_tenths" IS '十分质的最低分值';
COMMENT ON COLUMN "public"."survey_standard_sag_norm_transform"."high_tenths" IS '十分质的最高分值';
COMMENT ON COLUMN "public"."survey_standard_sag_norm_transform"."table_name" IS 'Ecexl表名';
COMMENT ON TABLE "public"."survey_standard_sag_norm_transform" IS '标准报告分值表';


/*标准报告模块表*/
DROP TABLE IF EXISTS "public"."survey_standard_sag_report_module";
CREATE TABLE "public"."survey_standard_sag_report_module"
(
    "key"         VARCHAR(100) NOT NULL,
    "parent_name" VARCHAR(100),
    "name"        VARCHAR(100),
    "edit"        VARCHAR(100),
    CONSTRAINT  "___pk__survey_standard_sag_report_module___" PRIMARY KEY (key)
);
COMMENT ON COLUMN "public"."survey_standard_sag_report_module"."key" IS '模块key';
COMMENT ON COLUMN "public"."survey_standard_sag_report_module"."parent_name" IS '父模块名';
COMMENT ON COLUMN "public"."survey_standard_sag_report_module"."name" IS '模块名';
COMMENT ON COLUMN "public"."survey_standard_sag_report_module"."edit" IS '是否可编辑';
COMMENT ON TABLE "public"."survey_standard_sag_report_module" IS '标准报告模块表';
