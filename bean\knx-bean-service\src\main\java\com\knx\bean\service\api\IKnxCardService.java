package com.knx.bean.service.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knx.bean.model.entity.KnxCard;
import com.knx.bean.model.vo.CardResultExcelVO;
import com.knx.bean.model.vo.CardSelectVO;
import com.knx.common.base.web.response.PageRequest;

import java.util.List;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/10/16 13:40
 */
public interface IKnxCardService extends IService<KnxCard> {

    PageRequest pageList(CardSelectVO cardSelectVO);

    List<KnxCard> saveBatchCard(List<KnxCard> knxCards);

    String checkoutCardPassword(String password);

    Integer updateBatchByPassword(List<KnxCard> knxCards);

    Integer updateRechargeBatchByPassword(List<KnxCard> knxCards);

    Boolean batchCheckoutCardPassword(List<String> passwords);

    Boolean batchCheckoutCardPasswordIsRecharge(List<String> passwords);

    List<KnxCard> listByCardPassword(List<String> cardPasswords);

    List<CardResultExcelVO> listExcel(CardSelectVO cardSelectVO);

}
