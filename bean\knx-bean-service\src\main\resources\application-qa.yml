spring:
  datasource:
    url: jdbc:postgresql://***********:5432/knx_bean
    username: <PERSON><PERSON>(oBBwyNMCVQJpXZwv7ec0yg==)
    password: ENC(MvaNwrHgymNpKzfcKdfGGK1Wi3/rtpku)
  kafka:
    bootstrap-servers: ***********:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
redis:
  host: ***********
  password: ENC(uYqlv3o5vRva0q/YmnjrO0xJY0WAm82c)
  port: 30479

swagger:
  show: true
