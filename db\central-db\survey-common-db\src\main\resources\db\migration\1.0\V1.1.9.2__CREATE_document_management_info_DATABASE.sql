CREATE TABLE document_management_info (
	id varchar(36) NOT NULL,
    name json NULL,
    front_cover varchar(50) NULL,
    status varchar(50) NULL DEFAULT 'DISABLE',
    is_common_file bool NULL DEFAULT false ,
    sort numeric(11),
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__document_management_info___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."document_management_info"."name" IS '名称';
COMMENT ON COLUMN "public"."document_management_info"."front_cover" IS '封面';
COMMENT ON COLUMN "public"."document_management_info"."status" IS '状态';
COMMENT ON COLUMN "public"."document_management_info"."is_common_file" IS '是否常见文件';
COMMENT ON COLUMN "public"."document_management_info"."sort" IS '排序';
COMMENT ON TABLE  "public"."document_management_info" IS '文档管理';
