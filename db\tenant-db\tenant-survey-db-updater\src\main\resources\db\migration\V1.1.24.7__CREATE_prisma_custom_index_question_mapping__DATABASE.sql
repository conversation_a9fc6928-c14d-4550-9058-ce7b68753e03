CREATE TABLE public.prisma_custom_index_question_mapping (
	id varchar(36) NOT NULL,
	prisma_report_data_id varchar(36) NOT NULL,
	prisma_custom_index_combination_id  varchar(36) NOT NULL,
	index_id varchar(36) NOT NULL,
	parent_dimension_id varchar(36) NULL,
	dimension_id varchar(36)  NULL,
	question_id varchar(36) NOT NULL,
	group_key varchar(36) NOT NULL,
	create_by varchar(36) NOT NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) NULL,
	CONSTRAINT "___pk___prisma_custom_index_question_mapping___" PRIMARY KEY (id)
);
COMMENT ON TABLE  "public"."prisma_custom_index_question_mapping" IS 'prisma自定义指数题目关系表';
COMMENT ON COLUMN "public"."prisma_custom_index_question_mapping"."prisma_report_data_id" IS 'prisma报告ID';
COMMENT ON COLUMN "public"."prisma_custom_index_question_mapping"."prisma_custom_index_combination_id" IS 'prisma自定义指数组合ID';
COMMENT ON COLUMN "public"."prisma_custom_index_question_mapping"."index_id" IS '指数ID';
COMMENT ON COLUMN "public"."prisma_custom_index_question_mapping"."parent_dimension_id" IS '一级维度ID';
COMMENT ON COLUMN "public"."prisma_custom_index_question_mapping"."dimension_id" IS '二级维度ID';
COMMENT ON COLUMN "public"."prisma_custom_index_question_mapping"."question_id" IS '题目ID';
COMMENT ON COLUMN "public"."prisma_custom_index_question_mapping"."group_key" IS '分组key';