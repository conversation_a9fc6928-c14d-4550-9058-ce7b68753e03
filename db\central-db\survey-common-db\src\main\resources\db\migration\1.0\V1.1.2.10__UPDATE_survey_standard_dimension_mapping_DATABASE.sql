ALTER TABLE survey_standard_dimension_mapping DROP CONSTRAINT ___pk__survey_standard_dimension_mapping___;
ALTER TABLE survey_standard_dimension_mapping ADD COLUMN id SERIAL PRIMARY KEY;
ALTER TABLE survey_standard_dimension_mapping ALTER COLUMN id TYPE varchar(36) USING id::varchar;
ALTER TABLE survey_standard_dimension_mapping ALTER COLUMN id DROP DEFAULT;
CREATE INDEX ___idx_survey_standard_dimension_mapping_parent_id_child_id___ ON survey_standard_dimension_mapping(parent_id, child_id);

ALTER TABLE survey_standard_dimension_mapping ADD "create_by" varchar(36) NULL;
ALTER TABLE survey_standard_dimension_mapping ADD "create_time" TIMESTAMP(3) DEFAULT NOW();
ALTER TABLE survey_standard_dimension_mapping ADD "update_time" TIMESTAMP(3) DEFAULT NOW();
ALTER TABLE survey_standard_dimension_mapping ADD "last_update_by" varchar(36) NULL;
ALTER TABLE survey_standard_dimension_mapping ADD is_deleted bool NULL DEFAULT false;
ALTER TABLE survey_standard_dimension_mapping ADD "app_id" varchar(36) NULL;