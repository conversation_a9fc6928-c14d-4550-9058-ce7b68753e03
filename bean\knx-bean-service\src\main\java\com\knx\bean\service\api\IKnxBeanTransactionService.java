package com.knx.bean.service.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knx.bean.model.entity.KnxBeanTransaction;
import com.knx.bean.model.vo.BeanTransactionExcelVo;

import java.util.List;

/**
 * 租户肯豆账户明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-05-21 15:00:28
 */
public interface IKnxBeanTransactionService extends IService<KnxBeanTransaction> {

    boolean saveTransaction(KnxBeanTransaction knxBeanTransaction);

    void recharge(KnxBeanTransaction knxBeanTransaction);

    boolean cancelRecharge(String seqNo);

    boolean spend(KnxBeanTransaction knxBeanTransaction);

    List<KnxBeanTransaction> listByTenantId(String tenantId);

    List<BeanTransactionExcelVo> export(String tenantId);

    void messageRecharge(KnxBeanTransaction knxBeanTransaction);

    boolean messageSpend(KnxBeanTransaction knxBeanTransaction);
}

