package com.knx.survey.common.base;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class BaseServiceImpl<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> {


    /**
     * 查询（根据ID 批量查询）
     *
     * @param idList 主键ID列表
     */
    @Override
    public List<T> listByIds(Collection<? extends Serializable> idList) {
        if (idList == null || idList.isEmpty()) {
            return Collections.EMPTY_LIST;
        }
        return super.listByIds(idList);
    }

    public List<T> listBatchByIds(List<String> idList, int size) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        idList = idList.stream().distinct().collect(Collectors.toList());
        List<T> datas = new ArrayList<>();
        List<List<String>> batchs = ListUtil.split(idList, size);
        for (List<? extends Serializable> batch : batchs) {
            datas.addAll(listByIds(batch));
        }

        return datas;
    }
}
