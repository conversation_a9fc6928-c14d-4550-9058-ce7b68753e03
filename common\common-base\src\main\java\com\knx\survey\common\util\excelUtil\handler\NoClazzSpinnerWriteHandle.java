package com.knx.survey.common.util.excelUtil.handler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;

import java.util.*;

public class NoClazzSpinnerWriteHandle implements SheetWriteHandler {

    private  Map<Integer, String[]> mapDropDown = new HashMap<>();

    public NoClazzSpinnerWriteHandle( Map<Integer, String[]> maps){
        mapDropDown = maps;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

    }

    @Override
    @SneakyThrows
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

        Sheet sheet = writeSheetHolder.getSheet();
        DataValidationHelper helper = sheet.getDataValidationHelper();//设置下拉框
        if(mapDropDown != null) {
            for (Integer key : mapDropDown.keySet()) {
                CellRangeAddressList addressList = new CellRangeAddressList(1, 10000, key, key);
                DataValidationConstraint explicitListConstraint = helper.createExplicitListConstraint(mapDropDown.get(key));
                DataValidation dataValidation = helper.createValidation(explicitListConstraint, addressList);
                if (dataValidation instanceof XSSFDataValidation) {
                    dataValidation.setSuppressDropDownArrow(true);
                    dataValidation.setShowErrorBox(true);
                } else {
                    dataValidation.setSuppressDropDownArrow(false);
                }
                sheet.addValidationData(dataValidation);
            }
        }
    }
}
