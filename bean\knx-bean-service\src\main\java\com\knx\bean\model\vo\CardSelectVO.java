package com.knx.bean.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.knx.bean.model.enums.KnxCardTypeEnum;
import com.knx.common.base.web.response.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/10/16 14:23
 */

@Data
public class CardSelectVO {

    @ApiModelProperty("分页")
    private PageRequest pageRequest;
    @ApiModelProperty("当前时间")
    @JsonIgnore
    private String today;
    @ApiModelProperty("创建人ids")
    private List<String> createByIds;
    @ApiModelProperty("租户姓名")
    private String tenantName;
    @ApiModelProperty("租户id")
    private List<String> tenantIds;
    @ApiModelProperty("卡片类型")
    private KnxCardTypeEnum cardType;
    @ApiModelProperty("是否被使用")
    private Boolean isUsed;
    @ApiModelProperty("是否过期")
    private Boolean isExpired;
    @ApiModelProperty("创建日期开始")
    private Date startCreateDate;
    @ApiModelProperty("创建日期结束")
    private Date endCreateDate;
    @ApiModelProperty("创建日期开始字符串")
    @JsonIgnore
    private String startCreateDateString;
    @ApiModelProperty("创建日期结束字符串")
    @JsonIgnore
    private String endCreateDateString;

}
