package com.knx.survey.common.util;

import com.alibaba.fastjson.JSON;
import com.knx.survey.model.anno.LanguageType;
import com.knx.survey.model.enums.LanguageTypeEnum;
import com.knx.common.base.field.CommonField;
import com.knx.survey.model.I18NString;
import com.knx.common.base.service.exception.ErrorCode;
import com.knx.survey.common.base.field.SurveyCommonField;
import com.knx.survey.common.base.field.SurveyField;
import com.knx.survey.model.anno.I18NStringFormat;
import com.knx.survey.model.anno.LanguageFormat;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/8/18 17:27
 */
public class I18NStringUtil {


    @SneakyThrows
    public static Boolean isEmpty(I18NString i18NString) {
        if (i18NString == null) {
            return Boolean.TRUE;
        }
        Class<? extends I18NString> clazz = i18NString.getClass();
        Field[] declaredFields = clazz.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            boolean present = declaredField.isAnnotationPresent(LanguageType.class);
            if (present) {
                LanguageType languageType = declaredField.getAnnotation(LanguageType.class);
                if (languageType.type() != LanguageTypeEnum.EMPTY) {
                    Object object = declaredField.get(i18NString);
                    if (object instanceof String) {
                        String s = (String) object;
                        if (StringUtils.isNotEmpty(s)) {
                            return Boolean.FALSE;
                        }
                    }
                }
            }
        }

        return Boolean.TRUE;
    }

    /**
     * @param target  目标对象
     * @param sources 多语言来源数组
     * @return 目标对象
     */
    @SneakyThrows
    public static <T> T formatI18NString(T target, Object... sources) {
        Class<?> targetClass = target.getClass();
        Field[] fields = targetClass.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            boolean present = field.isAnnotationPresent(I18NStringFormat.class);
            if (present) {
                String name = field.getName();
                Method setTargetDeclaredMethod = targetClass.getDeclaredMethod(CommonField.SET + name.substring(0, 1).toUpperCase() + name.substring(1), field.getType());
                I18NString i18NString = new I18NString();
                Class<? extends I18NString> i18NStringClass = i18NString.getClass();
                Field[] i18NStringFields = i18NStringClass.getDeclaredFields();
                for (Object source : sources) {
                    Class<?> sourceClass = source.getClass();
                    List<Field> sourceFields = Arrays.asList(sourceClass.getDeclaredFields());
                    Field sourceClassField = sourceFields.stream().filter(n -> Objects.equals(n.getName(), name)).findFirst().orElse(null);
                    if (sourceClassField == null) {
                        continue;
                    }
                    sourceClassField.setAccessible(true);
                    Method getDeclaredMethod = sourceClass.getDeclaredMethod(CommonField.GET + name.substring(0, 1).toUpperCase() + name.substring(1));
                    getDeclaredMethod.setAccessible(true);
                    Field[] declaredFields = sourceClass.getDeclaredFields();
                    LanguageTypeEnum type = null;
                    for (Field declaredField : declaredFields) {
                        if (declaredField.getType().equals((LanguageTypeEnum.class))) {
                            declaredField.setAccessible(true);
                            Object obj = declaredField.get(source);
                            if (obj instanceof LanguageTypeEnum) {
                                type = (LanguageTypeEnum) obj;
                                break;
                            }
                        }
                    }
                    for (Field i18NStringField : i18NStringFields) {
                        if (i18NStringField.isAnnotationPresent(LanguageType.class)) {
                            String i18NStringFieldName = i18NStringField.getName();
                            LanguageType i18NStringLanguageType = i18NStringField.getAnnotation(LanguageType.class);
                            if (Objects.equals(type, i18NStringLanguageType.type())) {
                                Method setDeclaredMethod = i18NStringClass.getDeclaredMethod(CommonField.SET + i18NStringFieldName.substring(0, 1).toUpperCase() + i18NStringFieldName.substring(1), i18NStringField.getType());
                                Object object = getDeclaredMethod.invoke(source);
                                if (object instanceof String) {
                                    String str = (String) object;
                                    setDeclaredMethod.invoke(i18NString, str);
                                }
                            }
                        }
                    }
                }
                setTargetDeclaredMethod.invoke(target, i18NString);
            }
        }
        return target;
    }


    /**
     * 多语言转成单语言StringMap集合
     *
     * @param tClass
     * @param arg1
     * @param <T>
     * @return
     */
    @SneakyThrows
    public static <T> Map<LanguageTypeEnum, T> i18NObjectToStringMap(Class<T> tClass, Object arg1) {
        LanguageTypeEnum[] values = LanguageTypeEnum.values();
        List<LanguageTypeEnum> languageTypeEnums = new ArrayList<>();
        for (LanguageTypeEnum value : values) {
            if (value == LanguageTypeEnum.ZH_CN) {
                languageTypeEnums.add(0, value);
            } else {
                languageTypeEnums.add(value);
            }
        }
        Map<LanguageTypeEnum, T> map = new LinkedHashMap<>();
        for (LanguageTypeEnum value : languageTypeEnums) {
            T newInstance = tClass.newInstance();
            if (value != LanguageTypeEnum.EMPTY) {
                T t = getLanguageFormatObject(value, newInstance, arg1);
                map.put(value, t);
            }
        }
        return map;
    }

    /**
     * @param type 语言类型
     * @param arg0 目标对象
     * @param arg1 来源对象
     * @return 目标对象
     */
    @SneakyThrows
    public static <T> T getLanguageFormatObject(LanguageTypeEnum type, T arg0, Object arg1) {
        if (type == null || arg0 == null || arg1 == null) {
            return null;
        }
        Class<?> target = arg0.getClass();
        Field[] fields = target.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            boolean present = field.isAnnotationPresent(LanguageFormat.class);
            if (present) {
                LanguageFormat languageFormat = field.getAnnotation(LanguageFormat.class);

                Class value = languageFormat.value();
                //来源字节码对象
                Class<?> source = arg1.getClass();
                //来源对象要和注解里的来源对象相同
                if (Objects.equals(value, source)) {
                    //得到目标字段
                    String fieldName = languageFormat.field();
                    if (fieldName.equals(SurveyCommonField.EMPTY)) {
                        fieldName = field.getName();
                    }

                    //得到对应对象字段的get方法
                    Method method = source.getDeclaredMethod(SurveyCommonField.GET + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1));
                    method.setAccessible(true);
                    Object object = method.invoke(arg1);
                    if (object instanceof I18NString) {
                        I18NString content = (I18NString) object;
                        //得到目标对象的set方法
                        Method declaredMethod = target.getDeclaredMethod(SurveyCommonField.SET + field.getName().substring(0, 1).toUpperCase() + field.getName().substring(1), field.getType());

                        //目标对象的字段得是String类型
                        if (Objects.equals(field.getType(), String.class)) {
                            Class<? extends I18NString> i18NStringClass = content.getClass();
                            Field[] i18NStringClassFields = i18NStringClass.getDeclaredFields();
                            for (Field i18NStringClassField : i18NStringClassFields) {
                                if (i18NStringClassField.isAnnotationPresent(LanguageType.class)) {
                                    LanguageType languageType = i18NStringClassField.getAnnotation(LanguageType.class);
                                    if (Objects.equals(languageType.type(), type)) {
                                        i18NStringClassField.setAccessible(true);
                                        Object obj = i18NStringClassField.get(content);
                                        if (obj instanceof String) {
                                            String text = (String) obj;
                                            declaredMethod.invoke(arg0, text);
                                            break;
                                        }
                                    }
                                }
                            }
                        }

                    }

                }
            }
        }
        return arg0;
    }

    @SneakyThrows
    public static String i18NStringToString(I18NString i18NString, LanguageTypeEnum type) {
        if (i18NString == null || type == null) {
            return StringUtils.EMPTY;
        }
        Class<? extends I18NString> clazz = i18NString.getClass();
        Field[] declaredFields = clazz.getDeclaredFields();

        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            boolean present = declaredField.isAnnotationPresent(LanguageType.class);
            if (present) {
                LanguageType languageType = declaredField.getAnnotation(LanguageType.class);
                if (Objects.equals(languageType.type(), type)) {
                    Object object = declaredField.get(i18NString);
                    if (object instanceof String) {
                        return (String) object;
                    }
                }
            }
        }
        return StringUtils.EMPTY;
    }

    @SneakyThrows
    public static void setI18NString(I18NString i18NString, String val, LanguageTypeEnum type) {
        if (i18NString == null || type == null) {
            return;
        }
        Class<? extends I18NString> clazz = i18NString.getClass();
        Field[] declaredFields = clazz.getDeclaredFields();

        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            boolean present = declaredField.isAnnotationPresent(LanguageType.class);
            if (present) {
                LanguageType languageType = declaredField.getAnnotation(LanguageType.class);
                if (Objects.equals(languageType.type(), type)) {
                    declaredField.set(i18NString, val);
                }
            }
        }
    }

    @SneakyThrows
    public static Map<LanguageTypeEnum, String> i18NStringToMap(I18NString i18NString) {
        Class<? extends I18NString> clazz = i18NString.getClass();
        Field[] declaredFields = clazz.getDeclaredFields();
        Map<LanguageTypeEnum, String> map = new HashMap<>();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            boolean present = declaredField.isAnnotationPresent(LanguageType.class);
            if (present) {
                LanguageType languageType = declaredField.getAnnotation(LanguageType.class);
                LanguageTypeEnum type = languageType.type();
                Object object = declaredField.get(i18NString);
                map.put(type, (String) object);
            }
        }

        return map;
    }


    @SneakyThrows
    public static void replaceName(Object o, String name) {
        if (o != null) {
            Class<?> aClass = o.getClass();
            Field[] declaredFields = aClass.getDeclaredFields();
            for (Field declaredField : declaredFields) {
                declaredField.setAccessible(true);
                Object obj = declaredField.get(o);
                if (obj instanceof String) {
                    String str = (String) obj;
                    String replace = StringUtils.replace(str, SurveyCommonField.SAMPLE_TEST, name);
                    declaredField.set(o, replace);
                }
            }
        }
    }

    @SneakyThrows
    public static List<String> toList(I18NString i18NString) {
        if (i18NString == null) {
            return new ArrayList<>();
        }
        Class<? extends I18NString> aClass = i18NString.getClass();
        List<String> objects = new ArrayList<>();
        Field[] declaredFields = aClass.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            Object o = declaredField.get(i18NString);
            if (o instanceof String) {
                objects.add((String) o);
            }
        }
        return objects;
    }

    @SneakyThrows
    public static String toString(I18NString i18NString) {
        if (i18NString == null) {
            return null;
        }
        Class<? extends I18NString> aClass = i18NString.getClass();
        Field[] declaredFields = aClass.getDeclaredFields();
        String s = "";
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            Object o = declaredField.get(i18NString);
            if (o instanceof String) {
                s += (o + SurveyField.SLASH_SEPARATE);
            }
        }
        return s.substring(NumberUtils.INTEGER_ZERO, s.length() - 1);
    }

    /**
     * 按题目规则替换占位符
     *
     * @param i18NString
     */
    @SneakyThrows
    public static void replaceQuestion(I18NString i18NString) {
        if (i18NString == null) {
            return;
        }
        Class<? extends I18NString> aClass = i18NString.getClass();
        Field[] declaredFields = aClass.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            Object o = declaredField.get(i18NString);
            if (o instanceof String) {
                String content = ServiceUtil.replaceQuestionRegex((String) o);
                declaredField.set(i18NString, content);
            }
        }
    }

    /**
     * 占位符替换
     *
     * @param i18NString
     * @param replace
     */
    @SneakyThrows
    public static void replaceQuestion(I18NString i18NString, I18NString replace) {
        if (i18NString == null || replace == null) {
            return;
        }
        Class<? extends I18NString> aClass = i18NString.getClass();
        Class<? extends I18NString> replaceClass = replace.getClass();
        Field[] declaredFields = aClass.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            String fieldName = declaredField.getName();
            Field replaceFieldName = replaceClass.getDeclaredField(fieldName);
            replaceFieldName.setAccessible(true);
            Object replaceOb = replaceFieldName.get(replace);
            Object o = declaredField.get(i18NString);
            if (o instanceof String && replaceOb instanceof String) {
                String content = ServiceUtil.replaceQuestionRegex((String) o, (String) replaceOb);
                declaredField.set(i18NString, content);
            }

        }
    }

    /**
     * 去除题目中html标签
     */
    @SneakyThrows
    public static void delQuestionHtmlTags(I18NString i18NString) {
        if (i18NString == null) {
            return;
        }
        Class<? extends I18NString> aClass = i18NString.getClass();
        Field[] declaredFields = aClass.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            Object o = declaredField.get(i18NString);
            if (o instanceof String) {
                String content = ServiceUtil.getTextFromHtml((String) o);
                declaredField.set(i18NString, content);
            }
        }
    }

    /**
     * 如果参数需要设置错误信息
     *
     * @param messageSource
     * @param errorCode
     * @param params
     * @return
     */
    public static ErrorCode getTransformerError(MessageSource messageSource, ErrorCode errorCode, I18NString... params) {

        String message = null;
        try {
            Locale locale = LocaleContextHolder.getLocale();
            //如果是英文环境，按英文处理
            if (locale != null && locale == Locale.US && messageSource != null) {
                String messageKey = SurveyField.ERROR_MESSAGE_CODE_PREFIX + errorCode.getCode();
                if (params != null) {
                    List<String> paramStrings = Arrays.asList(params).stream().map(I18NString::getEn_US).collect(Collectors.toList());
                    message = messageSource.getMessage(messageKey, paramStrings.toArray(), locale);
                } else {
                    message = messageSource.getMessage(messageKey, null, locale);
                }

                if (StringUtils.isNotBlank(message)) {
                    return new ErrorCode(errorCode.getCode(), message);
                }

            }
        } catch (Exception e) {

        }

        //否则按中文处理
        if (params != null && params.length > 0) {
            List<String> paramStrings = Arrays.asList(params).stream().map(I18NString::getZh_CN).collect(Collectors.toList());
            message = String.format(errorCode.getMessage(), paramStrings.toArray());
        } else {
            message = errorCode.getMessage();
        }

        return new ErrorCode(errorCode.getCode(), message);
    }

    public static I18NString stringToI18(String val, String valEn) {
        I18NString i18 = new I18NString();
        i18.setZh_CN(val);
        i18.setEn_US(valEn);
        return i18;
    }

    public static String getZhCN(I18NString i18NString) {
        if (i18NString == null) {
            return "";
        }
        if (StringUtils.isBlank(i18NString.getZh_CN())) {
            return "";
        }
        return i18NString.getZh_CN();
    }

    public static void main(String[] args) {
        I18NString i18NString = new I18NString();
        i18NString.setEn_US("Overall I am very satisfied with {{The company:The company,thedepartment,theteam}}.");
        i18NString.setZh_CN("<p>对<span style=\\\"text-decoration: underline;\\\">自己</span>的能力有信心，相信自己的<em><strong>价值和重要性</strong></em></p>");
        delQuestionHtmlTags(i18NString);
        System.out.println(JSON.toJSONString(i18NString));
    }
}
