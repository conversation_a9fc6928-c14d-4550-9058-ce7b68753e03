DROP TABLE IF EXISTS survey_lottery_category;
CREATE TABLE survey_lottery_category
(
    "id"                              VARCHAR(36)  NOT NULL,
    "lottery_id"                      VARCHAR(36)  NOT NULL,
    "name"                            json NOT NULL,
    "is_partake_prize"                BOOL DEFAULT false,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___survey_lottery_category___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."survey_lottery_category" IS '调研抽奖奖品分类表';
COMMENT ON COLUMN "public"."survey_lottery_category"."lottery_id" IS '抽奖ID';
COMMENT ON COLUMN "public"."survey_lottery_category"."name" IS '分类名称';
COMMENT ON COLUMN "public"."survey_lottery_category"."is_partake_prize" IS '是否参与奖';


CREATE INDEX ___idx_survey_lottery_category_lottery_id___ ON public.survey_lottery_category(lottery_id);
