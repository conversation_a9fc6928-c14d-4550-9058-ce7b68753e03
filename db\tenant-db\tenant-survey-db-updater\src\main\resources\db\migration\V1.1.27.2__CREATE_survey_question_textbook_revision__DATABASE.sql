DROP TABLE IF EXISTS "public"."survey_question_revision";
CREATE TABLE survey_question_revision (
	id varchar(36) NOT NULL,
	type varchar(50) NOT NULL,
	questionnaire_id varchar(36) NOT NULL,
	question_id varchar(36) NOT NULL,
	option_id varchar(36) NULL,
	organization_id varchar(36) NULL,
	organization_scope varchar(50) NULL,
    demographic_contents json default '[]',
	name json,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__survey_question_revision___" PRIMARY KEY (id)
);
COMMENT ON TABLE  "public"."survey_question_revision" IS '问卷题目题本修订表';
COMMENT ON COLUMN "public"."survey_question_revision"."type" IS '类型（题目、选项）';
COMMENT ON COLUMN "public"."survey_question_revision"."questionnaire_id" IS '问卷ID';
COMMENT ON COLUMN "public"."survey_question_revision"."question_id" IS '题目ID';
COMMENT ON COLUMN "public"."survey_question_revision"."option_id" IS '选项ID';
COMMENT ON COLUMN "public"."survey_question_revision"."organization_id" IS '组织ID';
COMMENT ON COLUMN "public"."survey_question_revision"."organization_scope" IS '组织作用范围（仅当前、及以下）';
COMMENT ON COLUMN "public"."survey_question_revision"."demographic_contents" IS '人口标签信息';
COMMENT ON COLUMN "public"."survey_question_revision"."name" IS '调整后的名称';


CREATE INDEX ___idx_survey_question_revision_questionnaire_id___ ON public.survey_question_revision(questionnaire_id);
CREATE INDEX ___idx_survey_question_revision_question_id___ ON public.survey_question_revision(question_id);
