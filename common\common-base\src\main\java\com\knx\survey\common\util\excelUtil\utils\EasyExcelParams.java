package com.knx.survey.common.util.excelUtil.utils;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.handler.WriteHandler;
import com.knx.survey.common.base.field.SurveyField;
import com.knx.survey.common.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/6/11 14:24
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class EasyExcelParams {


    /**
     * excel文件名（不带拓展名)
     */
    private String excelNameWithoutExt;

    /**
     * sheet名称和对应的数据
     */
    private List<SheetData> sheetDatas;


    /**
     * 响应
     */
    private HttpServletResponse response;

    /**
     * Excel级别处理
     */
    private List<WriteHandler> handlers;

    /**
     * 流数据
     */
    private ByteArrayOutputStream out;

    private ExcelTypeEnum excelType;

    /**
     * 模板地址
     */
    private String templatePath;
    /**
     * 模板sheet名称
     */
    private String templateSheetName;

    /**
     * 是否去除空格
     */
    private Boolean isNotStringTrim;


    public EasyExcelParams() {
    }

    /**
     * 检查不允许为空的属性
     *
     * @return this
     */
    public EasyExcelParams checkValid() {
        Assert.isTrue(ObjectUtils.allNotNull(excelNameWithoutExt, sheetDatas, response), "导出excel参数不合法!");
        return this;
    }

    public void setSingleSheet(SheetData sheetData) {
        this.sheetDatas = Collections.singletonList(sheetData);
    }

    /**
     * 组合下载文件名称
     * @param projectId
     * @param projectName
     * @return
     */
    public void setExportFileName(String projectId,String projectName,String tenantName) {
        String[] nameParts = new String[10];
        nameParts[0] = tenantName;
        if (StringUtils.isNotBlank(projectId) && StringUtils.isNotBlank(projectName)) {
            nameParts[1] = projectId + SurveyField.COLON_ZH + projectName;
        }
        nameParts[2] = excelNameWithoutExt;
        //时间戳
        nameParts[3] = DateUtil.formatPureDatetimePatternNoSecond(new Date());
        this.excelNameWithoutExt = ZipUtil.replaceFileInvalidChar(fileNameArrayToString(nameParts));
    }
    private String fileNameArrayToString(String[] fileParts) {
        return ArrayUtil.join(Arrays.stream(fileParts).filter(StringUtils::isNotBlank).toArray(String[]::new), SurveyField.UNDER_LINE);
    }
}
