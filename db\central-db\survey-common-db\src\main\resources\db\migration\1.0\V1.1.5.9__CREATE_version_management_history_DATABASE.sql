DROP TABLE IF EXISTS version_management_history;
CREATE TABLE version_management_history
(
    id                 VARCHAR(36) NOT NULL,
    site               VARCHAR(20) NULL,
    version            VARCHAR(20) NULL,
    version_config     json NULL,
    description        text NULL,
    create_by          VARCHAR(36) NULL,
    create_time        TIMESTAMP(3) NULL DEFAULT now( ),
    update_time        TIMESTAMP(3) NULL DEFAULT now( ),
    last_update_by     VARCHAR(36) NULL,
    is_deleted         bool NULL DEFAULT FALSE,
    app_id             VARCHAR(36) NULL,
    CONSTRAINT "___pk___version_management_history___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."version_management_history"."site" IS '站点';
COMMENT ON COLUMN "public"."version_management_history"."version" IS '版本号';
COMMENT ON COLUMN "public"."version_management_history"."version_config" IS '版本配置';
COMMENT ON COLUMN "public"."version_management_history"."description" IS '迭代说明';
COMMENT ON TABLE "public"."version_management_history" IS '版本管理历史表';