package com.knx.bean.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.knx.bean.model.enums.KnxCardTypeEnum;
import com.knx.common.util.excelUtil.anno.EnumFormat;
import com.knx.common.util.excelUtil.converter.EnumExcelConverter;
import lombok.Data;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/10/21 18:28
 */

@Data
public class CardResultExcelVO {


    @ExcelProperty(value = "类型", converter = EnumExcelConverter.class)
    @EnumFormat(value = KnxCardTypeEnum.class, name = KnxCardTypeEnum.Fields.name)
    private KnxCardTypeEnum cardType;

    @ExcelProperty(value = "卡片金额")
    private Integer amount;

    @ExcelProperty(value = "卡片密码")
    private String cardPassword;

    @ExcelProperty(value = "到期日")
    private String expireDate;

    @ExcelProperty(value = "操作人")
    private String createName;

    @ExcelProperty(value = "充值租户")
    private String tenantName;

    @ExcelProperty(value = "是否使用")
    private String isUsed;

    @ExcelProperty(value = "是否过期")
    private String isExpired;

    @ExcelProperty(value = "创建时间")
    private String createTime;


}
