package com.knx.survey.common.util.excelUtil.handler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SheetMergeHandler.java
 * @Description TODO
 * @createTime 2022年07月08日 15:57:00
 */
public class SheetMergeHand<PERSON> implements SheetWriteHandler {

    private List<CellRangeAddress> cellRangeAddresses;

    public SheetMergeHandler(List<CellRangeAddress> cellRangeAddresses) {
        this.cellRangeAddresses = cellRangeAddresses;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        if (CollectionUtils.isNotEmpty(cellRangeAddresses)) {
            for (CellRangeAddress cellRangeAddress : cellRangeAddresses) {
                sheet.addMergedRegion(cellRangeAddress);
            }
        }
    }
}
