package com.knx.survey.common.util.excelUtil.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/6/11 14:07
 */

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface EnumFormat {

    /**
     * 要转换的枚举类型
     *
     * @return enum class
     */
    Class value();

    /**
     * 要转换枚举的属性
     *
     * @return
     */
    String name();

    String[] values() default {};

    boolean isShow() default true;
}
