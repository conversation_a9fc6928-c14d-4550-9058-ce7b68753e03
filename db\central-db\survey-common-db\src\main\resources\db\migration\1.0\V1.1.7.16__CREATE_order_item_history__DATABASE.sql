DROP TABLE IF EXISTS "public"."order_item_history";
CREATE TABLE "public"."order_item_history" (
    "id" varchar(36) COLLATE "pg_catalog"."default" NOT NULL,
    "order_id" varchar(36) COLLATE "pg_catalog"."default",
    "order_history_id" varchar(36) COLLATE "pg_catalog"."default",
    "type" varchar(50) COLLATE "pg_catalog"."default",
    "report_style" varchar(50) COLLATE "pg_catalog"."default",
    "employee_level" varchar(50) COLLATE "pg_catalog"."default",
    "num" numeric(11,0),
    "price" numeric(11,2),
    "total_amount" numeric(11,2),
    "create_by" varchar(36) COLLATE "pg_catalog"."default",
    "create_time" timestamp(3),
    "update_time" timestamp(3),
    "last_update_by" varchar(36) COLLATE "pg_catalog"."default",
    "is_deleted" bool,
    "app_id" varchar(36) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."order_item_history"."id" IS '主键';
COMMENT ON COLUMN "public"."order_item_history"."order_id" IS '订单id';
COMMENT ON COLUMN "public"."order_item_history"."order_history_id" IS '订单历史ID';
COMMENT ON COLUMN "public"."order_item_history"."type" IS '类型';
COMMENT ON COLUMN "public"."order_item_history"."report_style" IS '报告风格';
COMMENT ON COLUMN "public"."order_item_history"."employee_level" IS '员工层级';
COMMENT ON COLUMN "public"."order_item_history"."num" IS '数量';
COMMENT ON COLUMN "public"."order_item_history"."price" IS '价格';
COMMENT ON COLUMN "public"."order_item_history"."total_amount" IS '总金额';
COMMENT ON TABLE "public"."order_item_history" IS '订单明细修改历史表';

ALTER TABLE "public"."order_item_history" ADD CONSTRAINT "order_item_history_pkey" PRIMARY KEY ("id");
CREATE INDEX ___idx_order_item_history_order_history_id___ ON public.order_item_history(order_history_id);