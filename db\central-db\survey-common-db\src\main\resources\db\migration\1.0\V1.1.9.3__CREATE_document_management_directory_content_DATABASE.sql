CREATE TABLE document_management_directory_content (
	id varchar(36) NOT NULL,
    directory_name json NULL,
    directory_level numeric(11) default 1,
    parent_id varchar(50) NULL,
    content json NULL,
    sort numeric(11),
    document_management_id varchar(36) NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__document_management_directory_content___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."document_management_directory_content"."directory_name" IS '目录名称';
COMMENT ON COLUMN "public"."document_management_directory_content"."directory_level" IS '目录级别';
COMMENT ON COLUMN "public"."document_management_directory_content"."parent_id" IS '父级id';
COMMENT ON COLUMN "public"."document_management_directory_content"."content" IS '内容';
COMMENT ON COLUMN "public"."document_management_directory_content"."sort" IS '排序';
COMMENT ON COLUMN "public"."document_management_directory_content"."document_management_id" IS '文档Id';
COMMENT ON TABLE  "public"."document_management_directory_content" IS '文档管理目录及内容';
