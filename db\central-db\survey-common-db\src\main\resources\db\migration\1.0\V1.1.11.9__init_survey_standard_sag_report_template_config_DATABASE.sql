
INSERT INTO "survey_standard_sag_report_template" ("id","name","code","type","standard_report_id","status","tenant_id","create_by","create_time","update_time","last_update_by","is_deleted","standard_report_type","sample_url","app_id","report_style","config_type")
    VALUES ('1279354877049363562', '{"zh_CN":"EPA报告（TIP细分专用）","en_US":""}', '043', 'STANDARD', null, 'OFF', null, null, null , null , null , 'f', 'EPA', null, null,null,null);


INSERT INTO "survey_standard_sag_report_template_config" ( "id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id" )
VALUES
( '1280332187905756153', '1279354877049363562', 'PAYMENT', '{"paymentConfig":{"summary":0,"appendix":0,"backCover":0,"detailedScore":0,"developmentSuggestions":0,"frontCover":0,"interviewSuggestions":0,"overview":0,"preface":0,"responseStyle":0,"total":0}}', null, null, null, null, 'f', NULL );


alter TABLE public.survey_standard_sag_report_template add column "is_tip" bool default true;
COMMENT ON COLUMN public.survey_standard_sag_report_template.is_tip IS '是否是tip细分报告';

update survey_standard_sag_report_template set "is_tip" = 'f' where "type" = 'CUSTOMIZE';

update survey_standard_sag_report_template set "is_tip" = 'f' WHERE "id" in ('1280019953325436929','1279354877049363547','1279354877045623547');



