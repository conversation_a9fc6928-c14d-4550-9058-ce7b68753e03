-- name字段转换成json类型
ALTER TABLE public.survey_standard_sag_report_template_job ADD name_bak varchar(50) NULL;
update survey_standard_sag_report_template_job set name_bak = name where name  is not null;
update survey_standard_sag_report_template_job set name = null where name  is not null;
ALTER TABLE public.survey_standard_sag_report_template_job ALTER COLUMN name TYPE json USING name::json;

update survey_standard_sag_report_template_job set name = cast(concat('{"en_US":"","zh_CN":"', name_bak, '"}') AS json) where name_bak is not null;
ALTER TABLE public.survey_standard_sag_report_template_job DROP COLUMN name_bak;