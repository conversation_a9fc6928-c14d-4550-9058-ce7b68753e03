package com.knx.survey.innerapi.client.report.vo;

import com.knx.survey.innerapi.client.report.enums.Condition;
import com.knx.survey.innerapi.client.report.enums.DescriptionRuleBackType;
import com.knx.survey.innerapi.client.report.enums.FrontOptionType;
import lombok.Data;

/**
 * 总分计算规则
 */
@Data
public class TotalScoreRule {
    /**
     * 结果前置选项类型
     */
    private FrontOptionType resultFrontOptionType;
    /**
     * 结果条件
     */
    private Condition resultCondition;
    /**
     * 结果后置条件类型
     */
    private DescriptionRuleBackType resultBackType;
    /**
     * 结果后置值
     */
    private String resultBackValue;
}
