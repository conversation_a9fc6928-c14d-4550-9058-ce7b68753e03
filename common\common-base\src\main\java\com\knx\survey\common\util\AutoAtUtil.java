package com.knx.survey.common.util;

import org.apache.commons.math3.distribution.NormalDistribution;

/**
 * <AUTHOR>
 * 自适应算法实现
 */

public class AutoAtUtil {

    final static double[] LISTX_ = {-4, -3.9, -3.8, -3.7, -3.6, -3.5, -3.4, -3.3, -3.2, -3.1,
            -3, -2.9, -2.8, -2.7, -2.6, -2.5, -2.4, -2.3, -2.2, -2.1,
            -2, -1.9, -1.8, -1.7, -1.6, -1.5, -1.4, -1.3, -1.2, -1.1,
            -1, -0.9, -0.8, -0.7, -0.6, -0.5, -0.4, -0.3, -0.2, -0.1,
            0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9,
            2.0, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9, 3.0, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9, 4};

    /**
     * AT自适应获取Theta
     *
     * @param listF 答题正确/错误情况（true为正确，false为错误）
     * @param listA 题目区分度，(0,2)
     * @param listB 题目难度，(-3,3)
     * @param listC 题目猜测系数，(0,0.5)
     * @return [-4,4]
     */
    public static double getTheta(boolean[] listF, double[] listA, double[] listB, double[] listC) {
        double s1 = 0;
        double s2 = 0;

        for (double x : LISTX_) {
            double lx = 1;
            for (int i = 0; i < listF.length; i++) {
                lx = lx * px(listF[i], x, listA[i], listB[i], listC[i]);
            }

            s1 += x * lx * ax(x, 0, 1);
            s2 += lx * ax(x, 0, 1);
        }

        if (Double.compare(s2, 0) == 0) {
            return 0;
        }

        return s1 / s2;
    }

    /**
     * 概率密度函数
     *
     * @param x    积分点
     * @param mean 正态分布平均值
     * @param std  正态分布标准差
     * @return
     */
    private static double ax(double x, double mean, double std) {
        return 1 / (std * Math.pow(2 * Math.PI, 0.5)) * (Math.exp(-(Math.pow((x - mean), 2)) / (2 * Math.pow(std, 2))));
    }

    /**
     * 似然函数
     *
     * @param f 该题是否答对，答对true，答错false
     * @param x 积分点
     * @param a 题目鉴别力
     * @param b 题目难度
     * @param c 题目猜测系数
     * @return
     */
    private static double px(boolean f, double x, double a, double b, double c) {
        double rt = c + (1 - c) / (1 + Math.exp(-1.7 * a * (x - b)));

        if (f) {
            //答对似然值
            return rt;
        } else {
            //答错似然值
            return 1 - rt;
        }
    }

    /**
     * 获取终止信息量
     *
     * @param theta 能力值
     * @param a     区分度
     * @param b     难度
     * @param c     猜测系数
     * @return
     */
    public static double getStopInfo(double theta, double a, double b, double c) {
        return (2.89 * Math.pow(a, 2) * (1 - c)) / ((c + Math.exp(1.7 * a * (theta - b))) * Math.pow((1 + Math.exp(-1.7 * a * (theta - b))), 2));
    }

    /**
     * 计算题目的最大能力值
     *
     * @param a 区分度
     * @param b 难度
     * @param c 猜测系数
     * @return
     */
    public static double getThetaMax(double a, double b, double c) {
        return b + Math.log(0.5 * (1 + Math.pow(1 + 8 * c, 0.5))) / (1.7 * a);
    }

    /**
     * 获取能力值累积概率(所占百分位)
     *
     * @param theta 能力值
     * @return 百分位值
     */
    public static Double getPercentile(Double theta) {

        if (theta == null) {
            return null;
        }
        //均值
        double mean = 0;
        //标准差
        double sd = 1;
        NormalDistribution standardNormalDistribution = new NormalDistribution(mean, sd);
        return standardNormalDistribution.cumulativeProbability(theta) * 100;
    }
}
