#!/bin/sh
# k8s环境下，pod停止时 pre-stop hook执行的脚本
echo "pre-stop hook execute begin!"
curl --connect-time 1 --max-time 2 -X POST 'http://localhost:8002/actuator/service-registry' -H 'Content-Type: application/json' -d '{"status": "DOWN"}'
echo "deregister from nacos done, wait for all consumers refresh service instance information..."
# spring-cloud 服务注册发现机制是服务消费方每隔30秒回从注册中心拉取一遍服务实例信息，刷新到消费方服务内存中。所以这里sleep35秒，等待所有消费方都刷新了最新的服务实例信息
sleep 35
echo "pre-stop hook execute done!"