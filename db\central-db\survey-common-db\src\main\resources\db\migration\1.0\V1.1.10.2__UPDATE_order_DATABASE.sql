alter TABLE public.order add column "counselor" varchar (255) default null;
alter TABLE public.order_history add column "counselor" varchar (255) default null;
COMMENT ON COLUMN public.order.counselor IS '顾问人员';
COMMENT ON COLUMN public.order_history.counselor IS '顾问人员';

alter TABLE public.order add column "base_remark" varchar (255) default null;
alter TABLE public.order_history add column "base_remark" varchar (255) default null;
COMMENT ON COLUMN public.order.base_remark IS '基本信息_备注';
COMMENT ON COLUMN public.order_history.base_remark IS '基本信息_备注';

alter TABLE public.order add column "sale_remark" varchar (255) default null;
alter TABLE public.order_history add column "sale_remark" varchar (255) default null;
COMMENT ON COLUMN public.order.sale_remark IS '售卖信息_备注';
COMMENT ON COLUMN public.order_history.sale_remark IS '售卖信息_备注';

alter TABLE public.order add column "is_fill_working_hours" bool default false ;
alter TABLE public.order_history add column "is_fill_working_hours" bool default false;
COMMENT ON COLUMN public.order.is_fill_working_hours IS '是否填写工时';
COMMENT ON COLUMN public.order_history.is_fill_working_hours IS '是否填写工时';

alter TABLE public.order add column "contract_remark" varchar (255) default null;
alter TABLE public.order_history add column "contract_remark" varchar (255) default null;
COMMENT ON COLUMN public.order.contract_remark IS '合同_备注';
COMMENT ON COLUMN public.order_history.contract_remark IS '合同_备注';

UPDATE "public"."order_item" set employee_level =  CONCAT('["',employee_level,'"]');
UPDATE "public"."order_item_history" set employee_level =  CONCAT('["',employee_level,'"]');

alter table "public"."order_item"  alter column employee_level type json USING employee_level::json;
alter table "public"."order_item_history"  alter column employee_level type json USING employee_level::json;