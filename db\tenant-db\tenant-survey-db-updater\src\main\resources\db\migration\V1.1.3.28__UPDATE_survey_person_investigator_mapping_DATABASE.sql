ALTER TABLE survey_person_investigator_mapping DROP CONSTRAINT ___pk___survey_person_investigator_mapping___;
ALTER TABLE survey_person_investigator_mapping ADD COLUMN id SERIAL PRIMARY KEY;
ALTER TABLE survey_person_investigator_mapping ALTER COLUMN id TYPE varchar(36) USING id::varchar;
ALTER TABLE survey_person_investigator_mapping ALTER COLUMN id DROP DEFAULT;
CREATE INDEX ___idx_survey_person_investigator_mapping_person_id_investigator_id___ ON survey_person_investigator_mapping(person_id, investigator_id);

ALTER TABLE survey_person_investigator_mapping ADD "create_by" varchar(36) NULL;
ALTER TABLE survey_person_investigator_mapping ADD "create_time" TIMESTAMP(3) DEFAULT NOW();
ALTER TABLE survey_person_investigator_mapping ADD "update_time" TIMESTAMP(3) DEFAULT NOW();
ALTER TABLE survey_person_investigator_mapping ADD "last_update_by" varchar(36) NULL;
ALTER TABLE survey_person_investigator_mapping ADD is_deleted bool NULL DEFAULT false;
ALTER TABLE survey_person_investigator_mapping ADD "app_id" varchar(36) NULL;