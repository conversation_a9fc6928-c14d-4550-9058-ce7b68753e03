package com.knx.bean.web.controller;

import com.knx.bean.model.entity.KnxBeanAccount;
import com.knx.bean.service.api.IKnxBeanAccountService;
import com.knx.common.base.web.response.IDValue;
import com.knx.common.base.web.response.WebResponse;
import com.knx.common.security.CommonRole;
import io.swagger.annotations.*;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;


/**
 * 租户肯豆账户
 *
 * <AUTHOR>
 * @version 1.0.0
 * @email <EMAIL>
 * @date 2020-05-18 15:53:19
 */
@RestController
@RequestMapping("/knx/bean/account")
@Api(tags = "租户肯豆账户")
public class KnxBeanAccountController {
    @Autowired
    private IKnxBeanAccountService knxBeanAccountService;

    @ApiOperation(value = "按租户id查看肯豆余额", notes = "按租户id查看肯豆余额")
    @GetMapping("/get/{tenantId}")
    public WebResponse<KnxBeanAccount> get(@ApiParam(value = "租户id", name = "tenantId", required = true) @PathVariable String tenantId) {
        KnxBeanAccount knxBeanAccount = knxBeanAccountService.get(tenantId);
        return WebResponse.returnGetResult(knxBeanAccount);
    }

    @ApiOperation(value = "创建租户肯豆账户", notes = "创建租户肯豆账户")
    @PostMapping("/create")
    //@RequiresRoles(CommonRole.ROLE_KNX_BEAN_ADMIN)
    public WebResponse<IDValue> create(@RequestBody KnxBeanAccount knxBeanAccount) {
        BigDecimal balance = new BigDecimal(0);
        knxBeanAccount.setBalance(balance);
        knxBeanAccountService.save(knxBeanAccount);
        return WebResponse.returnCreateResult(knxBeanAccount);
    }


    @ApiOperation(value = "修改租户肯豆账户", notes = "修改租户肯豆账户")
    @PostMapping("/update")
    @RequiresRoles(CommonRole.ROLE_KNX_BEAN_ADMIN)
    public WebResponse<IDValue> update(@RequestBody KnxBeanAccount knxBeanAccount) {
        knxBeanAccountService.updateById(knxBeanAccount);
        return WebResponse.returnCreateResult(knxBeanAccount);
    }


    @ApiOperation(value = "新用户默认赠送肯豆", notes = "新用户默认赠送肯豆")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "租户id", name = "tenantId", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(value = "赠送肯豆值", name = "balance", dataType = "Integer", paramType = "query", required = true)

    })
    @PostMapping("/defaultGiveKen")
    @RequiresRoles(CommonRole.ROLE_KNX_BEAN_ADMIN)
    public WebResponse defaultGiveKen(@RequestParam String tenantId, @RequestParam Integer balance) {
        knxBeanAccountService.defaultGiveKen(tenantId, balance);
        return WebResponse.returnSuccess();
    }


    @ApiOperation(value = "通过租户id批量获取肯豆值", notes = "通过租户id批量获取肯豆值")
    @PostMapping("/batchListByTenantId")
    @RequiresRoles(CommonRole.ROLE_KNX_BEAN_ADMIN)
    public WebResponse<List<KnxBeanAccount>> batchListByTenantId(@ApiParam(value = "需要查询的租户id集合", name = "tenantIds", allowMultiple = true, required = true) @RequestBody List<String> tenantIds
    ) {
        List<KnxBeanAccount> knxBeanAccountList = knxBeanAccountService.batchListByTenantId(tenantIds);
        return WebResponse.returnSuccessData(knxBeanAccountList);
    }

    @ApiOperation(value = "按租户id查看肯豆余额", notes = "按租户id查看肯豆余额")
    @GetMapping("/getBalance/{tenantId}")
    public WebResponse<BigDecimal> getBalance(@ApiParam(value = "租户id", name = "tenantId", required = true) @PathVariable String tenantId) {
        return WebResponse.returnSuccessData(knxBeanAccountService.getBalance(tenantId));
    }

    @ApiOperation(value = "按租户id查看短信肯豆余额", notes = "按租户id查看短信肯豆余额")
    @GetMapping("/getMessageBalance/{tenantId}")
    public WebResponse<BigDecimal> getMessageBalance(@ApiParam(value = "租户id", name = "tenantId", required = true) @PathVariable String tenantId) {
        return WebResponse.returnSuccessData(knxBeanAccountService.getMessageBalance(tenantId));
    }

}
