DROP TABLE IF EXISTS "public"."sag_report_general_comment";
CREATE TABLE "public"."sag_report_general_comment" (
                                                       "id" VARCHAR ( 36 ) NOT NULL,
                                                       "project_id" VARCHAR ( 36 ) DEFAULT NULL,
                                                       "questionnaire_id" VARCHAR ( 36 ) DEFAULT NULL,
                                                       "person_id" VARCHAR ( 36 ) DEFAULT NULL,
                                                       "investigator_id" VARCHAR ( 36 ) DEFAULT NULL,
                                                       "prisma_report_data_id" VARCHAR ( 36 ) DEFAULT NULL,
                                                       "general_comment" VARCHAR ( 200 ) DEFAULT NULL,
                                                       "general_comment_score" NUMERIC ( 10, 2 ) DEFAULT NULL,
                                                       "create_by" VARCHAR ( 36 ),
                                                       "create_time" TIMESTAMP ( 3 ) DEFAULT NOW( ),
                                                       "update_time" TIMESTAMP ( 3 ) DEFAULT NOW( ),
                                                       "last_update_by" VARCHAR ( 36 ),
                                                       "is_deleted" BOOL DEFAULT FALSE,
                                                       "app_id" VARCHAR ( 36 ),
                                                       CONSTRAINT "___pk___sag_report_general_comment___" PRIMARY KEY ( "id" )
);
COMMENT ON COLUMN "public"."sag_report_general_comment"."id" IS '主键';
COMMENT ON COLUMN "public"."sag_report_general_comment"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."sag_report_general_comment"."questionnaire_id" IS '问券id';
COMMENT ON COLUMN "public"."sag_report_general_comment"."person_id" IS '人员id';
COMMENT ON COLUMN "public"."sag_report_general_comment"."investigator_id" IS '调查者id';
COMMENT ON COLUMN "public"."sag_report_general_comment"."prisma_report_data_id" IS '调研数据id';
COMMENT ON COLUMN "public"."sag_report_general_comment"."general_comment" IS '总评文字';
COMMENT ON COLUMN "public"."sag_report_general_comment"."general_comment_score" IS '总评数字';
COMMENT ON TABLE "public"."sag_report_general_comment" IS '报告总评';
CREATE INDEX ___idx_sag_report_general_comment_person_id___ ON PUBLIC.sag_report_general_comment ( questionnaire_id, person_id );
CREATE INDEX ___idx_sag_report_general_comment_investigator_id___ ON PUBLIC.sag_report_general_comment ( questionnaire_id, investigator_id );
CREATE INDEX ___idx_sag_report_general_comment_prisma_report_data_id___ ON PUBLIC.sag_report_general_comment ( questionnaire_id, prisma_report_data_id );
