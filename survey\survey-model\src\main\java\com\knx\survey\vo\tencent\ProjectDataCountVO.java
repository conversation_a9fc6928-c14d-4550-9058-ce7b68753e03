package com.knx.survey.vo.tencent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目数据统计结果VO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-04
 */
@Data
public class ProjectDataCountVO {
    
    @ApiModelProperty("项目ID")
    private String id;
    
    @ApiModelProperty("项目名称")
    private String name;
    
    @ApiModelProperty("项目编码")
    private Integer code;
    
    @ApiModelProperty("data数量统计")
    private Integer dataCount;
    
    public ProjectDataCountVO() {
    }
    
    public ProjectDataCountVO(String id, String name, Integer code, Integer dataCount) {
        this.id = id;
        this.name = name;
        this.code = code;
        this.dataCount = dataCount;
    }
}
