package com.knx.survey.service.impl.domain.report.tencent;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.HashMultimap;
import com.knx.common.base.service.exception.BusinessException;
import com.knx.common.dynamic.annotation.MultiTenant;
import com.knx.survey.common.base.field.SurveyField;
import com.knx.survey.dao.mapper.report.tencent.PrismaOrgaztionOpenQuestionDataMapper;
import com.knx.survey.error.SurveyErrorCode;
import com.knx.survey.model.report.prisma.tencent.PrismaOrgaztionOpenQuestionData;
import com.knx.survey.model.tenant.project.SurveyProject;
import com.knx.survey.service.api.domain.IProjectService;
import com.knx.survey.service.api.domain.report.tencent.IPrismaOrgaztionOpenQuestionDataService;
import com.knx.survey.vo.tencent.ImportOpenQuestionResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.knx.survey.vo.tencent.OpenQuestionDataWarehouseResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【prisma_orgaztion_open_question_data(活动所有填答人开放题数据)】的数据库操作Service实现
* @createDate 2025-07-10 15:53:50
*/
@Slf4j
@MultiTenant
@Service
public class PrismaOrgaztionOpenQuestionDataServiceImpl extends ServiceImpl<PrismaOrgaztionOpenQuestionDataMapper, PrismaOrgaztionOpenQuestionData>
    implements IPrismaOrgaztionOpenQuestionDataService {

    @Autowired
    private IProjectService projectService;

    /**
     * 保存开放题数据从VO
     * @param projectId
     * @param orgDatasMap
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOpenQuestionDataFromVO(String projectId, HashMultimap<String, ImportOpenQuestionResultVo> orgDatasMap) {
        // 先删除原有数据
        this.deleteByProjectId(projectId);

        List<PrismaOrgaztionOpenQuestionData> dataList = new ArrayList<>();
        for (ImportOpenQuestionResultVo importData : orgDatasMap.values()) {
            PrismaOrgaztionOpenQuestionData data = new PrismaOrgaztionOpenQuestionData();
            data.setProjectId(projectId);
            data.setReleaseYear(importData.getReleaseYear());
            data.setOrganization(importData.getOrganizationCode());
            data.setCustomized(importData.getCustomized());
            data.setDimension(importData.getDimensionCode());
            data.setQuestion(importData.getQuestionCode());
            data.setOption(importData.getOptionId());
            data.setContent(importData.getContent());
            data.setParentQuestion(importData.getParentQuestionCode());
            data.setParentOption(importData.getParentOptionId());
            data.setType(importData.getIsPierceThrough() ? "PIERCE_THROUGH" : "OPEN");
            data.setCreateTime(new Date());
            data.setIsDeleted(false);
            dataList.add(data);
        }

        // 使用服务批量保存数据
        this.saveBatch(dataList);
    }

    /**
     * 按projectId物理删除所有开放题数据
     * @param projectId
     */
    @Override
    public void deleteByProjectId(String projectId) {
        // 使用Mapper自定义SQL删除
        this.baseMapper.deleteByProjectId(projectId);
    }

    /**
     * 根据projectId查询开放题数据
     * @param projectId
     * @return List of open question data
     */
    @Override
    public List<PrismaOrgaztionOpenQuestionData> getByProjectId(String projectId) {
        List<String> ids = this.baseMapper.listIdsByProjectId(projectId);
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<PrismaOrgaztionOpenQuestionData> prismaQuestionApprovals = new ArrayList<>(ids.size());
        List<List<String>> batchs = ListUtil.split(ids, SurveyField.QUERY_BATCH_SIZE_500);
        for (List<String> batch : batchs) {
            prismaQuestionApprovals.addAll(this.lambdaQuery()
                    .in(PrismaOrgaztionOpenQuestionData::getId, batch)
                    .select(
                            PrismaOrgaztionOpenQuestionData::getOrganization,
                            PrismaOrgaztionOpenQuestionData::getCustomized,
                            PrismaOrgaztionOpenQuestionData::getDimension,
                            PrismaOrgaztionOpenQuestionData::getQuestion,
                            PrismaOrgaztionOpenQuestionData::getOption,
                            PrismaOrgaztionOpenQuestionData::getContent,
                            PrismaOrgaztionOpenQuestionData::getParentQuestion,
                            PrismaOrgaztionOpenQuestionData::getParentOption,
                            PrismaOrgaztionOpenQuestionData::getType
                    )
                    .list());
        }
        return prismaQuestionApprovals;
    }

    /**
     * 根据projectId查询开放题数据并构建响应
     * @param projectCode
     * @return OpenQuestionDataWarehouseResponse
     */
    @Override
    public OpenQuestionDataWarehouseResponse getOpenQuestionResponse(String projectCode) {
        if(!NumberUtil.isNumber(projectCode)){
            throw new BusinessException(SurveyErrorCode.PROJECT_IS_NOT_EXIST);
        }

        SurveyProject project = projectService.getByCode(Integer.valueOf(projectCode));
        if (project == null) {
            throw new BusinessException(SurveyErrorCode.PROJECT_IS_NOT_EXIST);
        }

        List<PrismaOrgaztionOpenQuestionData> openQuestionList = this.getByProjectId(project.getId());

        OpenQuestionDataWarehouseResponse response = new OpenQuestionDataWarehouseResponse();
        response.setProjectId(projectCode);
        response.setProjectName(project.getName());
        response.setReleaseYear(String.valueOf(DateUtil.year(project.getStartTime())));
        response.setOpenquestions(openQuestionList);
        return response;
    }
}
