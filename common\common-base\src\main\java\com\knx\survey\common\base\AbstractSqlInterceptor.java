package com.knx.survey.common.base;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Invocation;

import java.lang.reflect.Field;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2021/10/12 16:02
 */

@Slf4j
public abstract class AbstractSqlInterceptor implements SqlInterceptor, Interceptor {


    @Override
    public Object intercept(Invocation invocation) throws Throwable {

        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object arg = invocation.getArgs()[1];
        BoundSql boundSql = mappedStatement.getBoundSql(arg);

        //获取到原始sql语句
        String oldSql = boundSql.getSql();
        oldSql = StringUtils.replace(oldSql, StringUtils.LF, StringUtils.SPACE);
        if (!isInterceptor(mappedStatement.getSqlCommandType().toString(), oldSql)) {
            return invocation.proceed();
        }

        String newSql = newSql(oldSql);
        log.debug("newSql:{}", newSql);
        //通过反射修改sql语句
        Field field = boundSql.getClass().getDeclaredField("sql");
        field.setAccessible(true);
        field.set(boundSql, newSql);
        MappedStatement newMs = copyFromMappedStatement(mappedStatement, new AbstractSqlInterceptor.BoundSqlSqlSource(boundSql));
        invocation.getArgs()[0] = newMs;
        return invocation.proceed();
    }

    // 复制原始MappedStatement
    private MappedStatement copyFromMappedStatement(MappedStatement ms, SqlSource newSqlSource) {
        MappedStatement.Builder builder = new MappedStatement.Builder(ms.getConfiguration(), ms.getId(), newSqlSource,
                ms.getSqlCommandType());
        builder.resource(ms.getResource());
        builder.fetchSize(ms.getFetchSize());
        builder.statementType(ms.getStatementType());
        builder.keyGenerator(ms.getKeyGenerator());
        if (ms.getKeyProperties() != null) {
            for (String keyProperty : ms.getKeyProperties()) {
                builder.keyProperty(keyProperty);
            }
        }
        builder.timeout(ms.getTimeout());
        builder.parameterMap(ms.getParameterMap());
        builder.resultMaps(ms.getResultMaps());
        builder.cache(ms.getCache());
        builder.useCache(ms.isUseCache());
        return builder.build();
    }

    private static class BoundSqlSqlSource implements SqlSource {
        BoundSql boundSql;

        public BoundSqlSqlSource(BoundSql boundSql) {
            this.boundSql = boundSql;
        }

        @Override
        public BoundSql getBoundSql(Object parameterObject) {
            return boundSql;
        }
    }
}


