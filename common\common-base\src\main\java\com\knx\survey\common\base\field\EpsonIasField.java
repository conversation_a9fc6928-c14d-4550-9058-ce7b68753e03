package com.knx.survey.common.base.field;

import com.knx.survey.model.I18NString;
import com.knx.survey.model.SagI18NString;

public class EpsonIasField {

    public static I18NString DIMENSION = new SagI18NString("维度", "Dimension");

    public static I18NString QUESTION = new SagI18NString("", "Item");

    public static I18NString QUESTION_NAME = new SagI18NString("题目", "Item");

    public static I18NString DEPARTMENT_NAME = new SagI18NString("部门", "Department");

    public static I18NString VALID_ANSWER_NUMBER_PERCENTAGE_NAME = new SagI18NString("有效人数及占比", "Effective Responses & Percentage");

    public static I18NString ANALYSIS_OBJECT = new SagI18NString("分析主体", "Analysis Object");

    public static I18NString CORRELATION_NAME = new SagI18NString("相关系数", "Correlation");

    public static I18NString CORRELATION_TOP_10_NAME = new SagI18NString("相关前10", "Correlation Top 10");

    public static I18NString IS_LOWEST_ITEM = new SagI18NString("是否最低分", "Among Lowest 10 Scores");

    public static I18NString SCORE_BOTTOM_10_NAME = new SagI18NString("得分后10", "Score Bottom 10");

    public static I18NString SCORE_TOP_10_NAME = new SagI18NString("得分前10", "Score Top 10");

    public static I18NString LAST_10_NAME = new SagI18NString("后10", " Bottom 10");

    public static I18NString TOP_10_NAME = new SagI18NString("前10", " Top 10");

    public static I18NString FOCUS_NAME = new SagI18NString("关注度", "Focus");

    public static I18NString DIMENSION_QUESTION = new SagI18NString("维度/题目", "Dimension/Item");

    public static I18NString NORM_DIVIDED_AREA_NAME = new SagI18NString("常模切分区域", "Norm-divided Area");

    public static I18NString ORGANIZATION_NAME = new SagI18NString("组织", "Organization");

    public static I18NString ANALYSIS_OBJECT_NAME = new SagI18NString("分析主体", "Analysis Object");

    public static I18NString NORM_NAME = new SagI18NString("常模", "Norm");

    public static I18NString AREA_NAME = new SagI18NString("所属区域", "Area");

    public static I18NString NO_NAME = new SagI18NString("序号", "No");

    public static I18NString NAME_NAME = new SagI18NString("名称", "Name");

    public static I18NString APPROVAL_ABBR_NAME = new SagI18NString("赞成百分比", "Agreement");

    public static I18NString NEUTRAL_ABBR_NAME = new SagI18NString("中立百分比", "Neutral");

    public static I18NString DISAPPROVAL_ABBR_NAME = new SagI18NString("不赞成百分比", "Disagreement");

    public static I18NString VALID_NUMBER_NAME = new SagI18NString("有效填答人数", "Valid Number");

    public static I18NString EMPLOYEE_GROUP_NAME = new SagI18NString("人群分类", "Employee group");

    public static I18NString INDEX_NAME = new SagI18NString("指数", "Index");

    public static I18NString EEI_NAME = new SagI18NString("敬业度指数", "EEI");

    public static I18NString EEI_ABBR_NAME = new SagI18NString("敬业度", "Engagement");

    public static I18NString ESI_NAME = new SagI18NString("满意度指数", "ESI");

    public static I18NString ESI_ABBR_NAME = new SagI18NString("满意度", "Satisfaction");

    public static I18NString OCI_NAME = new SagI18NString("组织能力指数", "OCI");

    public static I18NString EMPTY_NAME = new SagI18NString("", "");

    public static I18NString COLON_NAME = new SagI18NString("：", ": ");

    public static I18NString COMMA_NAME = new SagI18NString("，", ", ");

    public static I18NString HIGHEST_NAME = new SagI18NString("最高", "");

    public static I18NString LOWEST_NAME = new SagI18NString("最低", "");

    public static I18NString SUPERIOR_DEPARTMENT_NAME = new SagI18NString("上一级", "Superior Department");
}
