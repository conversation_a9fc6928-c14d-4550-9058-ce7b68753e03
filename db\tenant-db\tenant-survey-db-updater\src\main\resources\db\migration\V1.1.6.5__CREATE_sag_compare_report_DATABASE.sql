DROP TABLE IF EXISTS sag_compare_report;
CREATE TABLE sag_compare_report
(
    "id"                              VARCHAR(36)  NOT NULL,
    "project_id"                      VARCHAR(36),
    "questionnaire_id"                VARCHAR(36),
    "person_organization_ids"         json,
    "survey_type"                     VARCHAR(50),
    "status"                          VARCHAR(50) DEFAULT 'INIT',
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___sag_compare_report___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."sag_compare_report" IS '对比报告表';
COMMENT ON COLUMN "public"."sag_compare_report"."project_id" IS '活动ID';
COMMENT ON COLUMN "public"."sag_compare_report"."questionnaire_id" IS '问卷ID';
COMMENT ON COLUMN "public"."sag_compare_report"."person_organization_ids" IS '人员或组织ID列表';
COMMENT ON COLUMN "public"."sag_compare_report"."survey_type" IS '问卷类型';
COMMENT ON COLUMN "public"."sag_compare_report"."status" IS '状态';

DROP TABLE IF EXISTS sag_compare_report_dimension_score;
CREATE TABLE sag_compare_report_dimension_score
(
    "id"                              VARCHAR(36)  NOT NULL,
    "parent_id"                       VARCHAR(36),
    "compare_report_id"               VARCHAR(36),
    "person_organization_id"          VARCHAR(36),
    "dimension_name"                  json,
    "dimension_code"                  VARCHAR(50),
    "score"                           numeric(53,2),
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___sag_compare_report_dimension_score___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."sag_compare_report_dimension_score" IS '对比报告维度得分表';
COMMENT ON COLUMN "public"."sag_compare_report_dimension_score"."parent_id" IS '父ID';
COMMENT ON COLUMN "public"."sag_compare_report_dimension_score"."compare_report_id" IS '对比报告ID';
COMMENT ON COLUMN "public"."sag_compare_report_dimension_score"."person_organization_id" IS '人员或组织ID';
COMMENT ON COLUMN "public"."sag_compare_report_dimension_score"."dimension_name" IS '维度名称';
COMMENT ON COLUMN "public"."sag_compare_report_dimension_score"."dimension_code" IS '维度编码';
COMMENT ON COLUMN "public"."sag_compare_report_dimension_score"."score" IS '得分或赞成度';

