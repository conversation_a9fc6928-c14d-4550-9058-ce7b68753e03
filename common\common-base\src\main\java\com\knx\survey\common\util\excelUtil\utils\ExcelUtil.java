package com.knx.survey.common.util.excelUtil.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.knx.common.base.service.exception.BusinessException;
import com.knx.common.base.service.exception.ErrorCode;
import com.knx.survey.common.util.excelUtil.converter.EnumExcelConverter;
import com.knx.survey.common.util.excelUtil.handler.NoClazzSpinnerWriteHandle;
import com.knx.survey.common.util.excelUtil.handler.SpinnerWriteHandler;
import com.knx.survey.common.util.excelUtil.listener.BaseExcelListener;
import com.knx.survey.common.util.excelUtil.listener.MapBaseExcelListener;
import com.knx.survey.common.util.excelUtil.listener.MultiSheetMapBaseExcelListener;
import com.knx.survey.common.util.excelUtil.listener.ObjectExcelListener;
import com.knx.survey.common.util.excelUtil.pojo.ErrRows;
import com.knx.survey.common.util.excelUtil.pojo.ReadMsg;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/6/11 14:25
 */

@Slf4j
public class ExcelUtil {
    /*---------------------------------------------- 导出excel相关 ----------------------------------------------*/

    /**
     * 下载EXCEL文件2007版本
     *
     * @param excelParams 使用EasyExcel导出文件需要设置的相关参数对象,下同
     */
    public static void exportExcel2007(EasyExcelParams excelParams) throws IOException {
        exportExcel(excelParams, ExcelTypeEnum.XLSX);
    }

    public static EasyExcelParams dynamicGetExcel2007OutputStream(EasyExcelParams excelParams) throws IOException {
        Long current = System.currentTimeMillis();
        log.info("excel write to outputStream start");
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        excelParams.setOut(out);
        excelParams.setExcelType(ExcelTypeEnum.XLSX);
        dynamicResolveExcel2007(excelParams, out);
        log.info("excel write to outputStream end,cost:{}", System.currentTimeMillis() - current);
        return excelParams;
    }

    public static void dynamicExportExcel2007(EasyExcelParams excelParams) throws IOException {
        HttpServletResponse response = excelParams.getResponse();
        OutputStream out = response.getOutputStream();
        prepareResponds(response, excelParams.getExcelNameWithoutExt(), ExcelTypeEnum.XLSX);
        dynamicResolveExcel2007(excelParams, out);
    }

    public static EasyExcelParams dynamicExportExcel2007ToOutputStream(EasyExcelParams excelParams, String path) throws IOException {
        OutputStream out = new FileOutputStream(path);
        excelParams.setExcelType(ExcelTypeEnum.XLSX);
        dynamicResolveExcel2007ToFile(excelParams, out);
        return excelParams;
    }

    @SneakyThrows
    private static void dynamicResolveExcel2007ToFile(EasyExcelParams excelParams, OutputStream out) {
        List<WriteHandler> handlers = excelParams.getHandlers();
        ExcelWriter writer;
        if (StringUtils.isNotBlank(excelParams.getTemplatePath())) {
            ClassPathResource classPathResource = new ClassPathResource(excelParams.getTemplatePath());
            writer = EasyExcel.write(out).withTemplate(classPathResource.getInputStream()).build();
        } else {
            writer = EasyExcel.write(out).build();
        }
        List<SheetData> sheetDatas = excelParams.getSheetDatas();
        try {
            for (int i = 0; i < sheetDatas.size(); i++) {
                SheetData sheetData = sheetDatas.get(i);
                List<List<String>> head = sheetData.getHead();
                Class dataModelClazz = sheetData.getDataModelClazz();
                Integer numberOfSheets = writer.writeContext().writeWorkbookHolder().getWorkbook().getNumberOfSheets();
                ExcelWriterSheetBuilder excelWriterSheetBuilder = EasyExcel.writerSheet(numberOfSheets, sheetData.getSheetName());
                if (CollectionUtils.isNotEmpty(handlers)) {
                    for (WriteHandler handler : handlers) {
                        excelWriterSheetBuilder.registerWriteHandler(handler);
                    }
                }
                List<WriteHandler> sheetDataHandlers = sheetData.getHandlers();
                if (CollectionUtils.isNotEmpty(sheetDataHandlers)) {
                    for (WriteHandler sheetDataHandler : sheetDataHandlers) {
                        excelWriterSheetBuilder.registerWriteHandler(sheetDataHandler);
                    }
                }
                WriteSheet writeSheet;
                if (head != null) {
                    writeSheet = excelWriterSheetBuilder.head(head).build();
                } else {
                    writeSheet = excelWriterSheetBuilder.head(dataModelClazz).build();
                }
                writer.write(sheetData.getData(), writeSheet);

            }

            if (StringUtils.isNotBlank(excelParams.getTemplateSheetName())) {
                Integer numberOfSheets = writer.writeContext().writeWorkbookHolder().getWorkbook().getNumberOfSheets();
                writer.writeContext().writeWorkbookHolder().getWorkbook().setSheetOrder(excelParams.getTemplateSheetName(), numberOfSheets - 1);
            }
            // 默认打开第一个sheet
            writer.writeContext().writeWorkbookHolder().getWorkbook().setActiveSheet(0);

        } catch (Exception e) {
            log.error("dynamicExportExcel2007====  " + e.getMessage(), e);
        } finally {
            //必须保证写出结束后关闭IO
            Optional.ofNullable(writer).ifPresent(ExcelWriter::finish);
            out.close();
        }
    }


    @SneakyThrows
    private static void dynamicResolveExcel2007(EasyExcelParams excelParams, OutputStream out) {
        List<WriteHandler> handlers = excelParams.getHandlers();
        ExcelWriter writer;
        if (StringUtils.isNotBlank(excelParams.getTemplatePath())) {
            ClassPathResource classPathResource = new ClassPathResource(excelParams.getTemplatePath());
            writer = EasyExcel.write(out).withTemplate(classPathResource.getInputStream()).build();
        } else {
            writer = EasyExcel.write(out).build();
        }
        List<SheetData> sheetDatas = excelParams.getSheetDatas();
        try {
            for (int i = 0; i < sheetDatas.size(); i++) {
                SheetData sheetData = sheetDatas.get(i);
                List<List<String>> head = sheetData.getHead();
                Class dataModelClazz = sheetData.getDataModelClazz();
                Integer numberOfSheets = writer.writeContext().writeWorkbookHolder().getWorkbook().getNumberOfSheets();
                ExcelWriterSheetBuilder excelWriterSheetBuilder = EasyExcel.writerSheet(numberOfSheets, sheetData.getSheetName());
                if (CollectionUtils.isNotEmpty(handlers)) {
                    for (WriteHandler handler : handlers) {
                        excelWriterSheetBuilder.registerWriteHandler(handler);
                    }
                }
                List<WriteHandler> sheetDataHandlers = sheetData.getHandlers();
                if (CollectionUtils.isNotEmpty(sheetDataHandlers)) {
                    for (WriteHandler sheetDataHandler : sheetDataHandlers) {
                        excelWriterSheetBuilder.registerWriteHandler(sheetDataHandler);
                    }
                }

                WriteSheet writeSheet;
                if (head != null) {
                    writeSheet = excelWriterSheetBuilder.head(head).build();
                } else {
                    writeSheet = excelWriterSheetBuilder.head(dataModelClazz).build();
                }

                if (sheetData.getColumnWidthMap() != null) {
                    writeSheet.setColumnWidthMap(sheetData.getColumnWidthMap());
                }

                writer.write(sheetData.getData(), writeSheet);

            }

            if (StringUtils.isNotBlank(excelParams.getTemplateSheetName())) {
                Integer numberOfSheets = writer.writeContext().writeWorkbookHolder().getWorkbook().getNumberOfSheets();
                writer.writeContext().writeWorkbookHolder().getWorkbook().setSheetOrder(excelParams.getTemplateSheetName(), numberOfSheets - 1);
            }
            // 默认打开第一个sheet
            writer.writeContext().writeWorkbookHolder().getWorkbook().setActiveSheet(0);

        } catch (Exception e) {
            log.error("dynamicExportExcel2007====  " + e.getMessage(), e);
        } finally {
            //必须保证写出结束后关闭IO
            Optional.ofNullable(writer).ifPresent(ExcelWriter::finish);
        }
    }

    /**
     * 下载EXCEL文件2003版本
     *
     * @param excelParams 使用EasyExcel导出文件需要设置的相关参数对象
     */
    public static void exportExcel2003(EasyExcelParams excelParams) throws IOException {
        exportExcel(excelParams, ExcelTypeEnum.XLS);
    }

    /**
     * 根据参数和版本枚举导出excel文件
     *
     * @param excelParams 参数实体
     * @param excelType   excel类型枚举 03 or 07
     * @throws IOException IOException
     */
    public static void exportExcel(EasyExcelParams excelParams, ExcelTypeEnum excelType) throws IOException {
        HttpServletResponse response = excelParams.getResponse();
        ServletOutputStream out = response.getOutputStream();

        prepareResponds(response, excelParams.getExcelNameWithoutExt(), excelType);
        ExcelWriter writer;
        if (StringUtils.isNotBlank(excelParams.getTemplatePath())) {
            ClassPathResource classPathResource = new ClassPathResource(excelParams.getTemplatePath());
            writer = EasyExcel.write(out).withTemplate(classPathResource.getInputStream()).build();
        } else {
            writer = EasyExcel.write(out).build();
            if(Boolean.FALSE.equals(excelParams.getIsNotStringTrim())){
                writer.writeContext().currentWriteHolder().globalConfiguration().setAutoTrim(false);
            }
        }
        List<SheetData> sheetDatas = excelParams.getSheetDatas();
        List<WriteHandler> handlers = excelParams.getHandlers();
        try {
            for (int i = 0; i < sheetDatas.size(); i++) {
                SheetData sheetData = sheetDatas.get(i);
                List<WriteHandler> sheetDataHandlers = sheetData.getHandlers();
                Integer numberOfSheets = writer.writeContext().writeWorkbookHolder().getWorkbook().getNumberOfSheets();
                ExcelWriterSheetBuilder excelWriterSheetBuilder = new ExcelWriterSheetBuilder();
                if(sheetData.getIsUseModelClazzHeader()) {
                     excelWriterSheetBuilder = EasyExcel.writerSheet(numberOfSheets, sheetData.getSheetName()).head(sheetData.getDataModelClazz())
                            .registerWriteHandler(new SpinnerWriteHandler());
                } else {
                    excelWriterSheetBuilder = EasyExcel.writerSheet(numberOfSheets, sheetData.getSheetName()).head(sheetData.getHead())
                            .registerWriteHandler(new NoClazzSpinnerWriteHandle(sheetData.getMapDropDownData())).registerConverter(new EnumExcelConverter());
                }
                if (CollectionUtils.isNotEmpty(handlers)) {
                    for (WriteHandler handler : handlers) {
                        excelWriterSheetBuilder.registerWriteHandler(handler);
                    }
                }

                if (CollectionUtils.isNotEmpty(sheetDataHandlers)) {
                    for (WriteHandler handler : sheetDataHandlers) {
                        excelWriterSheetBuilder.registerWriteHandler(handler);
                    }
                }

                WriteSheet writeSheet = excelWriterSheetBuilder.build();

                //  EasyExcelFactory.write(out, sheetDatas.get(i).getDataModelClazz()).registerWriteHandler(new SpinnerWriteHandler()).sheet().doWrite(new ArrayList());
                if (sheetData.getColumnWidthMap() != null) {
                    writeSheet.setColumnWidthMap(sheetData.getColumnWidthMap());
                }
                writer.write(sheetData.getData(), writeSheet);
            }
            if (StringUtils.isNotBlank(excelParams.getTemplateSheetName())) {
                Integer numberOfSheets = writer.writeContext().writeWorkbookHolder().getWorkbook().getNumberOfSheets();
                writer.writeContext().writeWorkbookHolder().getWorkbook().setSheetOrder(excelParams.getTemplateSheetName(), numberOfSheets - 1);
            }
            for (int i = 0; i < sheetDatas.size(); i++) {
                SheetData sheetData = sheetDatas.get(i);
                if (!sheetData.getColumnHidden().isEmpty()) {
                    for (Integer column : sheetData.getColumnHidden()) {
                        writer.writeContext().writeWorkbookHolder().getWorkbook().getSheetAt(i).setColumnHidden(column, true);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            //必须保证写出结束后关闭IO
            Optional.ofNullable(writer).ifPresent(ExcelWriter::finish);
        }


    }


    /**
     * 设置response相关参数
     *
     * @param response response
     * @param fileName 文件名
     * @param typeEnum excel类型
     * @throws UnsupportedEncodingException e
     */
    private static void prepareResponds(HttpServletResponse response, String fileName, ExcelTypeEnum typeEnum) throws UnsupportedEncodingException {
        //contentType默认是.xls类型
        response.setContentType("application/vnd.ms-excel;charset=utf-8");

        if (".xlsx".equals(typeEnum.getValue())) {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        }

        response.setHeader("Content-disposition", String.format("attachment; filename=%s", URLEncoder.encode(fileName, "UTF-8") + typeEnum.getValue()));

    }

    /*---------------------------------------------- 以下为读取相关 ----------------------------------------------*/

    /**
     * 读取 Excel(支持单个model的多个sheet)
     *
     * @param excel    文件
     * @param rowModel 实体类映射
     * @param listener 用于读取excel的listener
     * @return 读取的数据和错误的数据
     */
    @SuppressWarnings("unchecked")
    public static <T> ReadMsg<T> readExcel(MultipartFile excel, Class rowModel, BaseExcelListener listener) {
        ExcelReader reader = null;
        try {
            reader = getReader(excel, rowModel, listener);
            Assert.notNull(reader, "导入Excel失败!");
            Integer totalSheetCount = reader.getSheets().size();
            for (Integer i = 0; i < totalSheetCount; i++) {
                reader.read(EasyExcel.readSheet(i).build());
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("非excel文件或者已损坏", ErrorCode.FILE_TYPE_ERROR);
        } finally {
            // 这里千万别忘记关闭,读的时候会创建临时文件,到时磁盘会崩的
            Optional.ofNullable(reader).ifPresent(ExcelReader::finish);
        }
        List<T> data = listener.getData();
        List<ErrRows> errRowsList = listener.getErrRowsList();
        ReadMsg<T> readMsg = new ReadMsg();
        readMsg.setErrRows(errRowsList);
        readMsg.setObjects(data);
        return readMsg;
    }

    /**
     * 读取 Excel(支持单个model的多个sheet)
     *
     * @param excel    文件
     * @param rowModel 实体类映射
     * @param listener 用于读取excel的listener
     * @return 读取的数据和错误的数据
     */
    @SuppressWarnings("unchecked")
    public static <T> Map<String, ReadMsg<T>> readMultiExcel(MultipartFile excel, Class rowModel, BaseExcelListener listener) {
        ExcelReader reader = null;
        Map<Integer, String> sheetNameNoMap = new LinkedHashMap<>();
        try {
            listener.setIsMultiSheetData(true);
            reader = getReader(excel, rowModel, listener);
            Assert.notNull(reader, "导入Excel失败!");
            List<Sheet> sheets = reader.getSheets();
            for (Integer i = 0; i < sheets.size(); i++) {
                Sheet sheet = sheets.get(i);
                ReadSheet readSheet = EasyExcel.readSheet(sheet.getSheetNo() - 1, sheet.getSheetName()).build();
                sheetNameNoMap.put(sheet.getSheetNo() - 1, sheet.getSheetName());
                reader.read(readSheet);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("非excel文件或者已损坏", ErrorCode.FILE_TYPE_ERROR);
        } finally {
            // 这里千万别忘记关闭,读的时候会创建临时文件,到时磁盘会崩的
            Optional.ofNullable(reader).ifPresent(ExcelReader::finish);
        }
        Map<Integer, List<T>> data = listener.getMultiDataMap();
        List<ErrRows> errRowsList = listener.getErrRowsList();

        Map<String, ReadMsg<T>> resultMap = new HashMap<>();
        for (Integer sheetNo : sheetNameNoMap.keySet()) {
            ReadMsg<T> readMsg = new ReadMsg();
            readMsg.setErrRows(errRowsList.stream().filter(d -> NumberUtils.isCreatable(d.getSheetNo())).filter(d -> sheetNo == Integer.parseInt(d.getSheetNo())).collect(Collectors.toList()));
            readMsg.setObjects(MapUtils.getObject(data, sheetNo, new ArrayList<>()));

            resultMap.put(sheetNameNoMap.get(sheetNo), readMsg);
        }

        return resultMap;
    }

    /**
     * 默认对象监听器
     *
     * @param excel
     * @param rowModel
     * @param <T>
     * @return
     */
    public static <T> ReadMsg<T> readExcel(MultipartFile excel, Class<T> rowModel) {
        ObjectExcelListener<T> tObjectExcelListener = new ObjectExcelListener<>();
        return readExcel(excel, rowModel, tObjectExcelListener);
    }

    /**
     * 读取 Excel(按sheetNum读取多个model的多个sheet,读取指定的sheet)
     *
     * @param excel    文件
     * @param rowModel 实体类映射
     * @param listener 用于读取excel的listener
     * @return 读取的数据和错误的数据
     */
    @SuppressWarnings("unchecked")
    public static <T> ReadMsg<T> readExcel(MultipartFile excel, Class rowModel, BaseExcelListener listener, Integer sheetNum) {
        ExcelReader reader = null;
        try {
            reader = getReader(excel, rowModel, listener);
            Assert.notNull(reader, "导入Excel失败!");
            //Integer totalSheetCount = reader.getSheets().size();
            reader.read(EasyExcel.readSheet(sheetNum).build());
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("非excel文件或者已损坏", ErrorCode.FILE_TYPE_ERROR);
        } finally {
            // 这里千万别忘记关闭,读的时候会创建临时文件,到时磁盘会崩的
            Optional.ofNullable(reader).ifPresent(ExcelReader::finish);
        }
        List<T> data = listener.getData();
        List<ErrRows> errRowsList = listener.getErrRowsList();
        ReadMsg<T> readMsg = new ReadMsg();
        readMsg.setErrRows(errRowsList);
        readMsg.setObjects(data);
        return readMsg;
    }

    /**
     * 读取 Excel(按sheetName读取多个model的多个sheet,读取指定的sheet)
     *
     * @param excel    文件
     * @param rowModel 实体类映射
     * @param listener 用于读取excel的listener
     * @return 读取的数据和错误的数据
     */
    public static <T> ReadMsg<T> readExcel(MultipartFile excel, Class<T> rowModel, BaseExcelListener listener, String sheetName) {
        ExcelReader reader = null;
        ReadMsg<T> readMsg = new ReadMsg();
        try {
            reader = getReader(excel, rowModel, listener);
            Assert.notNull(reader, "导入Excel失败!");
            //Integer totalSheetCount = reader.getSheets().size();
            reader.read(EasyExcel.readSheet(sheetName).build());
            List<T> data = listener.getData();
            List<ErrRows> errRowsList = listener.getErrRowsList();
            readMsg.setErrRows(errRowsList);
            readMsg.setObjects(data);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("非excel文件或者已损坏", ErrorCode.FILE_TYPE_ERROR);
        } finally {
            // 这里千万别忘记关闭,读的时候会创建临时文件,到时磁盘会崩的
            Optional.ofNullable(reader).ifPresent(ExcelReader::finish);
        }
        return readMsg;
    }

    /**
     * 读取 Excel(按sheetName读取多个model的多个sheet,读取指定的sheet)
     *
     * @param excel    文件
     * @param rowModel 实体类映射
     * @return 读取的数据和错误的数据
     */
    public static <T> List readExcel(MultipartFile excel, Class<T> rowModel, String sheetName) {
        ExcelReader reader = null;
        ObjectExcelListener listener = new ObjectExcelListener();
        try {
            reader = getReader(excel, rowModel, listener);
            Assert.notNull(reader, "导入Excel失败!");
            reader.read(EasyExcel.readSheet(sheetName).build());
            List<T> data = listener.getData();
            if (!listener.getSheetNames().contains(sheetName)) {
                throw new BusinessException("excel模板不正确，未找到"+ sheetName, ErrorCode.FILE_TYPE_ERROR);
            }
            return data;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("非excel文件或者已损坏", ErrorCode.FILE_TYPE_ERROR);
        } finally {
            // 这里千万别忘记关闭,读的时候会创建临时文件,到时磁盘会崩的
            Optional.ofNullable(reader).ifPresent(ExcelReader::finish);
        }
    }

    /**
     * 获取sheet名
     */

//    public List<String> getSheetNames (MultipartFile excel, Class rowModel,
//                                       BaseExcelListener listener){
//        ExcelReader reader = getReader(excel, rowModel, listener);
//        List<ReadSheet> readSheets = reader.excelExecutor().sheetList();
//        for (ReadSheet readSheet:readSheets
//             ) {
//            ExcelReader read = reader.read(readSheet);
//        }
//    }

    /**
     * 获取 ExcelReader
     *
     * @param excel    需要解析的 Excel 文件
     * @param listener new ExcelListener()
     * @return ExcelReader 或 null
     */
    private static ExcelReader getReader(MultipartFile excel, Class rowModel,
                                         BaseExcelListener listener) {
        String filename = excel.getOriginalFilename();
        if (StringUtils.isEmpty(filename) || (!filename.toLowerCase().endsWith(".xls") && !filename.toLowerCase().endsWith(".xlsx"))) {
            throw new BusinessException(new ErrorCode(210037, "文件格式错误,只支持xls和xlsx文件"));
        }

        try {
            return EasyExcel.read(excel.getInputStream(), rowModel, listener).build();
        } catch (IOException e) {
            log.error("/*------- 读取Excel IO异常 -------*/", e);
        }
        return null;
    }


    /**
     * 读第一个sheet
     *
     * @param excel
     * @return
     */
    public static ReadMsg readMap(MultipartFile excel) {
        return readMap(excel, NumberUtils.INTEGER_ZERO);
    }

    /**
     * 用map来读取数据
     *
     * @param excel
     * @param num
     * @return
     * @throws IOException
     */
    @SneakyThrows
    public static ReadMsg readMap(MultipartFile excel, Integer num) {
        MapBaseExcelListener listener = new MapBaseExcelListener();
        return readMap(excel, num, listener);
    }

    /**
     * @param excel
     * @param num
     * @param listener 自定义扩展map 监听器
     * @return
     * @throws IOException
     */

    @SneakyThrows
    public static ReadMsg readMap(MultipartFile excel, Integer num, MapBaseExcelListener listener) {
        ExcelReader reader = null;
        ReadMsg readMsg = new ReadMsg();
        try {
            reader = EasyExcel.read(excel.getInputStream(), listener).build();
            Assert.notNull(reader, "导入Excel失败!");
            reader.read(EasyExcel.readSheet(num).build());
            List<Map<Integer, String>> mapData = listener.getMapData();
            List<ErrRows> errRowsList = listener.getErrRowsList();
            readMsg.setErrRows(errRowsList);
            readMsg.setMapData(mapData);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("非excel文件或者已损坏", ErrorCode.FILE_TYPE_ERROR);
        } finally {
            // 这里千万别忘记关闭,读的时候会创建临时文件,到时磁盘会崩的
            Optional.ofNullable(reader).ifPresent(ExcelReader::finish);
        }
        return readMsg;
    }


    /**
     * 按sheet名称读取excel并转成map数据列表
     * @param excel
     * @param sheetName
     * @param listener
     * @return
     */
    @SneakyThrows
    public static List<Map<Integer, String>> readMap(MultipartFile excel, String sheetName, MapBaseExcelListener listener) {
        ExcelReader reader = null;
        try {
            reader = EasyExcel.read(excel.getInputStream(), listener).build();
            Assert.notNull(reader, "导入Excel失败!");
            reader.read(EasyExcel.readSheet(sheetName).build());
            if (!listener.getSheetNames().contains(sheetName)) {
                throw new BusinessException("excel模板不正确，未找到Sheet"+ sheetName, ErrorCode.FILE_TYPE_ERROR);
            }
            return listener.getMapData();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("非excel文件或者已损坏", ErrorCode.FILE_TYPE_ERROR);
        } finally {
            // 这里千万别忘记关闭,读的时候会创建临时文件,到时磁盘会崩的
            Optional.ofNullable(reader).ifPresent(ExcelReader::finish);
        }
    }

    /**
     * 按sheet名称读取excel并转成map数据列表
     * @param excel
     * @param sheetName
     * @param listener
     * @return
     */
    @SneakyThrows
    public static List<Map<Integer, String>> readMap(InputStream inputStream, String sheetName, MapBaseExcelListener listener) {
        ExcelReader reader = null;
        try {
            reader = EasyExcel.read(inputStream, listener).build();
            Assert.notNull(reader, "导入Excel失败!");
            reader.read(EasyExcel.readSheet(sheetName).build());
            if (!listener.getSheetNames().contains(sheetName)) {
                throw new BusinessException("excel模板不正确，未找到Sheet"+ sheetName, ErrorCode.FILE_TYPE_ERROR);
            }
            return listener.getMapData();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("非excel文件或者已损坏", ErrorCode.FILE_TYPE_ERROR);
        } finally {
            // 这里千万别忘记关闭,读的时候会创建临时文件,到时磁盘会崩的
            Optional.ofNullable(reader).ifPresent(ExcelReader::finish);
        }
    }

    /**
     * 获取所有的sheet数据
     *
     * @param excel
     * @param listener
     * @return
     */
    @SneakyThrows
    public static Map<String, ReadMsg> readMultiSheetMap(MultipartFile excel, MultiSheetMapBaseExcelListener listener) {
        return readMultiSheetMap(excel, listener, null);
    }

    /**
     * 获取所有的sheet数据
     *
     * @param excel
     * @param listener
     * @param filterSheetName
     * @return
     */

    @SneakyThrows
    public static Map<String, ReadMsg> readMultiSheetMap(MultipartFile excel, MultiSheetMapBaseExcelListener listener, String... filterSheetNames) {
        ExcelReader reader = null;
        Map<String, ReadMsg> sheetsMap = new HashMap<>();
        try {
            reader = EasyExcel.read(excel.getInputStream(), listener).build();
            Assert.notNull(reader, "导入Excel失败!");
            for (ReadSheet readSheet : reader.excelExecutor().sheetList()) {
                String sheetName = readSheet.getSheetName();
                if (filterSheetNames != null && filterSheetNames.length > 0) {
                    if (Arrays.stream(filterSheetNames).noneMatch(d->sheetName.contains(d))) {
                        continue;
                    }
                }
                Integer sheetNo = readSheet.getSheetNo();
                reader.read(readSheet);

                ReadMsg readMsg = new ReadMsg();
                List<Map<Integer, String>> mapData = listener.getMultiSheetMapData().get(sheetName);
                List<ErrRows> errRowsList = listener.getErrRowsList();
                readMsg.setErrRows(errRowsList.stream().filter(err -> String.valueOf(sheetNo).equals(err.getSheetNo())).collect(Collectors.toList()));
                readMsg.setMapData(mapData);

                sheetsMap.put(sheetName, readMsg);
            }


        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("非excel文件或者已损坏", ErrorCode.FILE_TYPE_ERROR);
        } finally {
            // 这里千万别忘记关闭,读的时候会创建临时文件,到时磁盘会崩的
            Optional.ofNullable(reader).ifPresent(ExcelReader::finish);
        }
        return sheetsMap;
    }

    /**
     * 获取所有的sheet数据
     *
     * @param excel
     * @param listener
     * @param filterSheetName
     * @return
     */

    @SneakyThrows
    public static Map<String, ReadMsg> readMultiSheetMap(InputStream ins, MultiSheetMapBaseExcelListener listener, String... filterSheetNames) {
        ExcelReader reader = null;
        Map<String, ReadMsg> sheetsMap = new HashMap<>();
        try {
            reader = EasyExcel.read(ins, listener).build();
            Assert.notNull(reader, "导入Excel失败!");
            for (ReadSheet readSheet : reader.excelExecutor().sheetList()) {
                String sheetName = readSheet.getSheetName();
                if (filterSheetNames != null && filterSheetNames.length > 0) {
                    if (Arrays.stream(filterSheetNames).noneMatch(d->sheetName.contains(d))) {
                        continue;
                    }
                }
                Integer sheetNo = readSheet.getSheetNo();
                reader.read(readSheet);

                ReadMsg readMsg = new ReadMsg();
                List<Map<Integer, String>> mapData = listener.getMultiSheetMapData().get(sheetName);
                List<ErrRows> errRowsList = listener.getErrRowsList();
                readMsg.setErrRows(errRowsList.stream().filter(err -> String.valueOf(sheetNo).equals(err.getSheetNo())).collect(Collectors.toList()));
                readMsg.setMapData(mapData);

                sheetsMap.put(sheetName, readMsg);
            }


        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("非excel文件或者已损坏", ErrorCode.FILE_TYPE_ERROR);
        } finally {
            // 这里千万别忘记关闭,读的时候会创建临时文件,到时磁盘会崩的
            Optional.ofNullable(reader).ifPresent(ExcelReader::finish);
        }
        return sheetsMap;
    }

    /**
     * 导出数据
     *
     * @param response
     * @param rowModel
     * @param datas
     * @param sheetName
     * @param <T>
     */
    @SneakyThrows
    public static <T> void simpleExportExcel(HttpServletResponse response, Class<T> rowModel, List<T> datas, String sheetName) {

        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode(sheetName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), rowModel).sheet("Sheet1").doWrite(datas);
    }

    public static boolean isExistSheet(MultipartFile excel, String sheetName) {
        ExcelReader reader = null;
        boolean exist = false;
        try {
            reader = EasyExcel.read(excel.getInputStream()).build();
            Assert.notNull(reader, "导入Excel失败!");
            for (ReadSheet readSheet : reader.excelExecutor().sheetList()) {
                if (Objects.equals(readSheet.getSheetName(), sheetName)) {
                    exist = true;
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("非excel文件或者已损坏", ErrorCode.FILE_TYPE_ERROR);
        } finally {
            // 这里千万别忘记关闭,读的时候会创建临时文件,到时磁盘会崩的
            Optional.ofNullable(reader).ifPresent(ExcelReader::finish);
        }
        return exist;
    }

    /**
     * @param excel
     * @param num
     * @param listener 自定义扩展map 监听器
     * @return
     * @throws IOException
     */

    @SneakyThrows
    public static ReadMsg readMap(File excel, Integer num, MapBaseExcelListener listener) {
        ExcelReader reader = null;
        ReadMsg readMsg = new ReadMsg();
        try {
            reader = EasyExcel.read(excel, listener).build();
            Assert.notNull(reader, "导入Excel失败!");
            reader.read(EasyExcel.readSheet(num).build());
            List<Map<Integer, String>> mapData = listener.getMapData();
            List<ErrRows> errRowsList = listener.getErrRowsList();
            readMsg.setErrRows(errRowsList);
            readMsg.setMapData(mapData);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("非excel文件或者已损坏", ErrorCode.FILE_TYPE_ERROR);
        } finally {
            // 这里千万别忘记关闭,读的时候会创建临时文件,到时磁盘会崩的
            Optional.ofNullable(reader).ifPresent(ExcelReader::finish);
        }
        return readMsg;
    }

    /**
     * 获取字段的ExcelProperty注解信息并对比表头Map
     *
     * @param clazz 实体类Class
     * @param head  表头Map（键为列索引，值为表头名称）
     * @return 校验结果Map（键为字段名，值为校验详情）
     */
    public static <T> Map<String, String> checkExcelAnnotations(Class<T> clazz, Map<Integer, String> head) {
        Map<String, String> result = new HashMap<>();
        for (Field field : clazz.getDeclaredFields()) {
            ExcelProperty excelProp = field.getAnnotation(ExcelProperty.class);
            if (excelProp != null) {
                int index = excelProp.index(); // 获取注解的index
                if (index < 0) {
                    continue;
                }
                String[] values = excelProp.value(); // 获取注解的value（可能为多个）
                String annotationValue = values.length > 0 ? values[0] : "";

                // 根据index从head中获取表头值
                String headerValue = head.get(index);

                // 对比注解的value和表头值
                if (headerValue != null) {
                    if (!annotationValue.equals(headerValue)) {
                        throw new BusinessException(new ErrorCode(210006, "第" + (index + 1) + "列应为" + annotationValue));
                    }
                } else {
                    throw new BusinessException(new ErrorCode(210006, "缺少第" + (index + 1) + "列" + annotationValue));
                }
            }
        }
        return result;
    }
}
