DROP TABLE IF EXISTS "public"."report_person";
CREATE TABLE  "public"."report_person"
(
    "id"                      VARCHAR(36) NOT NULL,
    "person_id"               VARCHAR(36) NOT NULL,
    "project_id"              VARCHAR(36) NOT NULL,
    "questionnaire_id"        VARCHAR(36) NOT NULL,
    "pay"                     BOOL,
    "create_by"               VARCHAR(36),
    "create_time"             TIMESTAMP(3) DEFAULT NOW(),
    "update_time"             TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"          VARCHAR(36),
    "is_deleted"              BOOL         DEFAULT FALSE,
    "app_id"                  VARCHAR(36),
    CONSTRAINT  "___pk__report_person___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."report_person"."person_id" IS '人员id';
COMMENT ON COLUMN "public"."report_person"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."report_person"."questionnaire_id" IS '问卷id';
COMMENT ON COLUMN "public"."report_person"."pay" IS '是否付费';
COMMENT ON TABLE  "public"."report_person" IS '报告人员';

