DROP TABLE IF EXISTS prisma_history_data_approval;
CREATE TABLE prisma_history_data_approval
(
    "id"                              VARCHAR(36)  NOT NULL,
    "prisma_history_data_id"          VARCHAR(36)  NOT NULL,
    "question_code"                   VARCHAR(36)  NOT NULL,
    "organization_code"               VARCHAR(36)  NOT NULL,
    "approval"                        NUMERIC(53,2),
    "neutral"                         NUMERIC(53,2),
	"disapproval"                     NUMERIC(53,2),
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___prisma_history_data_approval___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."prisma_history_data_approval" IS 'prisma历史对比数据赞成度表';
COMMENT ON COLUMN "public"."prisma_history_data_approval"."prisma_history_data_id" IS 'prisma历史对比数据ID';
COMMENT ON COLUMN "public"."prisma_history_data_approval"."question_code" IS '题目编码';
COMMENT ON COLUMN "public"."prisma_history_data_approval"."organization_code" IS '组织编码';
COMMENT ON COLUMN "public"."prisma_history_data_approval"."approval" IS '赞成度';
COMMENT ON COLUMN "public"."prisma_history_data_approval"."neutral" IS '中立';
COMMENT ON COLUMN "public"."prisma_history_data_approval"."disapproval" IS '不赞成';

CREATE INDEX ___idx_prisma_history_data_approval_prisma_history_data_id___ ON public.prisma_history_data_approval(prisma_history_data_id);
CREATE INDEX ___idx_prisma_history_data_approval_question_code___ ON public.prisma_history_data_approval(question_code);
CREATE INDEX ___idx_prisma_history_data_approval_organization_code___ ON public.prisma_history_data_approval(organization_code);

