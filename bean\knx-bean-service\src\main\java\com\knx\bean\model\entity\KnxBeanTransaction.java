package com.knx.bean.model.entity;

import com.knx.bean.model.enums.KnxBeanTransactionStatusEnum;
import com.knx.bean.model.enums.KnxBeanTransactionTypeEnum;
import com.knx.common.base.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 肯豆交易记录
 */
@Data
@ApiModel(value = "KnxBeanTransaction")
@FieldNameConstants
public class KnxBeanTransaction extends BaseModel {
    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id", example = "1331184718107910145")
    private String tenantId;
    /**
     * 租户消费或者充值数量
     */
    @ApiModelProperty("租户消费或者充值数量")
    @NotNull(message = "充值数量不能为空")
    private BigDecimal amount;
    /**
     * 合同金额
     */
    @ApiModelProperty("合同金额")
    private BigDecimal contractAmount;
    /**
     * 类型 0 充值 1 取消充值 2 创建活动 3 查看报告 4 下载报告
     */
    @ApiModelProperty("类型 充值  取消充值  创建活动  查看报告 下载报告 赠送肯豆")
    private KnxBeanTransactionTypeEnum type;
    /**
     * 合同编号，类型为充值时，这个字段才有用
     */
    @ApiModelProperty(value = "合同编号，类型为充值时，这个字段才有用", example = "1331184718107910145")
    private String contractNo;

    @ApiModelProperty(value = "流水订单编号", example = "1331184718107910145")
    private String orderNo;

    @ApiModelProperty("租户余额")
    private BigDecimal balance;

    @ApiModelProperty("充值的流水明细取消和执行")
    private KnxBeanTransactionStatusEnum status;

    @ApiModelProperty(value = "备注")
    private String memo;
}
