DROP TABLE IF EXISTS survey_lottery_record;
CREATE TABLE survey_lottery_record
(
    "id"                              VARCHAR(36)  NOT NULL,
    "project_id"                      VARCHAR(36)  NOT NULL,
    "person_id"                       VARCHAR(36)  NOT NULL,
    "lottery_id"                      VARCHAR(36)  NOT NULL,
    "prize_id"                        VARCHAR(36)  NOT NULL,
    "is_partake_prize"                BOOL DEFAULT false,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___survey_lottery_record___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."survey_lottery_record" IS '调研抽奖奖品表';
COMMENT ON COLUMN "public"."survey_lottery_record"."project_id" IS '活动ID';
COMMENT ON COLUMN "public"."survey_lottery_record"."person_id" IS '人员ID';
COMMENT ON COLUMN "public"."survey_lottery_record"."lottery_id" IS '抽奖ID';
COMMENT ON COLUMN "public"."survey_lottery_record"."prize_id" IS '奖品ID';
COMMENT ON COLUMN "public"."survey_lottery_record"."is_partake_prize" IS '是否参与奖';

CREATE INDEX ___idx_survey_lottery_record_project_id___ ON public.survey_lottery_record(project_id);
CREATE INDEX ___idx_survey_lottery_record_person_id___ ON public.survey_lottery_record(person_id);
CREATE INDEX ___idx_survey_lottery_record_lottery_id___ ON public.survey_lottery_record(lottery_id);
CREATE INDEX ___idx_survey_lottery_record_prize_id___ ON public.survey_lottery_record(prize_id);
