
-- CREATE TABLE survey_standard_pierce_through_combination (
--     id varchar(36) NOT NULL,
--     question_id varchar(36) NULL,
--     question_name json NULL,
--     parent_question_id varchar(36) ,
--     parent_question_name json NULL,
--     standard_question_option_combination json NULL,
--     create_by varchar(36) NULL,
--     create_time timestamp(3) NULL DEFAULT now(),
--     update_time timestamp(3) NULL DEFAULT now(),
--     last_update_by varchar(36) NULL,
--     is_deleted bool NULL DEFAULT false,
--     app_id varchar(36) null,
--     CONSTRAINT "___pk__survey_standard_pierce_through_combination___" PRIMARY KEY (id)
-- );
-- COMMENT ON COLUMN "public"."survey_standard_pierce_through_combination"."question_id" IS '结果题id';
-- COMMENT ON COLUMN "public"."survey_standard_pierce_through_combination"."question_name" IS '结果题题目';
-- COMMENT ON COLUMN "public"."survey_standard_pierce_through_combination"."parent_question_id" IS '条件题id';
-- COMMENT ON COLUMN "public"."survey_standard_pierce_through_combination"."parent_question_name" IS '结果题题目';
-- COMMENT ON COLUMN "public"."survey_standard_pierce_through_combination"."standard_question_option_combination" IS '条件题选项';
-- COMMENT ON TABLE  "public"."survey_standard_pierce_through_combination" IS '穿透题组合表';


ALTER TABLE public.survey_standard_question
    ADD is_pierce_through bool NULL DEFAULT false ;
COMMENT
ON COLUMN public.survey_standard_question.is_pierce_through IS '是否穿透题';
