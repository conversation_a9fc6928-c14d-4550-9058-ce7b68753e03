package com.knx.bean.model.dto.client;

import com.knx.common.base.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

/**
 * <AUTHOR>
 * @Package com.knx.userAccount.model
 * @date 2020/8/24 13:35
 */
@Data
@ApiModel(value = "Permission")
@FieldNameConstants
public class PermissionDTO extends BaseModel {

    @ApiModelProperty("权限名称")
    private String name;


}

