package com.knx.survey.common.util.excelUtil.converter;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.math.BigDecimal;

public class CustomStringConverter implements Converter<String>{
    @Override
    public Class supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public String convertToJavaData(CellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        // 根据cellData的类型进行转换
        if (cellData.getType() == CellDataTypeEnum.NUMBER) {
            // 使用BigDecimal转换为字符串
            BigDecimal numberValue = cellData.getNumberValue();
            return NumberUtil.round(numberValue, 1).toPlainString();
        } else if (cellData.getType() == CellDataTypeEnum.STRING) {
            return cellData.getStringValue();
        } else if (cellData.getType() == CellDataTypeEnum.EMPTY) {
            return null;
        } else {
            // 其他类型，可以按需处理，比如布尔值、日期等，这里直接返回字符串形式
            return cellData.toString();
        }
    }

    @Override
    public CellData convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new CellData(value);
    }
}
