package com.knx.db.updater.survey.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.knx.common.updater.model.TenantDbupdater;
import com.knx.common.updater.mq.KafkaConfigProperties;
import com.knx.common.updater.mq.event.DbupdaterMessage;
import com.knx.common.updater.service.api.domain.ITenantDbMaintenanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @Author: Erin
 * @Date: 2021/3/18 19:12
 * @Description:dbupdate消费者
 */
@Component
@Slf4j
@EnableKafka
public class DbUpdaterConsume {

    @Autowired
    private ITenantDbMaintenanceService tenantDbInstanceService;

    @Autowired
    private KafkaConfigProperties kafkaConfigProperties;

    /**
     * 监听DBUPDATER_INPUT队列
     */
    @KafkaListener(topics = "${kafka.topic}")
    public void comsume(String message) {
        log.info("db updater 开始消费消息类型topic:{},消息体message：{}", kafkaConfigProperties.getTopic(), message);
        if (message != null) {
            try {
                DbupdaterMessage dbupdaterMessage = JSONObject.parseObject(message, DbupdaterMessage.class);
                if (Objects.nonNull(dbupdaterMessage)) {
                    JSONObject jsonObject = (JSONObject) dbupdaterMessage.getPayload();
                    TenantDbupdater tenantDbupdater = JSONObject.toJavaObject(jsonObject, TenantDbupdater.class);
                    // 消费
                    tenantDbInstanceService.updateSingleTenantDb(tenantDbupdater);
                    log.info("db updater 消息消费类型topic:{}，消息消费结束", kafkaConfigProperties.getTopic());
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("db updater 服务消费异常,异常消息", message);
            }
        }
    }

}
