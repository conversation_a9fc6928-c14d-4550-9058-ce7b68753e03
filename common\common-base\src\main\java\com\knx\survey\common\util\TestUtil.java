package com.knx.survey.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.knx.common.base.web.response.WebResponse;
import com.knx.common.security.FeignSessionContext;
import com.knx.survey.client.uerAcount.model.LoginTokenVO;
import com.knx.survey.client.uerAcount.model.RefreshTokenVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.WebApplicationContext;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.net.URLDecoder;
import java.util.List;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2021/9/15 13:48
 */

@Slf4j
@Component
public class TestUtil {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    private MockHttpServletRequest mockHttpServletRequest;

    private MockHttpServletResponse mockHttpServletResponse;

    private MockHttpSession mockHttpSession;

    private String token;

    private String refreshToken;

    @Value("${knx.sag.test.refresh-token-url:http://127.0.0.1:8000/auth/refreshToken}")
    private String refreshTokenUrl;


    @Autowired
    private ShiroFilterFactoryBean shiroFilterFactoryBean;

    private LoginTokenVO testLogin(Object object, String loginUrl) {
        LoginTokenVO loginTokenVO = new LoginTokenVO();
        HttpHeaders accessHeaders = new HttpHeaders();
        accessHeaders.setContentType(MediaType.APPLICATION_JSON);
        if (token == null) {

            HttpEntity<Object> requestEntity = new HttpEntity<>(object, accessHeaders);

            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(
                    loginUrl,
                    requestEntity,
                    JSONObject.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            JSONObject jsonObject = responseEntity.getBody();

            JSONObject data = jsonObject.getJSONObject("data");

            if (data == null) {
                log.error("登录错误", jsonObject);
            }
            loginTokenVO = data.toJavaObject(LoginTokenVO.class);


        } else {

            RefreshTokenVO refreshTokenVO = new RefreshTokenVO();
            refreshTokenVO.setRefreshToken(refreshToken);
            HttpEntity<RefreshTokenVO> requestEntity = new HttpEntity<>(refreshTokenVO, accessHeaders);
            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(
                    refreshTokenUrl,
                    requestEntity,
                    JSONObject.class);
            JSONObject jsonObject = responseEntity.getBody().getJSONObject("data");
            loginTokenVO = jsonObject.toJavaObject(LoginTokenVO.class);
        }
        return loginTokenVO;
    }

    private void shiroLogin() {
    }

    /**
     * post请求，需要token
     *
     * @param url
     */
    public <T> T postRequestObject(String url, String json, Class<T> clazz) {
        return postRequestObject(url, json, null, clazz);
    }


    /**
     * post请求，需要token
     *
     * @param url
     */
    public <T> List<T> postRequestList(String url, String json, Class<T> clazz) {
        return postRequestList(url, json, null, clazz);
    }

    /**
     * 文件上传,返回集合，需要token
     *
     * @param url
     */
    public <T> List<T> uploadFileRequestList(String url, String filePath, String fileName, Class<T> clazz, String fileKey) {

        return fileRequestList(url, filePath, fileName, null, clazz, fileKey);
    }

    /**
     * 文件上传,返回对象，需要token
     *
     * @param url
     */
    public <T> T uploadFileRequestObject(String url, String filePath, String fileName, Class<T> clazz, String fileKey) {
        return uploadFileRequestObject(url, filePath, fileName, null, clazz, fileKey);
    }

    /**
     * post请求，需要tenantId
     *
     * @param url
     * @return JAVA对象
     */
    public <T> T postRequestObject(String url, String json, String tenantId, Class<T> clazz) {

        try {
            MockHttpServletRequestBuilder authorization = postRequestMockHttpServletRequestBuilder(url, json, tenantId);
            return getObjectData(clazz, authorization);
        } catch (Exception e) {
            log.error("POST单元测试url: {}异常,入参: {}", url, json);
        }
        return null;
    }


    /**
     * post请求，需要token
     * 下载文件
     *
     * @param url
     * @return
     */
    public void downloadFilePostRequestObject(String url, String json, String filePath) {

        try {
            MockHttpServletRequestBuilder authorization = postRequestMockHttpServletRequestBuilder(url, json, null);
            downloadFile(authorization, filePath);
        } catch (Exception e) {
            log.error("POST单元测试url: {}异常,入参: {}", url, json);
        }
    }

    /**
     * get请求，需要token
     * 下载文件
     *
     * @param url
     * @return
     */
    public void downloadFileGetRequestObject(String url, String filePath) {

        try {
            MockHttpServletRequestBuilder authorization = getRequestMockHttpServletRequestBuilder(url, null, null, null);
            downloadFile(authorization, filePath);
        } catch (Exception e) {
            log.error("get单元测试url: {}异常", url);
        }
    }


    /**
     * 文件上传
     *
     * @param url
     * @return JAVA对象
     */
    public <T> T uploadFileRequestObject(String url, String filePath, String fileName, String tenantId, Class<T> clazz, String fileKey) {

        try {
            MockHttpServletRequestBuilder authorization = uploadFileRequestMockHttpServletRequestBuilder(url, filePath, fileName, tenantId, fileKey);
            return getObjectData(clazz, authorization);
        } catch (Exception e) {
            log.error("单元测试url: {}异常,文件路径: {}", url, filePath);
        }
        return null;
    }


    private MockHttpServletRequestBuilder postRequestMockHttpServletRequestBuilder(String url, String json, String tenantId) {
        MockHttpServletRequestBuilder mockHttpServletRequestBuilder = MockMvcRequestBuilders
                .post(url)
                .content(json.getBytes())
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON_VALUE);
        return requestMockHttpServletRequestBuilder(tenantId, mockHttpServletRequestBuilder);
    }

    @SneakyThrows
    private MockHttpServletRequestBuilder uploadFileRequestMockHttpServletRequestBuilder(String url, String filePath, String fileName, String tenantId, String fileKey) {
        File file = new File(filePath);
        FileInputStream fileInputStream = new FileInputStream(file);
        MockMultipartFile multipartFile = new MockMultipartFile(fileKey, fileName, MediaType.MULTIPART_FORM_DATA_VALUE, fileInputStream);
        MockHttpServletRequestBuilder mockHttpServletRequestBuilder = MockMvcRequestBuilders
                .multipart(url)
//                .param("excel", multipartFile.getBytes().toString());
                .file(multipartFile)
                .contentType(MediaType.MULTIPART_FORM_DATA);
        return requestMockHttpServletRequestBuilder(tenantId, mockHttpServletRequestBuilder);
    }


    /**
     * 文件上传
     *
     * @param url
     * @return JAVA集合
     */
    public <T> List<T> fileRequestList(String url, String filePath, String fileName, String tenantId, Class<T> clazz, String fileKey) {

        try {
            MockHttpServletRequestBuilder authorization = uploadFileRequestMockHttpServletRequestBuilder(url, filePath, fileName, tenantId, fileKey);

            return getListData(clazz, authorization);
        } catch (Exception e) {
            log.error("POST单元测试url: {}异常,文件路径: {}", url, filePath);
        }
        return null;
    }

    /**
     * post请求，需要tenantId
     *
     * @param url
     * @return JAVA集合
     */
    public <T> List<T> postRequestList(String url, String json, String tenantId, Class<T> clazz) {

        try {
            MockHttpServletRequestBuilder authorization = postRequestMockHttpServletRequestBuilder(url, json, tenantId);

            return getListData(clazz, authorization);
        } catch (Exception e) {
            log.error("POST单元测试url: {}异常,入参: {}", url, json);
        }
        return null;
    }


    private MockHttpServletRequestBuilder requestMockHttpServletRequestBuilder(String tenantId, MockHttpServletRequestBuilder mockHttpServletRequestBuilder) {
        MockHttpServletRequestBuilder authorization;
        if (tenantId != null) {
            authorization = mockHttpServletRequestBuilder
                    .header("tenantId", tenantId);
        } else {
            authorization = mockHttpServletRequestBuilder
                    .header("authorization", token);
        }
        return authorization;
    }


    /**
     * 没有？拼接的get请求，需要token
     *
     * @param url
     * @return java object
     */
    public <T> T getRequestObject(String url, Class<T> clazz) {
        return getRequestObject(url, null, null, null, clazz);
    }

    public void getRequestDownload(String url, String filePath, String fileName) {

    }

    /**
     * 没有？拼接的get请求，需要token
     *
     * @param url
     * @return java list
     */
    public <T> List<T> getRequestList(String url, Class<T> clazz) {
        return getRequestList(url, null, null, null, clazz);
    }


    /**
     * 没有？拼接的get请求，需要tenantId
     *
     * @param url
     * @return java object
     */
    public <T> T getRequestObject(String url, String tenantId, Class<T> clazz) {
        return getRequestObject(url, null, null, tenantId, clazz);
    }

    /**
     * 没有？拼接的get请求，需要tenantId
     *
     * @param url
     * @return java list
     */
    public <T> List<T> getRequestList(String url, String tenantId, Class<T> clazz) {
        return getRequestList(url, null, null, tenantId, clazz);
    }


    /**
     * 有？拼接的get请求，不需要tenantId
     *
     * @param url
     * @return java object
     */
    public <T> T getRequestObject(String url, List<String> param, List<String> value, Class<T> clazz) {
        return getRequestObject(url, param, value, null, clazz);
    }

    /**
     * 有？拼接的get请求，不需要tenantId
     *
     * @param url
     * @return java list
     */
    public <T> List<T> getRequestList(String url, List<String> param, List<String> value, Class<T> clazz) {
        return getRequestList(url, param, value, null, clazz);
    }

    /**
     * get请求
     *
     * @param url
     */
    public <T> T getRequestObject(String url, List<String> params, List<String> values, String tenantId, Class<T> clazz) {

        try {
            MockHttpServletRequestBuilder authorization = getRequestMockHttpServletRequestBuilder(url, params, values, tenantId);

            return getObjectData(clazz, authorization);
        } catch (Exception e) {
            log.error("GET单元测试url: {} 异常,参数: {},入参: {}", url, params, values);
        }
        return null;
    }

    /**
     * get请求
     *
     * @param url
     */
    public <T> List<T> getRequestList(String url, List<String> params, List<String> values, String tenantId, Class<T> clazz) {
        try {
            MockHttpServletRequestBuilder authorization = getRequestMockHttpServletRequestBuilder(url, params, values, tenantId);

            return getListData(clazz, authorization);
        } catch (Exception e) {
            log.error("GET单元测试url: {} 异常,参数: {},入参: {}", url, params, values);
        }
        return null;
    }


    private MockHttpServletRequestBuilder getRequestMockHttpServletRequestBuilder(String url, List<String> params, List<String> values, String tenantId) {
        MockHttpServletRequestBuilder mockHttpServletRequestBuilder = MockMvcRequestBuilders.get(url);
        ;
        if (params != null && params != null) {
            for (int i = 0; i < params.size(); i++) {
                mockHttpServletRequestBuilder.param(params.get(i), values.get(i));
            }

        }

        return requestMockHttpServletRequestBuilder(tenantId, mockHttpServletRequestBuilder);
    }


    private <T> T getObjectData(Class<T> clazz, MockHttpServletRequestBuilder authorization) throws Exception {
        Object data = getWebData(authorization);
        if (clazz == null) {
            return null;
        }
        if (data instanceof JSONObject) {
            T t = ((JSONObject) data).toJavaObject(clazz);
            return t;
        }
        return null;
    }

    private <T> List<T> getListData(Class<T> clazz, MockHttpServletRequestBuilder authorization) throws Exception {
        Object data = getWebData(authorization);
        if (clazz == null) {
            return null;
        }
        if (data instanceof JSONArray) {
            List<T> ts = ((JSONArray) data).toJavaList(clazz);
            return ts;
        }
        return null;
    }


    private void downloadFile(MockHttpServletRequestBuilder authorization, String filePath) throws Exception {
        ResultActions resultActions = mockMvc.perform(authorization)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print());
        MvcResult mvcResult = resultActions.andReturn();
        byte[] contentAsByteArray = mvcResult.getResponse().getContentAsByteArray();
        String header = mvcResult.getResponse().getHeader("Content-disposition");
        if (filePath != null) {
            String[] split = header.split("filename=");
            String fileName = split[split.length - 1];
            fileName = URLDecoder.decode(fileName, "UTF-8");
            FileOutputStream fileOutputStream = new FileOutputStream(filePath + "\\" + fileName);
            fileOutputStream.write(contentAsByteArray);
            fileOutputStream.close();

        }
    }


    private Object getWebData(MockHttpServletRequestBuilder authorization) throws Exception {
        ResultActions resultActions = mockMvc.perform(authorization)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print());
        MvcResult mvcResult = resultActions.andReturn();
        byte[] contentAsByteArray = mvcResult.getResponse().getContentAsByteArray();
        String jsonString = new String(contentAsByteArray);
        WebResponse webResponse = JSON.parseObject(jsonString, WebResponse.class);
        log.info("返回结果：{}", webResponse.getData());
        return webResponse.getData();
    }

    /**
     * 设置mockMvc，单元测试前置工作，无需登录时
     */
    public void setupMockMvc() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        mockHttpServletRequest = new MockHttpServletRequest(webApplicationContext.getServletContext());
        mockHttpServletResponse = new MockHttpServletResponse();
        mockHttpSession = new MockHttpSession(webApplicationContext.getServletContext());
        mockHttpServletRequest.setSession(mockHttpSession);
        SecurityUtils.setSecurityManager(shiroFilterFactoryBean.getSecurityManager());
    }


    /**
     * 单元测试前置工作，需要登录时
     *
     * @param loginUrl
     */
    public void beforeDoTestForToken(Object object, String loginUrl) {
        setupMockMvc();
        LoginTokenVO loginTokenVO = testLogin(object, loginUrl);
        this.token = loginTokenVO.getToken();
        this.refreshToken = loginTokenVO.getRefreshToken();
        shiroLogin();
        FeignSessionContext.setAuthorization(token);
    }

    public String getToken() {
        return this.token;
    }

    public String getRefreshToken() {
        return this.refreshToken;
    }

}
