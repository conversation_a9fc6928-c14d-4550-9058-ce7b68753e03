package com.knx.survey.common.util.excelUtil.pojo;

import lombok.Builder;
import lombok.Data;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.xssf.usermodel.XSSFColor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OneCellStyle.java
 * @Description TODO
 * @createTime 2022年09月08日 10:22:00
 */
@Data
@Builder
public class OneCellStyle {

    private Integer rowIndex;

    private Integer columnIndex;

    //倾斜
    private Boolean italic;

    //加粗
    private Boolean bold;

    private XSSFColor bColor;

    private HorizontalAlignment horizontalAlignment;

    private Integer height;

    private Integer width;

    private Integer fontSize;

    private String fontName;

    private XSSFColor fontColor;

    private Boolean isShowBorder = true;

    private CustomCellStyle customCellStyle;

    private boolean isHead = false;


}
