<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.knx.bean.dao.mapper.KnxCardMapper">
    <select id="pageList" resultType="com.knx.bean.model.entity.KnxCard">
        select * from knx_card
        <where>
            1=1
            <if test="cardSelect.isExpired !=null and cardSelect.isExpired">
                and to_char(expire_date,'yyyy-MM-dd') &lt; #{cardSelect.today}
            </if>
            <if test="cardSelect.isExpired !=null and !cardSelect.isExpired">
                and to_char(expire_date,'yyyy-MM-dd') &gt;= #{cardSelect.today}
            </if>
            <if test="cardSelect.isUsed !=null">
                and is_used = #{cardSelect.isUsed}
            </if>
            <if test="cardSelect.createByIds !=null">
                and create_by in
                <foreach collection="cardSelect.createByIds" item="createId" open="(" close=")" separator=",">
                    #{createId}
                </foreach>
            </if>
            <if test="cardSelect.cardType !=null">
                and card_type = #{cardSelect.cardType}
            </if>
            <if test="cardSelect.tenantIds !=null">
                and tenant_id in
                <foreach collection="cardSelect.tenantIds" item="tenantId" open="(" close=")" separator=",">
                    #{tenantId}
                </foreach>
            </if>
            <if test="cardSelect.startCreateDate !=null">
                and to_char(create_time,'yyyy-MM-dd') &gt;= #{cardSelect.startCreateDateString}
            </if>
            <if test="cardSelect.endCreateDate !=null">
                and to_char(create_time,'yyyy-MM-dd') &lt;= #{cardSelect.endCreateDateString}
            </if>

        </where>
    </select>


    <select id="listKnxCard" resultType="com.knx.bean.model.entity.KnxCard">
        select * from knx_card
        <where>
            1=1
            <if test="cardSelect.isExpired !=null and cardSelect.isExpired">
                and to_char(expire_date,'yyyy-MM-dd') &lt; #{cardSelect.today}
            </if>
            <if test="cardSelect.isExpired !=null and !cardSelect.isExpired">
                and to_char(expire_date,'yyyy-MM-dd') &gt;= #{cardSelect.today}
            </if>
            <if test="cardSelect.isUsed !=null">
                and is_used = #{cardSelect.isUsed}
            </if>
            <if test="cardSelect.createByIds !=null">
                and create_by in
                <foreach collection="cardSelect.createByIds" item="createId" open="(" close=")" separator=",">
                    #{createId}
                </foreach>
            </if>
            <if test="cardSelect.cardType !=null">
                and card_type = #{cardSelect.cardType}
            </if>
            <if test="cardSelect.tenantIds !=null">
                and tenant_id in
                <foreach collection="cardSelect.tenantIds" item="tenantId" open="(" close=")" separator=",">
                    #{tenantId}
                </foreach>
            </if>
            <if test="cardSelect.startCreateDate !=null">
                and to_char(create_time,'yyyy-MM-dd') &gt;= #{cardSelect.startCreateDateString}
            </if>
            <if test="cardSelect.endCreateDate !=null">
                and to_char(create_time,'yyyy-MM-dd') &lt;= #{cardSelect.endCreateDateString}
            </if>

        </where>
    </select>

    <update id="updateBatchByPassword">
        <foreach collection="knxCards" item="knxCard" separator=";">
            update knx_card
            <set>
                <if test="knxCard.isUsed!=null">
                    is_used = #{knxCard.isUsed},
                </if>

                <if test="knxCard.useDate!=null">
                    use_date = #{knxCard.useDate},
                </if>

            </set>
            where card_password=#{knxCard.cardPassword}
        </foreach>
    </update>

    <update id="updateRechargeBatchByPassword">
        <foreach collection="knxCards" item="knxCard" separator=";">
            update knx_card
            <set>

                <if test="knxCard.isRecharge!=null">
                    is_recharge = #{knxCard.isRecharge},
                </if>
                <if test="knxCard.rechargeDate!=null">
                    recharge_date = #{knxCard.rechargeDate},
                </if>
                <if test="knxCard.tenantId!=null">
                    tenant_id= #{knxCard.tenantId},
                </if>
            </set>
            where card_password=#{knxCard.cardPassword}
        </foreach>
    </update>

</mapper>