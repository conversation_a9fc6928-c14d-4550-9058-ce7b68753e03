package com.knx.survey.common.util;

import com.knx.common.util.RegexUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SurveyRegexUtils extends RegexUtils {

    private static String mobileRegex = "^1(\\d){10}?$";

    public static boolean validateEmail(String email) {
        return checkEmail(email);
    }

    public static boolean validateMobile(String mobile) {
        try {
            Pattern pattern = Pattern.compile(mobileRegex);
            Matcher matcher = pattern.matcher(mobile);
            return matcher.matches();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public static boolean isLetterDigit(String str) {
        String regex = "^[a-z0-9A-Z]+$";
        return str.matches(regex);
    }

    public static boolean validateOptionId(String str) {
        String regex = "^\\d+$";
        return str.matches(regex);
    }

    /**
     * 校验字符串是否为字母数字加常见符号（允许的符号包括：_ - @ .）
     */
    public static boolean validateAlphaNumericWithCommonSymbols(String input) {
        if (input == null) return true;
        return input.matches("^[a-zA-Z0-9_\\-@.]+$");
    }

    public static void main(String[] args) {
        System.out.println(validateMobile("111231"));
        System.out.println(validateMobile("12345678901"));
        System.out.println(validateMobile("82345678901"));
        System.out.println(isLetterDigit("asDFs8712"));
        System.out.println(isLetterDigit("asas8712-"));
        System.out.println(isLetterDigit("asF8712好好"));

        System.out.println(validateOptionId("as90"));
        System.out.println(validateOptionId("1"));
        System.out.println(validateOptionId("0"));
        System.out.println(validateOptionId("212131"));
    }
}
