package com.knx.bean.model.entity;


import com.knx.bean.model.enums.KnxBeanAccountStatusEnum;
import com.knx.common.base.model.BaseTenantModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 每个租户的肯豆账户
 */
@Data
@ApiModel(value = "KnxBeanAccount")
@FieldNameConstants
public class KnxBeanAccount extends BaseTenantModel {

    /**
     * 余额这里是参考作用, 每次 Transaction 那边有交易,同步更新这里
     */
    @ApiModelProperty(value = "肯豆余额")
    private BigDecimal balance;

    /**
     * 肯豆账户状态
     */
    @ApiModelProperty(value = "肯豆账户状态")
    private KnxBeanAccountStatusEnum status;

    /**
     * 肯豆账户过期时间 (账号有到期不能使用)
     */
    @ApiModelProperty(value = "肯豆账户过期时间")
    private Date expiredDate;

    /**
     * 消息肯豆余额（包含短信，邮箱等消息发送）
     */
    @ApiModelProperty(value ="消息肯豆余额")
    private BigDecimal messageBalance;
}
