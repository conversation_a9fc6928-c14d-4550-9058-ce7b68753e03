package com.knx.survey.common.base.model;

import com.knx.common.base.service.exception.BusinessException;
import com.knx.common.base.service.exception.ErrorCode;
import com.knx.survey.common.util.IDUtil;
import lombok.Data;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 服务实例信息
 */
@Data
public class PodInstance {

    /**
     * 服务名称
     */
    private static String serviceName = "unknown";

    /**
     * 主机名称
     */
    private static String hostName = "unknown";

    /**
     * 主机ip地址
     */
    private static String hostAddress = "unknown";

    /**
     * 端口号
     */
    private static String port = "unknown";

    /**
     *  应用进程ID
     */
    private static String pid = "unknown";

    /**
     *  uuid
     */
    private static final String uuid = IDUtil.getSimpleUUID();

    /**
     * 判断是否初始化成功
     *
     * @return
     */
    public static boolean isInitSuccess() {
        if (getInstance().contains("unknown")) {
            return false;
        }
        return true;
    }

    /**
     * 初始化实例信息
     *
     * @param serviceName2
     * @param hostName2
     * @param hostAddress2
     * @param port2
     * @param pid2
     */
    public static void init(String serviceName2, String hostName2, String hostAddress2, String port2, String pid2) throws Exception {
        List<String> argList = new ArrayList<>();
        argList.add(serviceName2);
        argList.add(hostName2);
        argList.add(hostAddress2);
        argList.add(port2);
        argList.add(pid2);
        if (argList.stream().anyMatch(n -> StringUtils.isBlank(n) || Objects.equals(n, "unknown"))) {
            throw new BusinessException(String.format("init pod instance fail list=%s", argList), ErrorCode.FAIL);
        }
        serviceName = serviceName2;
        hostName = hostName2;
        hostAddress = hostAddress2;
        port = port2;
        pid = pid2;
    }

    /**
     * 获取服务名称
     *
     * @return
     */
    public static String getServiceName() {
        return serviceName;
    }

    /**
     * 获取实例信息
     *
     * @return
     */
    public static String getInstance() {
        return serviceName + "#" + hostAddress + ":" + port + "#" + pid + "#" + uuid;
    }

    public static void main(String[] args) {
        System.out.println(PodInstance.getInstance());;
    }
}
