DROP TABLE IF EXISTS platform_announcement;
CREATE TABLE platform_announcement
(
    "id"                              VARCHAR(36)  NOT NULL,
    "content"                         TEXT NOT NULL,
	"start_time"                      timestamp(3) NOT NULL,
	"end_time"                        timestamp(3) NOT NULL,
    "scope_type"                      VARCHAR(50) NOT NULL DEFAULT 'ALL',
    "is_expired"                      BOOL DEFAULT false,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___platform_announcement___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."platform_announcement" IS '平台公告表';
COMMENT ON COLUMN "public"."platform_announcement"."content" IS '内容';
COMMENT ON COLUMN "public"."platform_announcement"."start_time" IS '开始时间';
COMMENT ON COLUMN "public"."platform_announcement"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."platform_announcement"."scope_type" IS '范围（全部租户、指定租户）';
COMMENT ON COLUMN "public"."platform_announcement"."is_expired" IS '是否截止（手动截止）';

