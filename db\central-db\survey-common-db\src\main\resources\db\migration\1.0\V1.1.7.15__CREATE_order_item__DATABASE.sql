DROP TABLE IF EXISTS "public"."order_item";
CREATE TABLE "public"."order_item" (
   "id" varchar(36) COLLATE "pg_catalog"."default" NOT NULL,
   "order_id" varchar(36) COLLATE "pg_catalog"."default",
   "type" varchar(50) COLLATE "pg_catalog"."default",
   "report_style" varchar(50) COLLATE "pg_catalog"."default",
   "employee_level" varchar(50) COLLATE "pg_catalog"."default",
   "num" numeric(11,0),
   "price" numeric(11,2),
   "total_amount" numeric(11,2),
   "create_by" varchar(36) COLLATE "pg_catalog"."default",
   "create_time" timestamp(3),
   "update_time" timestamp(3),
   "last_update_by" varchar(36) COLLATE "pg_catalog"."default",
   "is_deleted" bool,
   "app_id" varchar(36) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."order_item"."id" IS '主键';
COMMENT ON COLUMN "public"."order_item"."order_id" IS '订单ID';
COMMENT ON COLUMN "public"."order_item"."type" IS '类型';
COMMENT ON COLUMN "public"."order_item"."report_style" IS '报告风格';
COMMENT ON COLUMN "public"."order_item"."employee_level" IS '员工层级';
COMMENT ON COLUMN "public"."order_item"."num" IS '数量';
COMMENT ON COLUMN "public"."order_item"."price" IS '价格';
COMMENT ON COLUMN "public"."order_item"."total_amount" IS '总金额';
COMMENT ON TABLE "public"."order_item" IS '订单明细表';

ALTER TABLE "public"."order_item" ADD CONSTRAINT "order_item_pkey" PRIMARY KEY ("id");
CREATE INDEX ___idx_order_item_order_id___ ON public.order_item(order_id);
