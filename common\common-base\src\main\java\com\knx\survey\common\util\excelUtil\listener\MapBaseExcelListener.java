package com.knx.survey.common.util.excelUtil.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelAnalysisException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2021/4/9 15:31
 */

@Slf4j
public class MapBaseExcelListener extends BaseExcelListener<Map<Integer, String>> {

    @Getter
    private List<Map<Integer, String>> mapData = new ArrayList<>();


    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        mapData.add(0, headMap);
    }

    @Override
    public void invoke(Map<Integer, String> map, AnalysisContext context) {
        String sheetName = context.readSheetHolder().getSheetName();
        log.info(sheetName + ":解析到一条数据:{}", map);
        if (!validateBeforeAddData(map)) {
            throw new ExcelAnalysisException("数据校验不合法!");
        }
        // 数据存储到list，供批量处理，或后续自己业务逻辑处理。
        if (isAllValueIsNull(map)) {
            return;
        }
        mapData.add(map);
    }

    private boolean isAllValueIsNull(Map<Integer, String> object) {
        boolean isAllValueIsNull = true;
        for (Map.Entry<Integer, String> entry : object.entrySet()) {
            if (entry.getValue() != null) {
                isAllValueIsNull = false;
                break;
            }
        }
        return isAllValueIsNull;
    }

    @Override
    public boolean validateBeforeAddData(Map<Integer, String> object) {
        return true;
    }
}
