DROP TABLE IF EXISTS platform_announcement_tenant_mapping;
CREATE TABLE platform_announcement_tenant_mapping
(
    "id"                              VARCHAR(36)  NOT NULL,
    "platform_announcement_id"        VARCHAR(36)  NOT NULL,
    "tenant_id"                       VARCHAR(36)  NOT NULL,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___platform_announcement_tenant_mapping___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."platform_announcement_tenant_mapping" IS '平台公告租户关系表（指定租户）';
COMMENT ON COLUMN "public"."platform_announcement_tenant_mapping"."platform_announcement_id" IS '平台公告ID';
COMMENT ON COLUMN "public"."platform_announcement_tenant_mapping"."tenant_id" IS '租户ID';

CREATE INDEX ___idx_platform_announcement_tenant_mapping_platform_announcement_id___ ON public.platform_announcement_tenant_mapping(platform_announcement_id);
CREATE INDEX ___idx_platform_announcement_tenant_mapping_tenant_id___ ON public.platform_announcement_tenant_mapping(tenant_id);