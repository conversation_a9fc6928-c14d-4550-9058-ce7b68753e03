package com.knx.survey.common.base.field;

import com.knx.survey.common.util.I18NStringUtil;
import com.knx.survey.model.I18NString;
import com.knx.survey.model.SagI18NString;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Package com.knx.survey.service.field
 * @date 2020/6/17 14:59
 */
public class SurveyField extends SagField {

    public static final Integer INTEGER_VALUE_ZERO = 0;
    public static final Integer INTEGER_VALUE_ONE = 1;
    public static final Integer INTEGER_VALUE_TWO = 2;
    public static final Integer INTEGER_VALUE_THREE = 3;
    public static final Integer INTEGER_VALUE_NEGATIVE_ONE = -1;
    public static final Integer INTEGER_VALUE_1024 = 1024;
    public static final Integer INTEGER_VALUE_100 = 100;

    public static final Double DEFAULT_ROLE_WEIGHT = 1D;

    public static final int FILE_SIZE_LIMIT = 240;

    public static final int FILE_SIZE_LIMIT_220 = 220;

    public static final String STRING_VALUE_ZERO = "0";
    public static final String STRING_VALUE_NEGATIVE_ONE = "-1";

    public static final String STRING_VALUE_NEGATIVE_TWO = "-2";

    public static final String SYMBOL_AND = "&";
    public static final String EQUAL = "=";
    public static final String STRING_TENANTID = "tenantid";
    public static final String STRING_TENANTID_EQUAL = STRING_TENANTID + "=";
    public static final String STRING_PROJECTID = "projectid";
    public static final String STRING_PROJECTID_EQUAL = STRING_PROJECTID + "=";
    public static final String STRING_PERSONID = "personid";
    public static final String STRING_PERSONID_EQUAL = STRING_PERSONID + "=";
    public static final String STRING_INVESTIGATOR_ID = "investigatorId";
    public static final String STRING_INVESTIGATOR_ID_EQUAL = STRING_INVESTIGATOR_ID + "=";

    public static final String STRING_AUDIT_PERSON_ID = "auditPersonId";
    public static final String STRING_AUDIT_PERSON_ID_EQUAL = STRING_AUDIT_PERSON_ID + "=";

    public static final String STRING_ANSWERCODETYPE = "answerCodeType";
    public static final String STRING_ANSWERCODETYPE_EQUAL = STRING_ANSWERCODETYPE + "=";
    public static final String PRISMA_REPORT_DATA_ID = "prismaReportDataId";
    public static final String QUESTION_ID = "questionId";
    public static final String TENANT_ID = "tenantId";
    public static final String CONDITION_QUESTION_ID = "conditionQuestionId";

    public static final String OPTION_ID = "optionId";

    public static final String PROJECT_INFO = "projectInfo";
    public static final String SURVEY_QUESTIONNAIRE_PERSON_MAPPINGS = "surveyQuestionnairePersonMappings";
    public static final String LAST_SURVEY_ANSWER = "lastSurveyAnswer";


    public static final String BEAN_NAME_ONE_HOUR_CACHE_MANAGER = "oneHourCacheManager";
    public static final String BEAN_NAME_ONE_DAY_CACHE_MANAGER = "oneDayCacheManager";
    public static final String BEAN_NAME_SEVEN_DAY_CACHE_MANAGER = "sevenDayCacheManager";
    public static final String BEAN_NAME_TEN_MINUTE_CACHE_MANAGER = "tenMinuteCacheManager";

    public static final String FILE_URL_PREFIX = "/file/www/";

    public static final String START_DATE = "{{开始日期}}";
    public static final String END_DATE = "{{结束日期}}";
    public static final String LINK = "{{链接}}";
    public static final String CAPTCHA = "{{验证码}}";
    public static final String QR_CODE = "{{登录二维码}}";
    public static final String PERSON_NAME = "{{姓名}}";
    public static final String PROJECT_NAME_REX = "{{活动名称}}";

    public static final String ANSWER_FINISH_NO = "{{完成序号}}";
    public static final String ANSWER_FINISH_DATE = "{{完成日期}}";

    public static final String PROJECT_NAME_EN = "{{activity name}}";

    public static final String ANSWER_FINISH_NO_EN = "{{completion number}}";
    public static final String ANSWER_FINISH_DATE_EN = "{{completion date}}";

    public static final String PERSON = "PERSON";
    public static final String QUESTION_MARK = "?";
    public static final String UNDER_LINE = "_";

    public static final String PLUS = "+";

    public static final String INVESTIGATOR_TITLE = "{{sag#360roleName}}";

    public static final String LEFT_PARENTHESIS = "（";
    public static final String RIGHT_PARENTHESIS = "）";

    public static final String SEMICOLON=";";

    public static final String ELLIPSIS="...";

    public static final String BUSINESS_RELATED = "业务相关";
    public static final String SUPERIOR = "上级";
    public static final String SUBORDINATE = "下级";
    public static final String SAME_LEVEL = "同级";
    public static final String COLLEAGUE = "同事";
    public static final String MYSELF = "本人";


    public static final String YSLD_DEPARTMENT_PARENT = "Brand/Department";
    public static final String YSLD_DEPARTMENT = "Department";
    public static final String YSLD_BRANCH = "Branch";
    public static final String YSLD_FUNCTION = "Function";

    public static final String MATRIX_MANAGER = "Matrix Manager";
    public static final String DIRECT_REPORTS = "Direct Reports";
    public static final String PEERS = "Peers";
    public static final String CROSS_FUNCTIONAL_PARTNERS = "Cross functional partners";
    public static final String MATRIX_REPORTS = "Matrix Reports";
    public static final String CADRE = "干部";
    public static final String FIRST_LEVEL_CADRE = "一级干部";
    public static final String SECOND_LEVEL_CADRE = "二级干部";

    public static final String SYSTEM = "体系";

    public static final String ORGANIZATION = "组织";

    public static final String SUPERIOR_EN = "Superior";
    public static final String SUBORDINATE_EN = "Subordinate";
    public static final String SAME_LEVEL_EN = "Peer";
    public static final String COLLEAGUE_EN = "Colleague";
    public static final String MYSELF_EN = "Self";


    public static final String PERCENT_SIGN = "%";

    public static final String EXPLANATION = "解释";

    public static final String DEMO_ONE = "评测关系表";

    public static final String DEMO_TWO = "360°行为反馈是一个促进员工自我认知和自我发展、推动员工能力开发的工具。它最大的价值在于帮助个体更加清晰地了解自己眼中的自己与他人眼中的自己，从而明确个人优劣势与发展需求，改善自己的职业发展路径。评测关系设计是否恰当合理直接影响到360°反馈调查能否实现其应有的价值。在评测开始前，我们希望由作为调查对象的上级（们）为他们推荐被评估人，然后交由HR做最后的汇总，并与我方一同做最终的审核与确定。";

    public static final String DEMO_THREE = "请本项目HR负责人协助调查对象的上级（们）参照最新组织架构图和以下原则为每位调查对象推荐合适的被评估人。为了保证本次调研结果的有效性，我们希望您能对本列表内的信息保密。";

    public static final String DEMO_FOUR = "关系原则：";

    public static final String DEMO_FIVE = "1. 入职超过3个月的员工，可作为被评估人。入职时间小于3个月的员工不适宜作为被评估人。";

    public static final String DEMO_SIX = "2. 熟悉被评估人的日常工作内容或与调查对象在工作有较频繁合作，可作为评测者。因此评测者主体应该来自于本部门和/或部门外的业务上联系比较密切者。";

    public static final String DEMO_SEVEN = "3. 角色：";

    public static final String DEMO_EIGHT = "    自评：调查对象自己";

    public static final String DEMO_NINE = "   上级：调查对象的直接汇报上级（1-2人）";

    public static final String DEMO_TEN = "   同级：调查对象合作紧密的同级 （3-4人）";

    public static final String DEMO_ELEVEN = "   下级：调查对象的直接下级（3-4人";

    public static final String DEMO_TWELVE = "填写要求：";

    public static final String DEMO_THIRTEEN = "1. 务必保证调查关系的正确，如需要一人同时调查同名同姓的员工，建议对称呼能有区分";

    public static final String DEMO_FOURTEEN = "2. 务必保证邮箱地址的正确，邮箱地址不能带空格，且不能从outlook等直接导出，不接受的格式如“andyxu<<EMAIL>>”";

    public static final String DEMO_FIFTEEN = "3. 角色默认为本人、上级、下级、同级。如调整，请确保与页面“角色管理”内容一致。否则上传失败。";

    public static final String DEMO_SIXTEEN = "";

    public static final String DEMO_SEVENTEEN = "填写样例：";

    public static final String DEMO_EIGHTEEN = "评测者";

    public static final String DEMO_NINETEEN_ZERO = "评估人编码(必填)";
    public static final String DEMO_NINETEEN_ONE = "评估人姓名(必填)";

    public static final String DEMO_NINETEEN_TWO = "评估人邮箱(必填)";

    public static final String DEMO_NINETEEN_THREE = "评估人手机(非必填)";

    public static final String DEMO_NINETEEN_FOUR = "评估人角色(必填)";

    public static final String DEMO_NINETEEN_FIVE = "在当前角色中的比重(必填)";

    public static final String DEMO_NINETEEN_SIX = "被评估人编码(必填)";

    public static final String DEMO_TWENTY_ZERO = "demo001";
    public static final String DEMO_TWENTY_ONE = "Jack Xu";

    public static final String DEMO_TWENTY_TWO = "<EMAIL>";

    public static final String DEMO_TWENTY_THREE = "13000000000";

    public static final String DEMO_TWENTY_FOUR = "上级";

    public static final String DEMO_TWENTY_FIVE = "2";

    public static final String DEMO_TWENTY_SIX = "demo003";

    public static final String DEMO_TWENTYONE_ZERO = "demo002";
    public static final String DEMO_TWENTYONE_ONE = "Jack Xu001";

    public static final String DEMO_TWENTYONE_TWO = "<EMAIL>";

    public static final String DEMO_TWENTYONE_THREE = "13000000001";

    public static final String DEMO_TWENTYONE_FOUR = "下级";

    public static final String DEMO_TWENTYONE_FIVE = "2 ";

    public static final String DEMO_TWENTYONE_SIX = "demo003";

    public static final String DEMO_TWENTYTHREE_ZERO = "demo003";
    public static final String DEMO_TWENTYTHREE_ONE = "Jack Xu002";

    public static final String DEMO_TWENTYTHREE_TWO = "<EMAIL>";

    public static final String DEMO_TWENTYTHREE_THREE = "13000000002";

    public static final String DEMO_TWENTYTHREE_FOUR = "同级";

    public static final String DEMO_TWENTYTHREE_FIVE = "1";

    public static final String DEMO_TWENTYTHREE_SIX = "demo003";

    public static final String DEMO_TWENTYTWO = "被评估人";

    public static final String DEMO_TWENTYFOUR_ZERO = "被评估人编码(必填)";
    public static final String DEMO_TWENTYFOUR_ONE = "被评估人姓名(必填)";

    public static final String DEMO_TWENTYFOUR_TWO = "被评估人邮箱(必填)";

    public static final String DEMO_TWENTYFOUR_THREE = "被评估人手机(非必填)";

    public static final String DEMO_TWENTYFOUR_FOUR = "是否自评(必填)";

    public static final String DEMO_TWENTYFOUR_FIVE = "角色";

    public static final String DEMO_TWENTYFOUR_SIX = "比重";

    public static final String DEMO_TWENTYFIVE_ZERO = "demo004";
    public static final String DEMO_TWENTYFIVE_ONE = "Yi Fan";

    public static final String DEMO_TWENTYFIVE_TWO = "<EMAIL>";

    public static final String DEMO_TWENTYFIVE_THREE = "13000000004";

    public static final String DEMO_TWENTYFIVE_FOUR = "是";

    public static final String DEMO_TWENTYFIVE_FIVE = "上级";

    public static final String DEMO_TWENTYFIVE_SIX = "4";

    public static final String DEMO_TWENTYSIX_ZERO = "demo005";
    public static final String DEMO_TWENTYSIX_ONE = "San Zhang";

    public static final String DEMO_TWENTYSIX_TWO = "<EMAIL>";

    public static final String DEMO_TWENTYSIX_THREE = "13000000005";

    public static final String DEMO_TWENTYSIX_FOUR = "是";

    public static final String DEMO_TWENTYSIX_FIVE = "下级";

    public static final String DEMO_TWENTYSIX_SIX = "2";

    public static final String DEMO_TWENTYSEVEN = "填写说明：";

    public static final String DEMO_TWENTYEIGHT = "1. 评估人/被评估人表中务必保证被评估人编码一致。编码重复时，视为同一人。";

    public static final String DEMO_TWENTYNINE = "2. 被评估人表中可根据不同角色设置不同比重.如同一个被评估人含多个相同角色，比重将累计增加。";

    public static final String SAG = "sag";

    public static final String COLON = ":";

    public static final String COLON_ZH = "：";

    public static final String PRISMA_DEFAULT_CODE = "KFSHD-prisma";

    public static final String CUSTOMIZE = "CUSTOMIZE";

    public final static int PRISMA_IN_GROUP_SIZE = 5000;

    public final static int PRISMA_MIN_CALCULATE_SIZE = 5;

    public final static String DEFAULT_LANGUAGE = "zh_CN";

    public final static String ZH_LANGUAGE = "zh_CN";

    public final static String EN_LANGUAGE = "en_US";

    public final static String OPTIONAL_LANGUAGE = "zh_CN,en_US";

    public static final String COPY = "复制";

    public static final String COPY_EN = "COPY";

    public static final String HYPHEN = "-";

    public static final String KFSHD_DIMENSION_CODE = "KFSHD-prisma076-077";

    public static final String DIRECTORY_CONFIG = "directoryConfig";

    public static final String ERROR_MESSAGE_CODE_PREFIX = "STATUS_CODE_";

    public static final String COPY_FROM_COMMON = "COMMON";
    public static final String COPY_FROM_TENANT = "TENANT";

    public static final Integer PERSON_SHEET_NO_1 = new Integer(1);
    public static final Integer PERSON_SHEET_NO_2 = new Integer(2);
    public static final Integer PERSON_SHEET_NO_3 = new Integer(3);
    public static final Integer PERSON_HEAD_LINE_MUN_1 = new Integer(1);
    public static final Integer PERSON_HEAD_LINE_MUN_2 = new Integer(2);
    public static final Integer PERSON_HEAD_LINE_MUN_3 = new Integer(3);

    public static final String ADD_COUNT = "addCount";
    public static final Integer MAXIMUM_NUMBER_OF_FAILURES = 4;

    public static final String ABILITY_ASSESSMENT_QUESTIONNAIRE = "TZTZ-EAA-AT";

    public static final String PARENT_ID_DEFAULE_VALUE = "0";

    public static final String DESCRIPTION0 = "低水平";
    public static final String DESCRIPTION1 = "中水平";
    public static final String DESCRIPTION2 = "高水平";

    public static final String REPORT_STYLE_ONE = "答案相同";
    public static final String REPORT_STYLE_TWO = "严苛";
    public static final String REPORT_STYLE_THREE = "无态度";
    public static final String REPORT_STYLE_FOUR = "宽松";
    public static final String REPORT_STYLE_FIVE = "正常";
    public static final String ONE_STRING = "1";
    public static final String TWO_STRING = "2";
    public static final String THREE_STRING = "3";
    public static final String FOUR_STRING = "4";
    public static final String FIVE_STRING = "5";
    public static final String SIX_STRING = "6";
    public static final String SEVEN_STRING = "7";
    public static final String EIGHT_STRING = "8";
    public static final String NINE_STRING = "9";

    public static final Integer FIT_SCORE_NO_0 = 35;
    public static final Integer FIT_SCORE_NO_1 = 75;

    public static final String MAIL = "MAIL";
    public static final String EMAIL = "邮箱";
    public static final String PHONE = "手机";
    public static final String NAME = "姓名";
    public static final String WX_WORK_USER_ID = "企业微信用户账户";

    public static final String DING_ACCOUNT = "钉钉用户账户";
    public static final String FEISHU_ACCOUNT = "飞书用户账户";
    public static final String DEPARTMENT_CODE = "部门编码";
    public static final String PERSON_CODE = "人员编码";
    public static final String DEPARTMENT_NAME = "部门名称";
    public static final String LANGUAGE = "语言";
    public static final String MANAGER = "是否管理者";
    public static final String LANGUAGE_EN = "language";
    public static final String EMAIL_TEMPLATE = "邮件模板";
    public static final String SMS_TEMPLATE = "短信模板";
    public static final String KEY = "关键字";
    public static final String STATUS = "填答状态";
    public static final String ANSWER_VALID = "有效填答";
    public static final String INVITE_TIME = "邀请时间";
    public static final String INVITE_URL = "邀请链接";
    public static final String INVITE_ANSWER_URL = "完善关系链接";
    public static final String PERSON_CAPTCHA = "人员验证码";
    public static final String INVITE_CAPTCHA = "邀请验证码";
    public static final String TENANT_NAME = "租户名称";
    public static final String PROJECT_NAME = "活动名称";
    public static final String QUESTIONNAIRE_NAME = "问卷名称";
    public static final String ANSWER_LOTTERY_DRAW_NAME = "是否抽奖";

    public static final String ANSWER_FINISH_NO_NAME = "作答完成序号";

    public static final String REGULATION = "-";

    public static final Double ONE_DOUBLE = new Double(1.00);

    public static final Double TOW_DOUBLE = new Double(2.00);

    public static final Double FIVE_POINT_FIVE_DOUBLE = new Double(5.50);

    public static final Double TEN_DOUBLE = new Double(10.00);

    public static final Double THIRTY_DOUBLE = new Double(30.00);

    public static final String REPORT_PERSON = "报告人员";

    public static final String SAMPLE_TEST = "sample test";

    public static final String INVITATION_TEMPLATE = "评价关系模板";

    public static final String EVALUATOR = "评估人（评价人）";

    public static final String INVESTIGATOR = "被评估人（被评人）";

    public static final String INVESTIGATOR_DEMO = "样例";

    public static final String EVENT_STAFF_INFORMATION_TABLE = "活动人员信息表";

    public static final String EMAIL_LINK = "邮箱填答链接";

    public static final String PARTICIPATE_IN = "CYX-9L-MCA-209";
    public static final String COACH = "JLX-9L-MCA-210";
    public static final String AUTHORIZATION = "SQX-9L-MCA-211";
    public static final String GUIDANCE = "ZDX-9L-MCA-212";

    public static final String OPEN_UP = "KTZ-9L-MCA-213";
    public static final String STEER = "ZDZ-9L-MCA-214";
    public static final String SUPPORT = "YHZ-9L-MCA-215";
    public static final String CONSERVATIVE = "BSZ-9L-MCA-216";

    public static final String GROUP_STRING = "GROUP";

    public static final String _360 = "360";

    public static final String C = "C";
    public static final String D = "D";
    public static final String I = "I";
    public static final String S = "S";

    public static final String COMPLIANCE = "服从(C)";
    public static final String DOMINANCE = "支配(D)";
    public static final String INFLUENCE = "影响(I)";
    public static final String STEADINESS = "稳健(S)";

    public static final String BALANCE = "平衡";

    public static final String SLASH = "/";


    public static final String TOTAL_SCORE = "总分";

    public static final String EFFECTIVE_NUMBER = "有效人数";

    public static final String ASSESSMENT = "ASSESSMENT";
    public static final String ORIGINAL_ANSWER = "原始答案";
    public static final String DEPARTMENT = "部门";
    public static final String PERSONNEL = "人员";
    public static final String DIMENSION = "维度";
    public static final String DIMENSION_EN = "Dimension";
    public static final String CORRELATION_COEFFICIENT = "相关系数";
    public static final String PERCENTAGE_OF_PEOPLE = "人数百分比";
    public static final String PERCENTAGE_OF_APPROVAL = "赞成百分比";
    public static final String NEUTRAL_PERCENTAGE = "中立百分比";
    public static final String NOT_PERCENTAGE_OF_APPROVAL = "不赞成百分比";
    public static final String SUB_SYMBOL = "▲";
    public static final String COMPANY = "公司";
    public static final String NORM = "常模";
    public static final String IS_THE_LOWEST_SCORE = "是否最低分";
    public static final String SCORE = "得分";
    public static final String RANK = "排名";

    public static final String TOTAL_NUM = "总人数";
    public static final String RANK_EN = "rank";
    public static final String DYKXD = "DYKXD-prisma074-075";
    public static final String YES = "是";
    public static final String NO = "否";

    public static final String COMMA = ",";

    public static final String ANSWER_SPILT_CHAR = "**";

    public static final String CHILD_ANSWER_SPILT_CHAR = "&&";

    public static final String REGEX_ANSWER_SPILT_CHAR = "\\*\\*";

    public static final String DEMOGRAPHIC_ROOT_COL_NAMES = "c1,c2,c3,c4,c5,c6,c7,c8";

    public static final String CUSTOM_QUESTION_TEMPLATE = "自定义题目模板";
    public static final String QUESTION = "题目";
    public static final String STANDARD_QUESTION = "标准题目";
    public static final String OPTION = "选项";
    public static final String SECONDARY_DIMENSION = "二级维度";
    public static final String FIRST_LEVEL_DIMENSION = "一级维度";

    public static final String THIRD_LEVEL_DIMENSION = "三级维度";
    public static final String WRITE_DEMO = "填写样例";
    public static final String TEMPLATE_QUESTION = "编码";
    public static final String TEMPLATE_DIMENSION = "多视角";

    public static final String QUESTION_LIST = "题目列表";

    public static final String NAN = "N/A";

    public static final String NAN0 = "/";

    public static final String HIGH = "高";

    public static final String MEDIUM = "中";

    public static final String LOW = "低";

    public static final String ZH_COMMA = "，";

    public static final String EMPTY_STRING = "";

    public static final Double DOUBLE_ZERO = 0D;

    public static final Double DOUBLE_HUNDRED = 100D;

    public static final String secondLevelDirectory = "secondLevelDirectory";

    public static final String directoryI18 = "directoryI18";

    public static final String ANSWER_CODE_TYPE = "answerCodeType";

    public static final String TIMESTAMP = "timestamp";

    public static final String UUID = "uuid";

    public static final String URL = "url";

    public static final String BTN_TXT = "btnTxt";

    public static final String PRISMA_HISTORY_DATA_TEMPLATE = "历史对比数据模板";

    public static final String SUMMARY_ZH = "总结";
    public static final String SUMMARY_EN = "Summary";

    public static final String ADVANTAGE_ZH = "优势";
    public static final String ADVANTAGE_EN = "Advantage";

    public static final String DISADVANTAGE_ZH = "劣势";
    public static final String DISADVANTAGE_EN = "Disadvantage";

    public static final int FIVE = 5;

    public static final int FIFTY = 50;

    public static final String BIRTHDAY = "出生日期";

    public static final String ANSWER_STATUS_TEMPLATE = "填答进度";
    public static final String DEMOGRAPHIC = "人口标签";
    public static final String CAPTCHA_TEXT = "验证码";

    public static final String START_TIME = "开始时间";
    public static final String END_TIME = "结束时间";
    public static final String GROUP = "组别";
    public static final String ROLE = "角色";
    public static final String ANSWER_FINISH_TIME = "作答完成时间";

    public static final int QUERY_BATCH_SIZE_500 = 500;

    public static final int QUERY_BATCH_SIZE_1000 = 1000;

    public static final int QUERY_BATCH_SIZE_200 = 200;

    public static final int QUERY_BATCH_SIZE_300 = 300;

    public static final int QUERY_BATCH_SIZE_50 = 50;

    public static final int QUERY_BATCH_SIZE_100 = 100;

    public static final int QUERY_BATCH_SIZE_10 = 10;

    public static final int QUERY_BATCH_SIZE_5 = 5;

    public static final int QUERY_BATCH_THREAD_SIZE = 2;

    public static final int QUERY_BATCH_THREAD_SIZE_5 = 5;

    public static final String ORGANIZATION_ID = "organizationId";

    public static final String DEMOGRAPHIC_QUESTION_ANSWER = "demographicQuestionAnswer";

    /**
     * 默认list分组大小
     */
    public static final int DEFAULT_SPLIT_LIST_SIZE = 500;

    public static final int DEFAULT_SPLIT_LIST_MAX_SIZE = 10000;

    public static final String RELATIVE_API_FILE_PATH = "../../api/file/www/";
    public static final String RELATIVE_API_FILE_PATH2 = "../api/file/www/";

    //未作答
    public static final String NO_ANSWRER = "(无)";
    //无法评价
    public static final String INVALUABLY = "无法评价";

    public static final String NO_SCORE = "不计分";

    public static final String REPORT_STYLE = "reportStyle";
    //业务系统申请的sso clientId
    public static final String MEITUAN_CLIENT_ID = "client_id";

    //登陆后将code回调业务系统的地址
    public static final String MEITUAN_REDIRECT_URI = "redirect_uri";

    //时间戳参数
    public static final String MEITUAN_T = "t";

    //登录成功后，sso回调业务系统时的code参数
    public static final String MEITUAN_CODE = "code";

    //登录成功后，sso回调业务系统时的code参数
    public static final String MEITUAN_ORIGINAL_URL = "original-url";

    //登录成功后，sso回调业务系统时的code参数
    public static final String BUSINESS = "business";

    //登录成功后，sso回调业务系统时的code参数
    public static final String MEITUAN = "meituan";

    //即ssoid，用户会话标识
    public static final String MEITUAN_ACCESSTOKEN = "accessToken";


    // 普通用户最大人口标签分类数量
    public static final int MAX_DEMOGRAPHIC_SIZE = 13;
    // 普通用户最大人口标签选项数量
    public static final int MAX_DEMOGRAPHIC_OPTION_SIZE = 20;
    // 管理员最大人口标签分类数量
    public static final int MAX_ADMIN_DEMOGRAPHIC_SIZE = 25;
    // 管理员最大人口标签选项数量
    public static final int MAX_ADMIN_DEMOGRAPHIC_OPTION_SIZE = 280;

    public static final String ANSWER = "ANSWER";

    public static final String NOTHING = "无";

    public static final String PREVIEW_PERSON_ID = "preview";

    public static final String SPACE = "  ";

    public static final String MATCH_ORGANIZATION_SHEET_NAME = "组织对比";

    public static final String ALL_ORGANIZATION_SHEET_NAME = "所有组织";

    public static final String MATCH_DEMOGRAPHIC_SHEET_NAME = "人口标签对比";

    public static final String ALL_DEMOGRAPHIC_SHEET_NAME = "所有人口标签";

    public static final String MATCH_QUESTION_SHEET_NAME = "题目对比";

    public static final String ALL_QUESTION_SHEET_NAME = "所有题目";

    public static final String MATCH_DIMENSION_SHEET_NAME = "维度对比";

    public static final String ALL_DIMENSION_SHEET_NAME = "所有维度";

    public static final String MATCH_LABEL_SHEET_NAME = "指数对比";

    public static final String ALL_LABEL_SHEET_NAME = "所有指数";

    public static final String PRIVATE = "PRIVATE";

    public static final String INVALID_CHARACTER = ":：/?？\\*<>|";

    public static final List<String> INVALID_CHARACTER_LIST;

    public static final List<String> INVALID_SHEET_NAME_CHARACTER_LIST;

    public static final int MAX_SHEET_NAME_LENGTH = 31;

    public static final int IAS_FILE_NAME_LENGTH = 80;

    public static final String ANALYZING_SUBJECT = "分析主体";

    public static final String AVERAGE = "平均分";

    public static final String ADMIN = "admin";

    public static final String STAR = "*";

    public static final String IS_SUPER_ADMIN = "superadmin";

    public static final String PIERCE_THROUGH_QUESTION_NAME = "穿透题";

    public static final String ONE_EMPTY = " ";

    public static final List<String> NEW_AT_DIMENSIONS;

    public static final int AT_DIMENSION_QUESTION_NUM = 10;

    public static final int AT_NEW_DIMENSION_QUESTION_NUM = 1;

    public static final String SUCCEED = "SUCCEED";

    public static final String FAIL = "FAIL";

    public static final String CAESURA_SIGN = "、";


    public static final I18NString GROUP_SCORE_NAME = new SagI18NString("团队均分", "Average of team", (Boolean) null);

    public static final String SELECT = "SELECT";

    public static final String AUDIT_PERSON = "审核人";

    public static final String HEAD_AUTHORIZATION = "authorization";

    /**
     * 文件名最大长度
     */
    public static final int MAX_FILE_NAME_LENGTH = 250;

    /**
     * 默认排序值
     */
    public static final Integer DEFAULT_SORT = 999;

    /**
     * 敬满
     */
    public static final I18NString EEI_ESI = I18NStringUtil.stringToI18("敬满","EEI&ESI");

    /**
     * 敬满
     */
    public static final I18NString OCI = I18NStringUtil.stringToI18("组织能力", "OCI");

    /**
     * 腾讯文件定义
     */
    public static class TENCENT_FILE_NAME {
        /**
         * 工具名称举例敬满调研报告_ 组织编码00122083_分析主体名称举例技术运营部_报告风格名称举例HR版本_时间戳到时分举例202308091907
         */
        public static final String ONE_ORG_PPT = "{questionnaire_name}_{organization}_{report_style}_{timestamp}";

        /**
         * 工具名称举例敬满调研报告_ 组织编码00122083+组织编码00122084+组织编码00122083_创建细分报告时的命名_报告风格名称举例HR版本_时间戳到时分举例202308091907
         */
        public static final String MULTI_ORG_PPT = "{questionnaire_name}_{organization}_{report_name}_{report_style}_{timestamp}";

        /**
         * 工具名称举例敬满调研报告_ 组织编码00122083_分析主体名称举例技术运营部_时间戳到时分举例202308091907
         */
        public static final String ONE_ORG_EXCEL = "{questionnaire_name}_{organization}_{timestamp}";

        /**
         * 工具名称举例敬满调研报告_ 组织编码00122083+组织编码00122084+组织编码00122083_创建细分报告时的命名_时间戳到时分举例202308091907
         */
        public static final String MULTI_ORG_EXCEL = "{questionnaire_name}_{organization}_{report_name}_{timestamp}";

        public static final String QUESTIONNAIRE_NAME_REPLACE = "{questionnaire_name}";

        public static final String ORGANIZATION_REPLACE = "{organization}";

        public static final String REPORT_NAME_REPLACE = "{report_name}";

        public static final String TIMESTAMP_REPLACE = "{timestamp}";

        public static final String REPORT_STYLE_REPLACE = "{report_style}";

        public static final String TENCENT_FILE_NAME_ZIP = "";
    }

    public static final String LANGUAGE_CONFIG = "语言配置";

    public static final String LEFT_BRACKET="(";

    public static final String RIGHT_BRACKET=")";

    public static final Map<String,I18NString> REPORT_TEXT_CONFIG_DESCRIBE_MAP;

    static {
        INVALID_CHARACTER_LIST = new ArrayList<>();
        INVALID_CHARACTER_LIST.add(":");
        INVALID_CHARACTER_LIST.add("：");
        INVALID_CHARACTER_LIST.add("/");
        INVALID_CHARACTER_LIST.add("?");
        INVALID_CHARACTER_LIST.add("？");
        INVALID_CHARACTER_LIST.add("\\");
        INVALID_CHARACTER_LIST.add("*");
        INVALID_CHARACTER_LIST.add("<");
        INVALID_CHARACTER_LIST.add(">");
        INVALID_CHARACTER_LIST.add("|");

        INVALID_SHEET_NAME_CHARACTER_LIST = new ArrayList<>();
        INVALID_SHEET_NAME_CHARACTER_LIST.add(":");
        INVALID_SHEET_NAME_CHARACTER_LIST.add("：");
        INVALID_SHEET_NAME_CHARACTER_LIST.add("/");
        INVALID_SHEET_NAME_CHARACTER_LIST.add("?");
        INVALID_SHEET_NAME_CHARACTER_LIST.add("？");
        INVALID_SHEET_NAME_CHARACTER_LIST.add("\\");
        INVALID_SHEET_NAME_CHARACTER_LIST.add("*");
        INVALID_SHEET_NAME_CHARACTER_LIST.add("[");
        INVALID_SHEET_NAME_CHARACTER_LIST.add("]");

        NEW_AT_DIMENSIONS = new ArrayList<>();
        NEW_AT_DIMENSIONS.add("WZFX-3YN-AT-038-informality");
        NEW_AT_DIMENSIONS.add("SZFX-5FC-AT-039-informality");
        NEW_AT_DIMENSIONS.add("LJFX-5FC-AT-037-informality");

        REPORT_TEXT_CONFIG_DESCRIBE_MAP = new HashMap<>();
        REPORT_TEXT_CONFIG_DESCRIBE_MAP.put("p2-child-directory-name-1-text",I18NStringUtil.stringToI18("行为改变指数 | 调研可信度 | 关键指数 | 维度",""));
        REPORT_TEXT_CONFIG_DESCRIBE_MAP.put("p2-child-directory-name-2-text",I18NStringUtil.stringToI18("双视角关键驱动因素 | 双视角综合分析",""));
        REPORT_TEXT_CONFIG_DESCRIBE_MAP.put("p2-child-directory-name-3-text",I18NStringUtil.stringToI18("综合优势 | 关键问题",""));
        REPORT_TEXT_CONFIG_DESCRIBE_MAP.put("p2-child-directory-name-4-text",I18NStringUtil.stringToI18("赞同百分比 | 历史对比 | 常模对比",""));
        REPORT_TEXT_CONFIG_DESCRIBE_MAP.put("p3-child-directory-name-1-text",I18NStringUtil.stringToI18("关键指数 - 历史对比下降群体",""));
        REPORT_TEXT_CONFIG_DESCRIBE_MAP.put("p3-child-directory-name-2-text",I18NStringUtil.stringToI18("关键指数 – 交叉分析",""));
        REPORT_TEXT_CONFIG_DESCRIBE_MAP.put("p3-child-directory-name-3-text",I18NStringUtil.stringToI18("二级维度 – 重点群体",""));
        REPORT_TEXT_CONFIG_DESCRIBE_MAP.put("p3-child-directory-name-4-text",I18NStringUtil.stringToI18("部门 & 人口标签",""));
        REPORT_TEXT_CONFIG_DESCRIBE_MAP.put("p4-child-directory-name-1-text",I18NStringUtil.stringToI18("分析主体题目数据",""));
        REPORT_TEXT_CONFIG_DESCRIBE_MAP.put("p4-child-directory-name-2-text",I18NStringUtil.stringToI18("参考表单",""));
    }
    //作答说明动态替换题目总数
    public static final String QUESTION_COUNT_REPLACE_REGEX = "\\{\\{(.*)}}";

    public static final String TENCENT_PERSON_RETREAT_LABEL_CATEGORY_CODE ="ismainland";
    public static final String TENCENT_PERSON_RETREAT_LABEL_MAINLAND_CODE ="mainland";
    public static final String TENCENT_PERSON_RETREAT_LABEL_NON_MAINLAND_CODE ="non-mainland";

    public static final String TENCENT_REPORT_TYPE_ALL ="整合版";

    public static final String TENCENT_REPORT_TYPE_MAINLAND ="大陆版";

    public static final String TENCENT_REPORT_TYPE_NON_MAINLAND ="非大陆版";

    public static final String TENCENT_REPORT_FILE_TITLE ="[系统交付报告]";

    //短信单价
    public static final  Double  SMS_PRICE =  0.1;

    //
    public static final  Double  EMAIL_PRICE =  0.05;

    public static final String SOURCE_Id = "sourceId";

    public static final String ROOT_SOURCE_Id = "rootSourceId";

    public static final String LINE = "—";

    public static final String VS = "VS";

    public static final String APP_ID = "app_id";

    public static final String URL_TARGET = "urlTarget";

    public static final String STATE = "state";

    public static final String IS_MERGE_SEND = "isMergeSend";

    public static final String YSLD_HERDER_1 = "看板人口标签筛选条件";

    public static final String YSLD_HERDER_2 = "X轴Y轴和Z轴";

    public static final String YSLD_HERDER_3 = "简历档案";

    public static final String YSLD_HERDER_BRAND_DEPARTMENT = "Brand/department";

    public static final String YSLD_WITHIN = "within";

    public static final String YSLD_CROSS = "cross";
    public static final String YSLD_HERDER_BRAND = "Brand";

    public static final String YSLD_HERDER_DEPT = "Dept.";

    public static final String YSLD_HERDER_GLOBAL_DEPT = "Global Dept.";
    public static final String YSLD_HERDER_FUNCTION = "Function";

    public static final String YSLD_HERDER_SUBFUNCTION = "Sub-function";

    public static final String YSLD_HERDER_LAYERS = "Layers";

    public static final String YSLD_HERDER_YEAR = "Year";

    public static final String YSLD_HERDER_LABEL = "Label";
}
