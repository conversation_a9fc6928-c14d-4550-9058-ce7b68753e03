DROP TABLE IF EXISTS `knx_card`;
CREATE TABLE `knx_card`
(
    `id`             VARCHAR(36),
    `card_password`  VARCHAR(50) COMMENT '卡密码',
    `is_used`        bool COMMENT '是否使用',
    `expire_date`    TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '过期时间',
    `card_type`      VARCHAR(50) COMMENT '卡片类型',
    `amount`         decimal COMMENT '金额',
    `tenant_id`      VARCHAR(36) COMMENT '租户id',
    `create_by`      VARCHAR(36),
    `create_time`    TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`    TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `last_update_by` VARCHAR(36),
    `is_deleted`     BOOL  DEFAULT FALSE,
    `app_id`         VARCHAR(36),
    CONSTRAINT  `___pk__knx_card___` PRIMARY KEY (`id`)
);