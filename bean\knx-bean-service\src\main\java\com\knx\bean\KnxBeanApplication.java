package com.knx.bean;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(scanBasePackages = {"com.knx"})
@ServletComponentScan
@EnableFeignClients(basePackages = "com.knx.bean.client")
public class KnxBeanApplication {
    public static void main(String[] args) {

        SpringApplication.run(KnxBeanApplication.class, args);


    }
}
