-- ----------------------------
-- Table structure for knx_bean_account
-- ----------------------------
/*租户余额信息*/
DROP TABLE IF EXISTS "public"."knx_bean_account";
CREATE TABLE "public"."knx_bean_account"
(
    "id"             VARCHAR(36) NOT NULL,
    "tenant_id"      VARCHAR(36) NOT NULL,
    "balance"        INT4,
    "status"         VARCHAR(50),
    "expired_date"   TIMESTAMP(3) DEFAULT NOW(),
    "create_by"      VARCHAR(36),
    "create_time"    TIMESTAMP(3) DEFAULT NOW(),
    "update_time"    TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by" VARCHAR(36),
    "is_deleted"     BOOL         DEFAULT false,
    "app_id"         VARCHAR(36),
    CONSTRAINT "___pk___knx_bean_account___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."knx_bean_account"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."knx_bean_account"."balance" IS '肯豆余额';
COMMENT ON COLUMN "public"."knx_bean_account"."status" IS '账户状态，0 可用 1 禁用';
COMMENT ON COLUMN "public"."knx_bean_account"."expired_date" IS '肯豆账户过期时间';
COMMENT ON TABLE "public"."knx_bean_account" IS '租户肯豆账户表';

-- ----------------------------
-- Table structure for knx_bean_transaction
-- ----------------------------
/*租户账户流水信息表*/
DROP TABLE IF EXISTS "public"."knx_bean_transaction";
CREATE TABLE "public"."knx_bean_transaction"
(
    "id"              VARCHAR(36) NOT NULL,
    "tenant_id"       VARCHAR(36) NOT NULL,
    "amount"          INT4,
    "type"            VARCHAR(50),
    "contract_no"     VARCHAR(100),
    "order_no"        VARCHAR(100),
    "contract_amount" INT4,
    "balance"         INT4,
    "status"          VARCHAR(50),
    "create_by"       VARCHAR(36),
    "create_time"     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"  VARCHAR(36),
    "is_deleted"      BOOL         DEFAULT false,
    "app_id"          VARCHAR(36),
    CONSTRAINT  "___pk__knx_bean_transaction___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."knx_bean_transaction"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."knx_bean_transaction"."amount" IS '租户消费或者充值数量';
COMMENT ON COLUMN "public"."knx_bean_transaction"."type" IS '类型 0 充值 1 取消充值 2 创建活动 3 查看报告 4 下载报告';
COMMENT ON COLUMN "public"."knx_bean_transaction"."contract_no" IS '合同编号，类型为充值时，这个字段才有用';
COMMENT ON COLUMN "public"."knx_bean_transaction"."order_no" IS '订单号';
COMMENT ON COLUMN "public"."knx_bean_transaction"."contract_amount" IS '合同金额';
COMMENT ON COLUMN "public"."knx_bean_transaction"."balance" IS '租户余额';
COMMENT ON COLUMN "public"."knx_bean_transaction"."status" IS '充值的流水明细取消和执行';
COMMENT ON TABLE "public"."knx_bean_transaction" IS '租户肯豆账户明细表';