DROP TABLE IF EXISTS prisma_custom_index_combination_detail;
CREATE TABLE prisma_custom_index_combination_detail
(
    "id"                              VARCHAR(36)  NOT NULL,
    "prisma_custom_index_combination_id"  VARCHAR(36)  NOT NULL,
    "type"                            VARCHAR(50) NOT NULL,
    "dimension_code"                  VARCHAR(50) DEFAULT NULL,
    "question_id"                     VARCHAR(36) DEFAULT NULL,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___prisma_custom_index_combination_detail___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."prisma_custom_index_combination_detail" IS 'prisma自定义指数组合明细表';
COMMENT ON COLUMN "public"."prisma_custom_index_combination_detail"."prisma_custom_index_combination_id" IS 'prisma自定义指数组合ID';
COMMENT ON COLUMN "public"."prisma_custom_index_combination_detail"."type" IS '类型';
COMMENT ON COLUMN "public"."prisma_custom_index_combination_detail"."dimension_code" IS '维度编码';
COMMENT ON COLUMN "public"."prisma_custom_index_combination_detail"."question_id" IS '题目ID';
