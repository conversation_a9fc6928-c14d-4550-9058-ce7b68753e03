server:
  port: 8002
  shutdown: graceful #开启优雅停机，默认IMMEDIATE是立即关机

# 应用就绪相关设置，开启探针。
management:
  endpoints:
    web:
      exposure:
        include: "health,info,service-registry"
  endpoint:
    health:
      probes:
        enabled: true
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true


spring:
  application:
    name: knx-bean-service
  lifecycle:
    # 设置优雅停机的缓冲时间，默认30s，在规定时间内如果线程无法执行完毕则会被强制停机。
    timeout-per-shutdown-phase: 30s
  datasource:
    hikari:
      minimum-idle: 0
      maximum-pool-size: 5
      max-lifetime: 10000
      idle-timeout: 10000

  jackson:
    default-property-inclusion: non_null
    time-zone: Asia/Shanghai
  kafka:
    group-id: knx_bean_service_group
ribbon:
  ReadTimeout: 600000
  ConnectTimeout: 600000

redis:
  timeout: 30000
  maxTotal: 30
  maxIdle: 10
  numTestsPerEvictionRun: 1024
  timeBetweenEvictionRunsMillis: 30000
  minEvictableIdleTimeMillis: 1800000
  softMinEvictableIdleTimeMillis: 10000
  maxWaitMillis: 1500
  testOnBorrow: true
  testWhileIdle: true
  blockWhenExhausted: false

mybatis-plus:
  mapper-locations: classpath*:/mapper/*Mapper.xml
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    map-underscore-to-camel-case: true
  global-config:
    banner: false

shiro:
  ignore:
    - /knx/bean/card/checkoutCardPassword
    - /knx/bean/card/batchCheckoutCardPassword
    - /knx/bean/card/updateBatch
