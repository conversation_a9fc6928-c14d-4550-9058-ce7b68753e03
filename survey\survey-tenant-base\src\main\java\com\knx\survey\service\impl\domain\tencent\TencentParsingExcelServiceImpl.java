package com.knx.survey.service.impl.domain.tencent;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.knx.common.base.model.BaseModel;
import com.knx.common.base.service.exception.BusinessException;
import com.knx.common.dynamic.annotation.MultiTenant;
import com.knx.survey.common.base.field.SurveyField;
import com.knx.survey.common.util.ToolUtil;
import com.knx.survey.common.util.excelUtil.listener.MapBaseExcelListener;
import com.knx.survey.common.util.excelUtil.listener.MultiSheetMapBaseExcelListener;
import com.knx.survey.common.util.excelUtil.pojo.ReadMsg;
import com.knx.survey.common.util.excelUtil.utils.ExcelUtil;
import com.knx.survey.error.SurveyErrorCode;
import com.knx.survey.model.common.option.SurveyStandardOptionData;
import com.knx.survey.model.enums.*;
import com.knx.survey.model.report.prisma.PrismaHistoryData;
import com.knx.survey.model.report.prisma.PrismaNorm;
import com.knx.survey.model.report.prisma.SelectedQuestionMapping;
import com.knx.survey.model.report.prisma.tencent.ApprovalQuestionDimensionDetail;
import com.knx.survey.model.report.prisma.tencent.PrismaOrganizationDemographicApprovalData;
import com.knx.survey.model.tenant.answer.SurveyAnswer;
import com.knx.survey.model.tenant.answer.json.OptionScore;
import com.knx.survey.model.tenant.demographic.SurveyDemographic;
import com.knx.survey.model.tenant.mapping.SurveyPersonOrganizationMapping;
import com.knx.survey.model.tenant.organization.SurveyOrganization;
import com.knx.survey.model.tenant.organization.SurveyOrganizationParentMapping;
import com.knx.survey.model.tenant.person.SurveyPersonValid;
import com.knx.survey.model.tenant.project.SurveyIasSelectedQuestionMapping;
import com.knx.survey.model.tenant.project.SurveyProject;
import com.knx.survey.model.tenant.question.SurveyQuestion;
import com.knx.survey.model.tenant.questionnaire.SurveyQuestionnaire;
import com.knx.survey.service.api.domain.*;
import com.knx.survey.service.api.domain.report.tencent.IPrismaOrganizationDemographicApprovalDataService;
import com.knx.survey.service.api.domain.tencent.ITencentParsingExcelService;
import com.knx.survey.service.config.DynamicConfig;
import com.knx.survey.service.config.DynamicTencentProjectConfig;
import com.knx.survey.service.impl.domain.dimensionScore.TencentDimensionScoreServiceImpl;
import com.knx.survey.util.I18NStringUtil;
import com.knx.survey.vo.ProjectVO;
import com.knx.survey.vo.answer.ConditionQuestionOptionVo;
import com.knx.survey.vo.prisma.PrismNormReqVO;
import com.knx.survey.vo.prisma.PrismaHistoryDataVO;
import com.knx.survey.vo.prisma.PrismaNormVO;
import com.knx.survey.vo.report.DimensionQuestionInfoVo;
import com.knx.survey.vo.report.PrismaReportExportInfo;
import com.knx.survey.vo.tencent.ImportDimensionScoreResultVo;
import com.knx.survey.vo.tencent.ImportIndexDimensionDataVo;
import com.knx.survey.vo.tencent.ImportOpenQuestionResultVo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TencentParsingExcelServiceImpl.java
 * @Description TODO
 * @createTime 2025年07月01日 13:41:00
 */
@MultiTenant
@Slf4j
@Service
public class TencentParsingExcelServiceImpl implements ITencentParsingExcelService {

    @Autowired
    private IProjectService projectService;

    @Autowired
    private ISurveyOrganizationService organizationService;

    @Autowired
    private IDemographicService demographicService;

    @Autowired
    private IQuestionService questionService;

    @Autowired
    private IQuestionnaireService questionnaireService;

    @Autowired
    private TencentDimensionScoreServiceImpl tencentDimensionScoreService;

    @Autowired
    private IPrismaHistoryDataService prismaHistoryDataService;

    @Autowired
    private IPrismaNormService prismaNormService;

    @Autowired
    private ISurveyOrganizationParentMappingService surveyOrganizationParentMapping;

    @Autowired
    private ISurveyPersonValidService personValidService;

    @Autowired
    private ISurveyPersonOrganizationMappingService personOrganizationMappingService;

    @Autowired
    private ISurveyAnswerService surveyAnswerService;

    @Autowired
    private ISurveyOrganizationParentMappingService organizationParentMappingService;

    @Autowired
    private ISurveyIasSelectedQuestionMappingService selectedQuestionMappingService;

    @Autowired
    @Lazy
    private IPrismaOrganizationDemographicApprovalDataService prismaOrganizationDemographicApprovalDataService;

    @Autowired
    private DynamicTencentProjectConfig tencentProjectConfig;


    /**
     * 解析excel
     */
    @Override
    @SneakyThrows
    public void parsingExcel(String projectName, List<File> oneProjectFiles,  List<ProjectVO> remoteProjectList, Map<String, List<PrismaHistoryDataVO>> projectIdHistorysMap, Map<String, List<PrismaNormVO>> projectIdPrismaNormsMap) {
        List<SurveyProject> filterProjects = projectService.getByName(projectName);
        filterProjects = filterProjects.stream().filter(project -> BooleanUtils.isNotFalse(project.getIsShow())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterProjects)) {
            throw new BusinessException(projectName + "活动不存在", SurveyErrorCode.PARAM_ERROR);
        }

        if (filterProjects.size() > 1) {
            throw new BusinessException(projectName + "活动存在多个", SurveyErrorCode.PARAM_ERROR);
        }
        SurveyProject project = filterProjects.get(0);

        ProjectVO remoteProject;
        List<PrismaHistoryDataVO> remoteProjectHistorys = null;
        if (CollectionUtils.isNotEmpty(remoteProjectList)) {
            List<ProjectVO> similarRemoteProjects = remoteProjectList.stream().filter(p -> p.getName().equals(project.getName())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(similarRemoteProjects)) {
                throw new BusinessException(projectName + "远程活动不存在", SurveyErrorCode.PARAM_ERROR);
            }

            if (similarRemoteProjects.size() > 1) {
                throw new BusinessException(projectName + "远程活动有多个", SurveyErrorCode.PARAM_ERROR);
            }
            remoteProject = similarRemoteProjects.get(0);
            remoteProjectHistorys = projectIdHistorysMap.getOrDefault(remoteProject.getId(), new ArrayList<>());
        } else {
            remoteProject = null;
        }

        SurveyQuestionnaire questionnaire = questionnaireService.getOneByProjectId(project.getId());

        String projectYear;
        if (remoteProject != null) {
            projectYear = String.valueOf(DateUtil.year(remoteProject.getStartTime()));
        } else {
            projectYear = String.valueOf(DateUtil.year(project.getStartTime()));
        }

        // 常模
        List<PrismaNormVO> norms;
        if (remoteProject != null) {
            norms = projectIdPrismaNormsMap.getOrDefault(remoteProject.getId(), new ArrayList<>());
        } else {
            PrismNormReqVO req = new PrismNormReqVO();
            req.setProjectId(project.getId());
            norms = prismaNormService.normListByProjectId(req);
        }

        //历史数据
        List<PrismaHistoryDataVO> historyDataList;
        if (remoteProject != null) {
            historyDataList = remoteProjectHistorys;
        } else {
            historyDataList = prismaHistoryDataService.listByProjectId(project.getId());
        }

        // 所有人口标签
        List<SurveyDemographic> demographics = demographicService.listAllByProjectId(project.getId());
        Map<String, SurveyDemographic> demographicMap = demographics.stream().collect(Collectors.toMap(SurveyDemographic::getId, demographic -> demographic));
        // 设置人口标签全路径映射
        Map<String, SurveyDemographic> fullNameDemographicMap = new HashMap<>();
        for (SurveyDemographic demographic : demographics) {
            if (SurveyField.STRING_VALUE_ZERO.equals(demographic.getParentId())) {
                // 标签题目
                fullNameDemographicMap.put(demographic.getName().getZh_CN(), demographic);
                continue;
            }

            /* 剩下的都是子标签 */
            SurveyDemographic rootParent = demographicMap.get(demographic.getParentId());
            if (SurveyField.STRING_VALUE_ZERO.equals(rootParent.getParentId())) {
                String demName = rootParent.getName().getZh_CN() + SurveyField.HYPHEN;
                demName += demographic.getName().getZh_CN();

                fullNameDemographicMap.put(demName, demographic);
            } else {
                // 二级标签
                SurveyDemographic parentDem = rootParent;
                rootParent = demographicMap.get(rootParent.getParentId());
                String demName = rootParent.getName().getZh_CN() + SurveyField.HYPHEN;
                demName += parentDem.getName().getZh_CN() + SurveyField.UNDER_LINE;
                demName += demographic.getName().getZh_CN();

                fullNameDemographicMap.put(demName, demographic);
            }
        }

        // 活动属性
        MainlandEnum defaultMainland = null;
        if (tencentProjectConfig.getProject().stream().anyMatch(d -> projectName.equals(d.getProject()))) {
            defaultMainland = MainlandEnum.getByName(tencentProjectConfig.getProject().stream().filter(d -> projectName.equals(d.getProject())).findFirst().get().getType());
        }

        // 题目数据
        List<SurveyQuestion> questions = getPrismaQuestions(questionnaire);
        // 多视角数据
        PrismaReportExportInfo prismaReportExportInfo = new PrismaReportExportInfo();
        prismaReportExportInfo.setQuestions(questions);
        prismaReportExportInfo.setQuestionnaire(questionnaire);
        prismaReportExportInfo.setProject(project);
        prismaReportExportInfo.setProjectId(project.getId());
        tencentDimensionScoreService.setPrismaDimensions(prismaReportExportInfo);
        tencentDimensionScoreService.setPrismaDimensionQuestionsInfo(prismaReportExportInfo);
        Map<String, DimensionQuestionInfoVo> dimensionQuestionLevelMap = prismaReportExportInfo.getDimensionQuestionLevelMap();
        Set<String> labels = Arrays.stream(IasParentDimensionCodeEnum.values()).map(Enum::name).collect(Collectors.toSet());
        Set<String> keys = new HashSet<>(dimensionQuestionLevelMap.keySet());
        for (String key : keys) {
            if (!labels.contains(key)) {
                dimensionQuestionLevelMap.remove(key);
            }
        }
        dimensionQuestionLevelMap.putAll(dimensionQuestionLevelMap.values().stream().collect(Collectors.toMap(d -> d.getNameI18().getZh_CN(), dimensionQuestionInfoVo -> dimensionQuestionInfoVo)));

        Map<String, SurveyQuestion> questionNameMap = questions.stream().collect(Collectors.toMap(d -> d.getName().getZh_CN(), q -> q, (q1, q2) -> q2));


        Map<String, List<File>> orgFilesMap = oneProjectFiles.stream()
                .collect(Collectors.groupingBy(d -> {
                    String key = d.getName().replace("_组织能力.xlsx", "")
                            .replace("_敬满.xlsx", "");
                    if (key.contains("_组织能力") || key.contains("_敬满")) {
                        key = ToolUtil.removeFromMarker(key, "_敬满");
                        key = ToolUtil.removeFromMarker(key, "_组织能力");
                    }
                    return key;
                }));

        for (String oneOrgFilesKey : orgFilesMap.keySet()) {
            List<File> oneOrgFiles = orgFilesMap.get(oneOrgFilesKey);
            // 获取组织编码
            String[] parts = oneOrgFiles.get(0).getName().split("_");

            // 获取组织编码
            String orgCode = null;
            int orgIndex = 0;
            // 从第一个下划线后开始遍历
            for (int i = 1; i < parts.length; i++) {
                String part = parts[i];
                // 判断是否为纯字母或数字
                if (part.matches("[a-zA-Z0-9]+")) {
                    orgCode = part;
                    orgIndex = i;
                    break;
                }
            }

            if (StringUtils.isBlank(orgCode)) {
                throw new BusinessException(oneOrgFiles.get(0).getName()+"组织编码无法解析",SurveyErrorCode.PARAM_ERROR);
            }

            // 获取组织
            SurveyOrganization organization = organizationService.getByCode(orgCode, project.getId());
            if (organization == null) {
                String fileNames = oneOrgFiles.stream().map(File::getName).collect(Collectors.joining());
                throw new BusinessException("project:"+project.getId()+"---"+orgCode + "组织不存在"+"---"+fileNames + "组织不存在", SurveyErrorCode.PARAM_ERROR);
            }

            // 所有上级组织
            List<SurveyOrganizationParentMapping> organizationParentMappings = surveyOrganizationParentMapping.listBatchByOrganizationIds(Lists.newArrayList(organization.getId()));
            organizationParentMappings = organizationParentMappings.stream()
                    .sorted(Comparator.comparingInt(SurveyOrganizationParentMapping::getDistance).reversed())
                    .collect(Collectors.toList());

            // 根组织
            SurveyOrganization rootOrganization = organizationService.getById(organizationParentMappings.get(0).getParentOrganizationId());

            // 根组织
            SurveyOrganization oneLevelOrganization = null;
            if (organizationParentMappings.size() >= 2 && organizationParentMappings.get(1).getDistance() > 0) {
                oneLevelOrganization = organizationService.getById(organizationParentMappings.get(1).getParentOrganizationId());
            }

            List<ImportDimensionScoreResultVo> importDimensionScoreResultVos = new ArrayList<>();
            for(File file : oneOrgFiles){
                boolean isOcd =  file.getName().contains("_组织能力");
                /* 解析数据 */
                MultiSheetMapBaseExcelListener multiSheetMapBaseExcelListener = new MultiSheetMapBaseExcelListener();
                Map<String, ReadMsg> readMsgMap = ExcelUtil.readMultiSheetMap(IoUtil.toStream(file), multiSheetMapBaseExcelListener, "数据概览表", "数据汇总表", "多维定焦表","分发题说明");

                ImportDimensionScoreResultVo result = new ImportDimensionScoreResultVo();

                // 活动
                result.setProject(project);

                // 分析主体
                ReadMsg subjectReadMsg = readMsgMap.remove("数据概览表_下一级组织");
                List<Map<Integer, String>> subjectMapDataList = subjectReadMsg.getMapData();
                MainlandEnum mainland = null;
                if (defaultMainland == null) {
                    // 判断是否大陆
                    String title = subjectMapDataList.get(0).get(0);
                    if (projectName.contains("23年")) {
                        if (title.contains("是否海外")) {
                            if (title.contains("是否海外_是")) {
                                mainland = MainlandEnum.NON_MAINLAND;
                            } else if (title.contains("是否海外_否")) {
                                mainland = MainlandEnum.MAINLAND;
                            } else {
                                throw new BusinessException(title + "无法识别人员范围属性", SurveyErrorCode.PARAM_ERROR);
                            }
                        } else {
                            mainland = MainlandEnum.ALL;
                        }
                    } else {
                        if (title.contains("人员范围")) {
                            if (title.contains("非大陆")) {
                                mainland = MainlandEnum.NON_MAINLAND;
                            } else if (title.contains("大陆")) {
                                mainland = MainlandEnum.MAINLAND;
                            } else {
                                throw new BusinessException(title + "无法识别人员范围属性", SurveyErrorCode.PARAM_ERROR);
                            }
                        } else {
                            mainland = MainlandEnum.ALL;
                        }
                    }
                } else {
                    mainland = defaultMainland;
                }

                List<ImportIndexDimensionDataVo> subjectDatas = parsingExcel(subjectMapDataList, organization, rootOrganization, oneLevelOrganization, isOcd);
                markLabelDimensionCode(subjectDatas, dimensionQuestionLevelMap, isOcd);
                markQuestionCode(subjectDatas, questionNameMap);
                for(ImportIndexDimensionDataVo subjectData : subjectDatas){
                    subjectData.setProjectYear(projectYear);
                    if (remoteProject != null) {
                        subjectData.setProjectId(remoteProject.getId());
                    } else {
                        subjectData.setProjectId(project.getId());
                    }
                    subjectData.setMainlandType(mainland);
                }
                markSelectQuestionDimensionCode(subjectDatas, questions);
                result.setSubject(subjectDatas);

                // 相关系数
                ReadMsg multiFocusReadMsg = readMsgMap.remove("多维定焦表");
                if (multiFocusReadMsg != null) {
                    List<Map<Integer, String>> multiFocusMapDataList = multiFocusReadMsg.getMapData();
                    Map<String, Double> questionCorrelationMap = parsingMultiFocusExcel(multiFocusMapDataList);
                    subjectDatas.forEach(d -> d.setCorr(questionCorrelationMap.get(d.getQuestionName())));
                }

                // 分发题说明
                ReadMsg selectQuestionReadMsg = readMsgMap.remove("分发题说明");
                LinkedHashSet<String> selectQuestionNames;
                if (selectQuestionReadMsg != null) {
                    List<Map<Integer, String>> selectQuestionMapDataList = selectQuestionReadMsg.getMapData();
                    selectQuestionNames = parsingselectQuestionExcel(selectQuestionMapDataList);
                } else {
                    selectQuestionNames = new LinkedHashSet<>();
                }

                // 人口学数据
                for (String sheetName : readMsgMap.keySet()) {
                    if (!sheetName.startsWith("数据概览表_")) {
                        continue;
                    }
                    ReadMsg demReadMsg = readMsgMap.get(sheetName);
                    String demographicName = sheetName.replace("数据概览表_", "");
                    List<Map<Integer, String>> demMapDataList = demReadMsg.getMapData();
                    Map<String, List<ImportIndexDimensionDataVo>> demDatas = parsingExcel(demMapDataList, demographicName, organization, fullNameDemographicMap);
                    for (String key : demDatas.keySet()) {
                        List<ImportIndexDimensionDataVo> oneDem = demDatas.get(key);

                        for (int i = 0; i < oneDem.size(); i++) {
                            ImportIndexDimensionDataVo oneDemData = oneDem.get(i);
                            oneDemData.setProjectId(subjectDatas.get(0).getProjectId());
                            oneDemData.setMainlandType(subjectDatas.get(0).getMainlandType());
                            oneDemData.setProjectYear(subjectDatas.get(0).getProjectYear());
                        }

                        for (int i = 0; i < oneDem.size(); i++) {
                            ImportIndexDimensionDataVo oneDemData = oneDem.get(i);
                            ImportIndexDimensionDataVo oneSubjectData = subjectDatas.get(i);
                            if (StringUtils.isNotBlank(oneDemData.getSelectObject())) {
                                // 分发题开始不处理
                                break;
                            }

                            // 设置code
                            oneDemData.setLabel(oneSubjectData.getLabel());
                            oneDemData.setOneDimensionCode(oneSubjectData.getOneDimensionCode());
                            oneDemData.setTwoDimensionCode(oneSubjectData.getTwoDimensionCode());
                            oneDemData.setQuestionCode(oneSubjectData.getQuestionCode());
                        }
                        markSelectQuestionDimensionCode(oneDem,questions);
                    }
                    result.getDems().put(demographicName, demDatas);
                }

                markVsData(result, oneLevelOrganization, rootOrganization, historyDataList, norms);

                //
                ReadMsg detailReadMsg = readMsgMap.remove("数据汇总表");
                List<Map<Integer, String>> detailMapDataList = detailReadMsg.getMapData();
                parsingDetailExcel(detailMapDataList, result, selectQuestionNames, fullNameDemographicMap, demographicMap, isOcd);

                importDimensionScoreResultVos.add(result);
            }

            if (CollectionUtils.isNotEmpty(importDimensionScoreResultVos)) {
                Map<String,List<ImportDimensionScoreResultVo>> groups = importDimensionScoreResultVos.stream().collect(Collectors.groupingBy(d->{
                   return  d.getSubject().get(0).getProjectId() + SurveyField.UNDER_LINE + d.getSubject().get(0).getOrgCode() + SurveyField.UNDER_LINE + d.getSubject().get(0).getMainlandType().name();
                }));
                for( String group : groups.keySet()){
                    List<ImportDimensionScoreResultVo> dimensionScoreResultVos = groups.get(group);
                    if (dimensionScoreResultVos.size() == 2) {
                        dimensionScoreResultVos.get(0).getSubject().addAll(dimensionScoreResultVos.get(1).getSubject());
                        for (String rootDem : dimensionScoreResultVos.get(0).getDems().keySet()) {
                            Map<String, List<ImportIndexDimensionDataVo>> rootDemDatasMap = dimensionScoreResultVos.get(0).getDems().get(rootDem);
                            Map<String, List<ImportIndexDimensionDataVo>> rootDemDatasMap_ = dimensionScoreResultVos.get(1).getDems().get(rootDem);
                            for (String demKey : rootDemDatasMap.keySet()) {
                                rootDemDatasMap.get(demKey).addAll(rootDemDatasMap_.get(demKey));
                            }
                        }
                    } else if(dimensionScoreResultVos.size() > 2){
                        throw new BusinessException("文件数量异常："+group,SurveyErrorCode.PARAM_ERROR);
                    }
                    prismaOrganizationDemographicApprovalDataService.saveDimensionScore(dimensionScoreResultVos.get(0));
                }

            }
        }
    }

    @Override
    public List<String> checkExcel(List<File> allFiles, List<ProjectVO> remoteProjectList) {
        Map<String, List<File>> projectOrgFilesMap = allFiles.stream()
                .collect(Collectors.groupingBy(d -> {
                    String key = d.getName().replace("_组织能力.xlsx", "")
                            .replace("_敬满.xlsx", "");
                    if (key.contains("_组织能力") || key.contains("_敬满")) {
                        key = ToolUtil.removeFromMarker(key, "_敬满");
                        key = ToolUtil.removeFromMarker(key, "_组织能力");
                    }
                    return key;
                }));

        List<String> errorFiles = new ArrayList<>();
        int count = 0;
        for (String key : projectOrgFilesMap.keySet()) {
            // 获取组织编码
            String[] parts = key.split("_");

            // 获取组织编码
            String orgCode = null;
            int orgIndex = 0;
            // 从第一个下划线后开始遍历
            for (int i = 1; i < parts.length; i++) {
                String part = parts[i];
                // 判断是否为纯字母或数字
                if (part.matches("[a-zA-Z0-9]+")) {
                    orgCode = part;
                    orgIndex = i;
                    break;
                }
            }

            if (StringUtils.isBlank(orgCode)) {
                throw new BusinessException(key + "无法匹配出组织code", SurveyErrorCode.PARAM_ERROR);
            }

            // 获取活动
            String projectName = ArrayUtil.join(ArrayUtil.sub(parts, 0, orgIndex), "_");

            ProjectVO remoteProject;
            List<PrismaHistoryDataVO> remoteProjectHistorys = null;
            if (CollectionUtils.isNotEmpty(remoteProjectList)) {
                List<ProjectVO> similarRemoteProjects = remoteProjectList.stream().filter(p -> p.getName().equals(projectName)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(similarRemoteProjects)) {
                    throw new BusinessException(projectName + "远程活动不存在", SurveyErrorCode.PARAM_ERROR);
                }

                if (similarRemoteProjects.size() > 1) {
                    throw new BusinessException(projectName + "远程活动有多个", SurveyErrorCode.PARAM_ERROR);
                }
                remoteProject = similarRemoteProjects.get(0);
            } else {
                throw new BusinessException(key + "无法匹配出活动", SurveyErrorCode.PARAM_ERROR);
            }

            // 活动属性
            MainlandEnum defaultMainland = null;
            if (tencentProjectConfig.getProject().stream().anyMatch(d -> projectName.equals(d.getProject()))) {
                defaultMainland = MainlandEnum.getByName(tencentProjectConfig.getProject().stream().filter(d -> projectName.equals(d.getProject())).findFirst().get().getType());
            }

            Map<String,PrismaOrganizationDemographicApprovalData> keyApprovalMap = new HashMap<>();
            for (File oneFile : projectOrgFilesMap.get(key)) {
                log.error("checkExcel process :"+(count ++)+"/"+allFiles.size());
                boolean isOcd =  oneFile.getName().contains("_组织能力");

                /* 解析数据 */
                MapBaseExcelListener mapBaseExcelListener =  new MapBaseExcelListener();
                List<Map<Integer, String>> dataMap = ExcelUtil.readMap(IoUtil.toStream(oneFile),"数据概览表_下一级组织", mapBaseExcelListener);

                MainlandEnum mainland = null;
                if (defaultMainland == null) {
                    // 判断是否大陆
                    String title = dataMap.get(0).get(0);
                    if (projectName.contains("23年")) {
                        if (title.contains("是否海外")) {
                            if (title.contains("是否海外_是")) {
                                mainland = MainlandEnum.NON_MAINLAND;
                            } else if (title.contains("是否海外_否")) {
                                mainland = MainlandEnum.MAINLAND;
                            } else {
                                throw new BusinessException(title + "无法识别人员范围属性", SurveyErrorCode.PARAM_ERROR);
                            }
                        } else {
                            mainland = MainlandEnum.ALL;
                        }
                    } else {
                        if (title.contains("人员范围")) {
                            if (title.contains("非大陆")) {
                                mainland = MainlandEnum.NON_MAINLAND;
                            } else if (title.contains("大陆")) {
                                mainland = MainlandEnum.MAINLAND;
                            } else {
                                throw new BusinessException(title + "无法识别人员范围属性", SurveyErrorCode.PARAM_ERROR);
                            }
                        } else {
                            mainland = MainlandEnum.ALL;
                        }
                    }
                } else {
                    mainland = defaultMainland;
                }

                String onlyKey = remoteProject.getId()+SurveyField.UNDER_LINE+orgCode+SurveyField.UNDER_LINE+mainland.name();
                PrismaOrganizationDemographicApprovalData approvalData = null;
                if(!keyApprovalMap.containsKey(onlyKey)){
                    List<PrismaOrganizationDemographicApprovalData> demographicApprovalDatas= prismaOrganizationDemographicApprovalDataService.getByProjectIdAndOrganizationCodesAndMainlandType(remoteProject.getId(),Lists.newArrayList(orgCode),mainland);
                    if(CollectionUtils.isEmpty(demographicApprovalDatas)){
                        errorFiles.add(oneFile.getName());
                        log.error("checkExcel Fail :"+oneFile.getName());
                        continue;
                    }
                    approvalData = demographicApprovalDatas.get(0);
                    approvalData.setData(JSONObject.parseArray(JSONArray.toJSONString(approvalData.getData()), ApprovalQuestionDimensionDetail.class));
                    keyApprovalMap.put(onlyKey,approvalData);
                }  else {
                    approvalData = keyApprovalMap.get(onlyKey);
                }

                if (isOcd){
                    ApprovalQuestionDimensionDetail detail = approvalData.getData().stream().filter(d -> d.getType().contains("dimension") && d.getIndex().equals("OCD")).findFirst().orElse(null);
                    if (detail != null && detail.getApproval() != null) {

                    } else {
                        log.error("checkExcel Fail :"+oneFile.getName());
                        errorFiles.add(oneFile.getName());
                    }
                } else {
                    ApprovalQuestionDimensionDetail detail = approvalData.getData().stream().filter(d -> d.getType().contains("index") && d.getIndex().equals("EEI")).findFirst().orElse(null);
                    if (detail != null && detail.getApproval() != null) {

                    } else {
                        log.error("checkExcel Fail :"+oneFile.getName());
                        errorFiles.add(oneFile.getName());
                    }
                }

            }
        }

        return errorFiles;
    }


    /**
     * 处理腾讯问卷开放题数据解析
     *
     * @param projectId 活动ID
     * @return Map<String, List < ImportOpenQuestionResultVo>> 包含开放题问题编码作为key，对应回答列表为value的结果
     * @throws BusinessException 若无效数据或解析失败
     */
    @Override
    public HashMultimap<String, ImportOpenQuestionResultVo> analyseTencentOpenQuestion(String projectId) {
        SurveyProject project = projectService.getById(projectId);
        String releaseYear = String.valueOf(DateUtil.year(project.getStartTime()));
        SurveyQuestionnaire questionnaire = questionnaireService.getOneByProjectId(projectId);

        // 全部题目
        List<SurveyQuestion> questions = getPrismaQuestions(questionnaire);
        Map<String,SurveyQuestion> questionMap = questions.stream()
                .collect(Collectors.toMap(SurveyQuestion::getId, q -> q));

        // 条件题和结果题映射map
        HashMultimap<String, ConditionQuestionOptionVo> resultConditionsMap = HashMultimap.create();
        for (SurveyQuestion question : questions) {
            if (BooleanUtils.isTrue(question.getIsPierceThrough())) {
                for (SurveyStandardOptionData optionData : question.getOptions().getOptions()) {
                    if (StringUtils.isNotBlank(optionData.getQuestionId())) {
                        resultConditionsMap.put(optionData.getQuestionId(), new ConditionQuestionOptionVo(optionData.getQuestionId(),question.getId(),optionData.getId(),optionData.getAnswerType()));
                    }
                }
            }
        }
        List<String> searchQuestionIds = resultConditionsMap.values().stream().map(d->d.getConditionQuestionId()).distinct().collect(Collectors.toList());

        // 开放题
        List<SurveyQuestion> openQuestions = questions.stream()
                .filter(q -> q.getType().isEssayQuestion())
                .collect(Collectors.toList());
        List<String> openQuestionIds = openQuestions.stream()
                .map(SurveyQuestion::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(openQuestionIds)) {
            return HashMultimap.create();
        }
        searchQuestionIds.addAll(openQuestionIds);
        searchQuestionIds = searchQuestionIds.stream()
                .distinct()
                .collect(Collectors.toList());

        // 结果题开放题
        List<String> resultOpenQuestionIds = openQuestionIds.stream().filter(d -> resultConditionsMap.keySet().contains(d)).collect(Collectors.toList());
        // 普通开放题
        List<String> commonOpenQuestionIds = openQuestionIds.stream().filter(d -> !resultConditionsMap.keySet().contains(d)).collect(Collectors.toList());

        // 分发规则
        List<SurveyIasSelectedQuestionMapping> selectedQuestionMappings = selectedQuestionMappingService.listByProjectId(projectId);
        Set<String> selectQuestionIds = selectedQuestionMappings.stream()
                .map(SurveyIasSelectedQuestionMapping::getQuestionId)
                .flatMap(List::stream)
                .collect(Collectors.toSet());

        // 获取当前活动所有组织
        List<SurveyOrganization> allOrganizations = organizationService.getByProjectId(projectId);
        // 转换为Map提高查询效率，key为组织ID
        Map<String, SurveyOrganization> orgIdMap = allOrganizations.stream()
                .collect(Collectors.toMap(SurveyOrganization::getId, org -> org, (existing, replacement) -> existing));

        // 获取当前活动所有有效参与人员
        List<SurveyPersonValid> personValids = personValidService.listValidByProjectId(projectId);
        Set<String> personIds = personValids.stream()
                .map(SurveyPersonValid::getPersonId)
                .collect(Collectors.toSet());

        // 获取当前活动所有用户组织映射关系
        List<SurveyPersonOrganizationMapping> personOrganizationMappings = personOrganizationMappingService.listByProjectId(projectId);
        // 转换为用户映射关系Map，key为personId (处理重复键情况)
        int initialCapacity = calculateInitialHashMapCapacity(personOrganizationMappings.size());
        Map<String, SurveyPersonOrganizationMapping> personOrgMap = new HashMap<>(initialCapacity);
        HashMultimap<String, String> orgPersonIdsMap = HashMultimap.create();
        for(SurveyPersonOrganizationMapping organizationMapping : personOrganizationMappings){
            personOrgMap.put(organizationMapping.getPersonId(),organizationMapping);
            orgPersonIdsMap.put(organizationMapping.getOrganizationId(),organizationMapping.getPersonId());
        }

        // 组织层级关系，转换为Map，key为organizationId
        List<SurveyOrganizationParentMapping> organizationParentMappings = organizationParentMappingService.listByProjectId(projectId);
        Map<String, List<SurveyOrganizationParentMapping>> organizationChildMappingsMap = organizationParentMappings.stream()
                .collect(Collectors.groupingBy(SurveyOrganizationParentMapping::getParentOrganizationId));
        Map<String, SurveyOrganizationParentMapping> organizationParentMappingsMap = organizationParentMappings.stream()
                .filter(d->d.getDistance() == 1)
                .collect(Collectors.toMap(SurveyOrganizationParentMapping::getOrganizationId,d->d,(d1,d2)->d1));

        HashMultimap<String, ImportOpenQuestionResultVo> orgDatas = HashMultimap.create();

        List<List<String>> batchs = CollectionUtil.split(personIds,500);
        for (List<String> batch : batchs) {
            List<SurveyAnswer> allAnswers = surveyAnswerService.listBatchByPersonIds(batch, searchQuestionIds);
            Map<String, List<SurveyAnswer>> answersMap = allAnswers.stream().collect(Collectors.groupingBy(d -> d.getPersonId() + d.getQuestionId()));

            for (String personId : batch) {
                SurveyPersonOrganizationMapping organizationMapping = personOrgMap.get(personId);
                SurveyOrganization organization = orgIdMap.get(organizationMapping.getOrganizationId());

                // 普通开放题
                for (String questionId : commonOpenQuestionIds) {
                    SurveyAnswer answer = answersMap.getOrDefault(personId + questionId, new ArrayList<>()).stream().findFirst().orElse(null);
                    if (answer == null) {
                        continue;
                    }
                    SurveyQuestion question = questionMap.get(questionId);

                    List<OptionScore> optionScores = new ArrayList<>();
                    if (question.getType() == QuestionTypeEnum.ESSAY_QUESTION) {
                        OptionScore optionScore = new OptionScore();
                        optionScore.setDescription(answer.getOptionId());
                        optionScores.add(optionScore);
                    } else if (question.getType() == QuestionTypeEnum.MULTIPLE_CHOICE_ESSAY_QUESTION) {
                        optionScores = JSONArray.parseArray(answer.getOptionId(), OptionScore.class);
                    }

                    for (OptionScore optionScore : optionScores) {
                        ImportOpenQuestionResultVo openQuestionResultVo = new ImportOpenQuestionResultVo();
                        openQuestionResultVo.setQuestionCode(question.getCode());
                        openQuestionResultVo.setProjectId(projectId);
                        openQuestionResultVo.setReleaseYear(releaseYear);
                        openQuestionResultVo.setOrganizationCode(organization.getCode());
                        openQuestionResultVo.setCustomized(selectQuestionIds.contains(questionId));
                        openQuestionResultVo.setDimensionCode(question.getCustomDimension());
                        openQuestionResultVo.setQuestionCode(question.getCode());
                        openQuestionResultVo.setOptionId(optionScore.getOptionId());
                        openQuestionResultVo.setContent(optionScore.getDescription());
                        openQuestionResultVo.setIsPierceThrough(false);
                        orgDatas.put(organization.getId(), openQuestionResultVo);
                    }
                }

                for (String questionId : resultOpenQuestionIds) {
                    SurveyQuestion question = questionMap.get(questionId);
                    List<SurveyAnswer> answers = answersMap.getOrDefault(personId + questionId, new ArrayList<>());
                    if (CollectionUtils.isEmpty(answers)) {
                        continue;
                    }

                    // 去重
                    answers = new ArrayList<>(answers.stream().collect(Collectors.toMap(SurveyAnswer::getResultOnlykey, d -> d, (d1, d2) -> d1)).values());
                    for (SurveyAnswer answer : answers) {
                        if (StringUtils.isNotBlank(answer.getConditionQuestionId())) {
                            SurveyQuestion conditionQuestion = questionMap.get(answer.getConditionQuestionId());

                            List<OptionScore> optionScores = new ArrayList<>();
                            if (question.getType() == QuestionTypeEnum.ESSAY_QUESTION) {
                                OptionScore optionScore = new OptionScore();
                                optionScore.setDescription(answer.getOptionId());
                                optionScores.add(optionScore);
                            } else if (question.getType() == QuestionTypeEnum.MULTIPLE_CHOICE_ESSAY_QUESTION) {
                                optionScores = JSONArray.parseArray(answer.getOptionId(), OptionScore.class);
                            }

                            for (OptionScore optionScore : optionScores) {
                                ImportOpenQuestionResultVo openQuestionResultVo = new ImportOpenQuestionResultVo();
                                openQuestionResultVo.setQuestionCode(question.getCode());
                                openQuestionResultVo.setProjectId(projectId);
                                openQuestionResultVo.setReleaseYear(releaseYear);
                                openQuestionResultVo.setOrganizationCode(organization.getCode());
                                openQuestionResultVo.setCustomized(selectQuestionIds.contains(conditionQuestion.getId()));
                                openQuestionResultVo.setDimensionCode(question.getCustomDimension());
                                openQuestionResultVo.setQuestionCode(question.getCode());
                                openQuestionResultVo.setOptionId(optionScore.getOptionId());
                                openQuestionResultVo.setContent(optionScore.getDescription());
                                openQuestionResultVo.setParentQuestionCode(conditionQuestion.getCode());
                                openQuestionResultVo.setParentOptionId(answer.getConditionOptionId());
                                openQuestionResultVo.setIsPierceThrough(true);
                                orgDatas.put(organization.getId(), openQuestionResultVo);
                            }
                        } else {
                            Set<ConditionQuestionOptionVo> conditionQuestionOptionVos = resultConditionsMap.get(answer.getQuestionId());
                            for (ConditionQuestionOptionVo conditionQuestionOptionVo : conditionQuestionOptionVos) {
                                SurveyAnswer conditionAnswer = answersMap.getOrDefault(personId + conditionQuestionOptionVo.getConditionQuestionId(), new ArrayList<>()).stream().findFirst().orElse(null);
                                if (conditionAnswer == null) {
                                    continue;
                                }

                                SurveyQuestion conditionQuestion = questionMap.get(conditionQuestionOptionVo.getConditionQuestionId());
                                if (conditionQuestion == null) {
                                    continue;
                                }

                                if (conditionQuestion.getType() == QuestionTypeEnum.MULTIPLE_CHOICE_ESSAY_QUESTION) {
                                    List<OptionScore> optionScores = JSONArray.parseArray(conditionAnswer.getOptionId(), OptionScore.class);
                                    if (optionScores.stream().noneMatch(d -> d.getOptionId().equals(conditionQuestionOptionVo.getConditionOptionId()))) {
                                        continue;
                                    }
                                } else {
                                    if (!conditionQuestionOptionVo.getConditionOptionId().equals(conditionAnswer.getOptionId())) {
                                        continue;
                                    }
                                }

                                List<OptionScore> optionScores = new ArrayList<>();
                                if (question.getType() == QuestionTypeEnum.ESSAY_QUESTION) {
                                    OptionScore optionScore = new OptionScore();
                                    optionScore.setDescription(answer.getOptionId());
                                    optionScores.add(optionScore);
                                } else if (question.getType() == QuestionTypeEnum.MULTIPLE_CHOICE_ESSAY_QUESTION) {
                                    optionScores = JSONArray.parseArray(answer.getOptionId(), OptionScore.class);
                                }

                                for (OptionScore optionScore : optionScores) {
                                    ImportOpenQuestionResultVo openQuestionResultVo = new ImportOpenQuestionResultVo();
                                    openQuestionResultVo.setQuestionCode(question.getCode());
                                    openQuestionResultVo.setProjectId(projectId);
                                    openQuestionResultVo.setReleaseYear(releaseYear);
                                    openQuestionResultVo.setOrganizationCode(organization.getCode());
                                    openQuestionResultVo.setCustomized(selectQuestionIds.contains(conditionQuestion.getId()));
                                    openQuestionResultVo.setDimensionCode(question.getCustomDimension());
                                    openQuestionResultVo.setQuestionCode(question.getCode());
                                    openQuestionResultVo.setOptionId(optionScore.getOptionId());
                                    openQuestionResultVo.setContent(optionScore.getDescription());
                                    openQuestionResultVo.setParentQuestionCode(conditionQuestion.getCode());
                                    openQuestionResultVo.setParentOptionId(conditionQuestionOptionVo.getConditionOptionId());
                                    openQuestionResultVo.setIsPierceThrough(true);
                                    orgDatas.put(organization.getId(), openQuestionResultVo);
                                }
                            }
                        }
                    }
                }
            }

        }

        List<String> orgIds = new ArrayList<>(orgDatas.keySet());
        Map<String,Set<String>> orgWithChildPersonIdsMap = new HashMap<>();
        for (String orgId : orgIds) {
            String currentOrgId = orgId;
            while (!SurveyField.STRING_VALUE_ZERO.equals(currentOrgId) && getOrgValidPersonList(currentOrgId, orgWithChildPersonIdsMap, organizationChildMappingsMap, orgPersonIdsMap, personIds).size() <= 10){
                SurveyOrganizationParentMapping organizationParentMapping = organizationParentMappingsMap.get(currentOrgId);
                if (organizationParentMapping == null) {
                    currentOrgId = SurveyField.STRING_VALUE_ZERO;
                } else {
                    currentOrgId = organizationParentMapping.getParentOrganizationId();
                }

            }

            if(!SurveyField.STRING_VALUE_ZERO.equals(currentOrgId) && !orgId.equals(currentOrgId)){
                SurveyOrganization organization = orgIdMap.get(currentOrgId);
                orgDatas.get(orgId).forEach(d->d.setOrganizationCode(organization.getCode()));
            }
        }

        return orgDatas;
    }

    private Set<String> getOrgValidPersonList(String orgId , Map<String,Set<String>> orgWithChildPersonIdsMap, Map<String, List<SurveyOrganizationParentMapping>> organizationChildMappingsMap, HashMultimap<String, String> orgPersonIdsMap, Set<String> allValidPersonIds) {
        Set<String> orgValidPersonIds = new HashSet<>();
        if(orgWithChildPersonIdsMap.containsKey(orgId)){
            orgValidPersonIds = orgWithChildPersonIdsMap.get(orgId);
        } else {
            // 查看有效率
            Set<String> childOrgIds = organizationChildMappingsMap.getOrDefault(orgId, new ArrayList<>()).stream().map(d -> d.getOrganizationId()).collect(Collectors.toSet());
            orgValidPersonIds = new HashSet<>(calculateInitialHashMapCapacity(200));
            for (String childOrgId : childOrgIds) {
                orgValidPersonIds.addAll(orgPersonIdsMap.get(childOrgId));
            }
            orgValidPersonIds = orgValidPersonIds.stream().filter(allValidPersonIds::contains).collect(Collectors.toSet());
            orgWithChildPersonIdsMap.put(orgId, orgValidPersonIds);
        }
        return orgValidPersonIds;
    }

    /**
     * 计算基于列表大小适当的初始 HashMap 容量
     * @param size
     * @return
     */
    private int calculateInitialHashMapCapacity(int size) {
        if (size < 3) {
            return size + 1;
        }
        size--;
        size |= size >>>  1;
        size |= size >>>  2;
        size |= size >>>  4;
        size |= size >>>  8;
        size |= size >>> 16;
        return size + 1;
    }

    public List<ImportIndexDimensionDataVo> parsingExcel(List<Map<Integer, String>> mapDataList, SurveyOrganization organization, SurveyOrganization rootOrganization, SurveyOrganization oneLevelOrganization,boolean isOcd) {
        // 分析主体数据
        List<ImportIndexDimensionDataVo> voList = this.convertExcelDataToVoList(mapDataList, organization, rootOrganization, oneLevelOrganization, isOcd);
        return voList;
    }

    public Map<String, List<ImportIndexDimensionDataVo>> parsingExcel(List<Map<Integer, String>> mapDataList, String demName, SurveyOrganization organization, Map<String, SurveyDemographic> fullNameDemographicMap) {
        // 分析主体数据
        Map<String, List<ImportIndexDimensionDataVo>> voListMap = this.convertExcelDataToDemVoList(mapDataList, demName, organization, fullNameDemographicMap);
        return voListMap;
    }

    public Map<String, Double> parsingMultiFocusExcel(List<Map<Integer, String>> mapDataList) {
        Map<String, Double> result = new HashMap<>();
        if (!"EEI相关系数".equals(mapDataList.get(1).get(4))) {
            return result;
        }

        for (int i = 3; i < mapDataList.size(); i++) {
            String questionName = mapDataList.get(i).get(3);
            String val = mapDataList.get(i).get(4);
            result.put(questionName, NumberUtils.isCreatable(val) ? Double.parseDouble(val) : null);
        }
        return result;
    }

    public LinkedHashSet<String> parsingselectQuestionExcel(List<Map<Integer, String>> mapDataList) {
        LinkedHashSet<String> result = new LinkedHashSet<>();

        for (int i = 1; i < mapDataList.size(); i++) {
            String questionName = mapDataList.get(i).get(2);
            result.add(questionName);
        }
        return result;
    }

    public void parsingDetailExcel(List<Map<Integer, String>> detailMapDataList, ImportDimensionScoreResultVo result, LinkedHashSet<String> selectQuestionNames, Map<String, SurveyDemographic> fullNameDemographicMap, Map<String, SurveyDemographic> demographicMap, boolean isOcd) {
        // 判定分析主体是第几列
        int mainRowNum = 0;
        for (int i = 3; i < detailMapDataList.size(); i++) {
            Map<Integer, String> rowData = detailMapDataList.get(i);
            if (NumberUtils.isCreatable(rowData.get(1))) {
                mainRowNum = i;
                break;
            }
        }

        int historySize = 0;
        for (String demKey : result.getDems().keySet()) {
            Map<String, List<ImportIndexDimensionDataVo>> oneDemRootIndexDimensionDatasMap = result.getDems().get(demKey);
            for (String oneChildKey : oneDemRootIndexDimensionDatasMap.keySet()) {
                for (ImportIndexDimensionDataVo indexDimensionDataVo : oneDemRootIndexDimensionDatasMap.get(oneChildKey)) {
                    historySize = indexDimensionDataVo.getVsDataMap().size();
                    break;
                }
            }
        }

        // 看是否有平均分列
        boolean isHaveAvg = detailMapDataList.get(2).entrySet().stream().anyMatch(d -> d.getValue().contains("平均分"));
        int dataSize = isHaveAvg ? 4 : 3;

        // 分发题开始列
        Integer selectColumnIndex = null;
        Integer selectSize = null;
        for (int i = 0; i < detailMapDataList.get(1).size(); i++) {
            if (selectQuestionNames.contains(detailMapDataList.get(1).get(i))) {
                selectColumnIndex = i;
                selectSize = (detailMapDataList.get(1).size() - selectColumnIndex) / dataSize;
                break;
            }
        }

        String mainOrgName = detailMapDataList.get(mainRowNum).get(0);

        // 获取分析主体数据
        for (int row = mainRowNum; row < detailMapDataList.size(); row = row + historySize + 1) {
            Map<Integer, String> rowData = detailMapDataList.get(row);
            Integer inviteNum = NumberUtils.isCreatable(rowData.get(1)) ?  new BigDecimal(rowData.get(1)).intValue() : null;
            Integer inviteFinishNum = NumberUtils.isCreatable(rowData.get(2)) ? new BigDecimal(rowData.get(2)).intValue() : null;
            Integer inviteValidNum = NumberUtils.isCreatable(rowData.get(3)) ? new BigDecimal(rowData.get(3)).intValue() : null;

            List<ImportIndexDimensionDataVo> indexDimensionDataVos = null;
            if (row == mainRowNum) {
                indexDimensionDataVos = result.getSubject();
            } else {
                String demName = rowData.get(0).replace(mainOrgName + "_", "");
                SurveyDemographic demographic = fullNameDemographicMap.get(demName);
                if (demographic == null) {
                    break;
                }

                SurveyDemographic rootDem = demographicMap.get(demographic.getParentId());
                if (!SurveyField.STRING_VALUE_ZERO.equals(rootDem.getParentId())) {
                    rootDem = demographicMap.get(rootDem.getParentId());
                }

                Map<String, List<ImportIndexDimensionDataVo>> oneRootDemData = result.getDems().get(rootDem.getName().getZh_CN());
                indexDimensionDataVos = oneRootDemData.get(demName);
            }

            // 采用临时列表只修改属性值
            List<ImportIndexDimensionDataVo> tempIndexDimensionDataVos = new ArrayList<>(indexDimensionDataVos);
            repairOcd(tempIndexDimensionDataVos,isOcd);

            for (int dataNum = 0; dataNum < tempIndexDimensionDataVos.size(); dataNum++) {
                ImportIndexDimensionDataVo indexDimensionDataVo = tempIndexDimensionDataVos.get(dataNum);
                if (StringUtils.isNotBlank(indexDimensionDataVo.getSelectObject())) {
                    // 到分发题不处理
                    break;
                }

                if(indexDimensionDataVo.getLevel() == DimensionRankEnum.LABEL && !"关键指数".equals(detailMapDataList.get(0).get(dataNum * dataSize + 6))){
                    throw new BusinessException(String.format("组织[%s]活动[%s]解析Excel异常",indexDimensionDataVo.getOrgCode(),indexDimensionDataVo.getProjectId()),SurveyErrorCode.PARAM_ERROR);
                }

                indexDimensionDataVo.setParticipantCount(inviteNum);
                indexDimensionDataVo.setValidRespondentCount(inviteValidNum);

                for (int index = 0; index < dataSize; index++) {
                    int column = dataNum * dataSize + 6 + index;
                    if (!NumberUtils.isCreatable(rowData.get(column))) {
                        continue;
                    }

                    if (index == 1) {
                        indexDimensionDataVo.setNetural(Double.parseDouble(rowData.get(column)));
                    } else if (index == 2) {
                        indexDimensionDataVo.setDisApproval(Double.parseDouble(rowData.get(column)));
                    }
                }
            }
        }

        if (selectSize != null) {
            for (int selectIndex = 0; selectIndex < selectSize; selectIndex++) {
                // 分发题题目
                // 赞成列
                int columnApprovalIndex = selectColumnIndex + selectIndex * dataSize;
                int columnNeturalIndex = selectColumnIndex + selectIndex * dataSize + 1;
                int columnDisApprovalIndex = selectColumnIndex + selectIndex * dataSize + 2;

                // 分发题目
                String selectQuestionName = detailMapDataList.get(1).get(columnApprovalIndex);

                for (int row = mainRowNum; row < detailMapDataList.size(); row = row + historySize + 1) {
                    Map<Integer, String> rowData = detailMapDataList.get(row);
                    Integer inviteNum = NumberUtils.isCreatable(rowData.get(1)) ?  new BigDecimal(rowData.get(1)).intValue() : null;
                    Integer inviteFinishNum = NumberUtils.isCreatable(rowData.get(2)) ? new BigDecimal(rowData.get(2)).intValue() : null;
                    Integer inviteValidNum = NumberUtils.isCreatable(rowData.get(3)) ? new BigDecimal(rowData.get(3)).intValue() : null;

                    List<ImportIndexDimensionDataVo> indexDimensionDataVos = null;
                    String dataName ="";
                    if (row == mainRowNum) {
                        indexDimensionDataVos = result.getSubject();
                        dataName = "分析主体";
                    } else {
                        String demName = rowData.get(0).replace(mainOrgName + "_", "");
                        SurveyDemographic demographic = fullNameDemographicMap.get(demName);
                        if (demographic == null) {
                            continue;
                        }
                        dataName = demName;

                        SurveyDemographic rootDem = demographicMap.get(demographic.getParentId());
                        if (!SurveyField.STRING_VALUE_ZERO.equals(rootDem.getParentId())) {
                            rootDem = demographicMap.get(rootDem.getParentId());
                        }

                        Map<String, List<ImportIndexDimensionDataVo>> oneRootDemData = result.getDems().get(rootDem.getName().getZh_CN());
                        indexDimensionDataVos = oneRootDemData.get(demName);
                    }

                    ImportIndexDimensionDataVo indexDimensionDataVo = indexDimensionDataVos.stream()
                            .filter(vo -> selectQuestionName.equals(vo.getQuestionName()))
                            .filter(vo -> StringUtils.isNotBlank(vo.getSelectObject()))
                            .filter(d->d.getParticipantCount() == null)
                            .findFirst().orElse(null);

                    if (indexDimensionDataVo == null) {
                        // 到分发题不处理
                        continue;
                    }

                    indexDimensionDataVo.setParticipantCount(inviteFinishNum);
                    indexDimensionDataVo.setValidRespondentCount(inviteValidNum);

                    if (!NumberUtils.isCreatable(rowData.get(columnApprovalIndex))) {
                        continue;
                    }

                    if (Double.valueOf(rowData.get(columnApprovalIndex)).compareTo(indexDimensionDataVo.getApproval()) != 0) {
                        throw new BusinessException("selectIndex:" + selectIndex + ", row:" + row + ", 数据项：" + dataName + " 数据不一致", SurveyErrorCode.PARAM_ERROR);
                    }

                    if (NumberUtils.isCreatable(rowData.get(columnNeturalIndex))) {
                        indexDimensionDataVo.setNetural(Double.parseDouble(rowData.get(columnNeturalIndex)));
                    }

                    if (NumberUtils.isCreatable(rowData.get(columnDisApprovalIndex))) {
                        indexDimensionDataVo.setDisApproval(Double.parseDouble(rowData.get(columnDisApprovalIndex)));
                    }

                }

            }
        }


    }

    /**
     * 将Excel解析后的人口学数据转换为ImportIndexDimensionDataVo列表
     *
     * 职责：将Excel文件人口学数据部分转换为数据模型对象列表，包括提取指标、维度信息，并进行VS数据处理等
     * 输入：
     *  - mapDataList：解析后的人口学数据列表，每行对应一个Map
     *  - demName：当前处理的人口学数据子维度名称
     *  - organization：当前组织信息
     *  - fullNameDemographicMap：人口标签与对象的映射表
     * 输出：
     *  - 存储每个指标、维度对应的ImportIndexDimensionDataVo对象列表，并包括相应的VS对比数据
     *
     * 主要步骤：
     *  1. 校验数据列表最少需4行
     *  2. 处理表头合并，提取指标、维度相关信息
     *  3. 遍历每行数据进行ImportIndexDimensionDataVo对象构建并设置组织code，人口标签code等属性
     *  4. 处理VS对比数据并储存在对象的vsDataMap中
     */
    public Map<String, List<ImportIndexDimensionDataVo>> convertExcelDataToDemVoList(List<Map<Integer, String>> mapDataList, String demName, SurveyOrganization organization, Map<String, SurveyDemographic> fullNameDemographicMap) {
        Map<String, List<ImportIndexDimensionDataVo>> demApprovalDatasMap = new HashMap<>();

        /* 至少需要4行数据(标题行+2行表头+1行数据) */
        if (mapDataList == null || mapDataList.size() < 4) {
            return demApprovalDatasMap;
        }
        /* 跳过第一行标题行，获取第二行和第三行作为表头行 */
        /* 第二行(索引0) */
        Map<Integer, String> headerRow0 = mapDataList.get(0);
        /* 第二行(索引1) */
        Map<Integer, String> headerRow1 = mapDataList.get(1);
        /* 第三行(索引2) */
        Map<Integer, String> headerRow2 = mapDataList.get(2);

        /* 处理VS对比列 (从第六列开始) */
        /* 合并表头行数据为List<String> */
        List<Integer> demHeaderIndexs = new ArrayList<>();
        if (!headerRow0.isEmpty()) {
            int maxColumn = Collections.max(headerRow0.keySet());
            for (int i = 0; i <= maxColumn; i++) {
                if (headerRow0.get(i).startsWith(demName + "-")) {
                    demHeaderIndexs.add(i);
                }
            }
        }

        // 没有人口标签数据
        if (CollectionUtils.isEmpty(demHeaderIndexs)) {
            return demApprovalDatasMap;
        }

        int vsHistorySize = headerRow0.size() - 1 - demHeaderIndexs.get(demHeaderIndexs.size() - 1);
        for (Integer demHeaderIndex : demHeaderIndexs) {
            String oneChildDemName = headerRow0.get(demHeaderIndex);
            SurveyDemographic surveyDemographic = fullNameDemographicMap.get(oneChildDemName);
            if (surveyDemographic == null) {
                continue;
            }
            /* 处理数据行（从第四行开始，索引3） */
            List<ImportIndexDimensionDataVo> indexDimensionDataVos = new ArrayList<>();
            demApprovalDatasMap.put(oneChildDemName, indexDimensionDataVos);
            for (int i = 3; i < mapDataList.size(); i++) {
                Map<Integer, String> dataRow = mapDataList.get(i);

                ImportIndexDimensionDataVo vo = new ImportIndexDimensionDataVo();
                indexDimensionDataVos.add(vo);
                vo.setOrgCode(organization.getCode());

                /* 从数据行对应列获取指数、一级维度、二级维度 */
                vo.setIndexName(dataRow.get(0));
                vo.setOneDimensionName(dataRow.get(1));
                vo.setTwoDimensionName(dataRow.get(2));

                /* 设置问题名称 */
                vo.setQuestionName(dataRow.get(3));

                /* 分发规则 */
                if ("分发规则".equals(headerRow0.get(4))) {
                    vo.setSelectObject(dataRow.get(4));
                }

                /* 人口标签code */
                vo.setDemographicCode(surveyDemographic.getCode());

                String approvalStr = dataRow.get(demHeaderIndex);
                if (NumberUtils.isParsable(approvalStr)) {
                    vo.setApproval(Double.parseDouble(approvalStr));
                }

                /* vs数据 */
                for (int j = 1; j <= vsHistorySize; j++) {
                    String vsStrHead = headerRow0.get(demHeaderIndex + j);
                    String approvalVsStr = dataRow.get(demHeaderIndex + j);
                    if (NumberUtils.isParsable(approvalVsStr)) {
                        vo.getVsDataMap().put(vsStrHead, Double.parseDouble(approvalVsStr));
                    } else {
                        vo.getVsDataMap().put(vsStrHead, null);
                    }
                }
            }

        }

        List<String> demKeys = new ArrayList<>(demApprovalDatasMap.keySet());
        for (int j = 0; j < demKeys.size(); j++) {
            List<ImportIndexDimensionDataVo> resultList = demApprovalDatasMap.get(demKeys.get(j));
            if (j == 0) {
                markDimensionLevelType(resultList);
            } else {
                List<ImportIndexDimensionDataVo> templateList = demApprovalDatasMap.get(demKeys.get(0));
                for (int k = 0; k < resultList.size(); k++) {
                    ImportIndexDimensionDataVo vo = resultList.get(k);
                    ImportIndexDimensionDataVo template = templateList.get(k);

                    if (template.getLevel() == DimensionRankEnum.LABEL) {
                        vo.setLevel(DimensionRankEnum.LABEL);
                        vo.setIndexName(vo.getQuestionName());
                        vo.setQuestionName(null);
                    } else if (template.getLevel() == DimensionRankEnum.ONE_RANK) {
                        vo.setLevel(DimensionRankEnum.ONE_RANK);
                        vo.setOneDimensionName(vo.getQuestionName());
                        vo.setQuestionName(null);
                    } else if (template.getLevel() == DimensionRankEnum.TWO_RANK) {
                        vo.setLevel(DimensionRankEnum.TWO_RANK);
                        vo.setTwoDimensionName(vo.getQuestionName());
                        vo.setQuestionName(null);
                    } else if (template.getLevel() == DimensionRankEnum.QUESTION) {
                        vo.setLevel(DimensionRankEnum.QUESTION);
                    }
                }
            }
        }

        return demApprovalDatasMap;
    }

    private void markDimensionLevelType(List<ImportIndexDimensionDataVo> resultList) {
        // 先标题目类型
        List<String> twoDimensions = new ArrayList<>();
        for (ImportIndexDimensionDataVo vo : resultList) {
            //判断是否分发题
            if (StringUtils.isNotBlank(vo.getSelectObject())) {
                vo.setLevel(DimensionRankEnum.QUESTION);
                continue;
            }

            if (StringUtils.isNotBlank(vo.getTwoDimensionName()) && StringUtils.isNotBlank(vo.getQuestionName())) {
                vo.setLevel(DimensionRankEnum.QUESTION);
                if (StringUtils.isNotBlank(vo.getTwoDimensionName())) {
                    twoDimensions.add(vo.getTwoDimensionName());
                }
            }
        }

        // 标级二级维度和指数
        List<String> oneDimensions = new ArrayList<>();
        for (int i = 0; i < resultList.size(); i++) {
            ImportIndexDimensionDataVo vo = resultList.get(i);
            if (vo.getLevel() != null) {
                continue;
            }

            ImportIndexDimensionDataVo pre = i > 0 ? resultList.get(i - 1) : null;
            ImportIndexDimensionDataVo next = i == resultList.size() - 1 ? null : resultList.get(i + 1);

            // 判断是否指数
            if ("关键指数".equals(vo.getIndexName())) {
                vo.setLevel(DimensionRankEnum.LABEL);
                vo.setIndexName(vo.getQuestionName());
                vo.setQuestionName(null);
                continue;
            }

            // 判断是不是 有一级维度的二级维度
            if (StringUtils.isNotBlank(vo.getOneDimensionName()) && StringUtils.isBlank(vo.getTwoDimensionName())
                    && twoDimensions.contains(vo.getQuestionName())) {
                vo.setLevel(DimensionRankEnum.TWO_RANK);
                vo.setTwoDimensionName(vo.getQuestionName());
                vo.setQuestionName(null);
                if (StringUtils.isNotBlank(vo.getOneDimensionName())) {
                    oneDimensions.add(vo.getOneDimensionName());
                }
                continue;
            }
        }

        // 标记一级维度
        for (int i = 0; i < resultList.size(); i++) {
            ImportIndexDimensionDataVo vo = resultList.get(i);
            if (vo.getLevel() != null) {
                continue;
            }

            ImportIndexDimensionDataVo pre = i > 0 ? resultList.get(i - 1) : null;
            ImportIndexDimensionDataVo next = i == resultList.size() - 1 ? null : resultList.get(i + 1);

            // 判断是不是 有一级维度的二级维度
            if (StringUtils.isBlank(vo.getOneDimensionName()) && StringUtils.isBlank(vo.getTwoDimensionName())
                    && oneDimensions.contains(vo.getQuestionName()) && next != null && next.getLevel() == DimensionRankEnum.TWO_RANK) {
                vo.setLevel(DimensionRankEnum.ONE_RANK);
                vo.setOneDimensionName(vo.getQuestionName());
                vo.setQuestionName(null);
                continue;
            }
        }

        // 标记没有一级维度的二级维度
        for (int i = 0; i < resultList.size(); i++) {
            ImportIndexDimensionDataVo vo = resultList.get(i);
            if (vo.getLevel() != null) {
                continue;
            }

            ImportIndexDimensionDataVo pre = i > 0 ? resultList.get(i - 1) : null;
            ImportIndexDimensionDataVo next = i == resultList.size() - 1 ? null : resultList.get(i + 1);

            // 判断是不是 有一级维度的二级维度
            if (StringUtils.isBlank(vo.getOneDimensionName()) && StringUtils.isBlank(vo.getTwoDimensionName())
                    && twoDimensions.contains(vo.getQuestionName()) && next != null && next.getLevel() == DimensionRankEnum.QUESTION) {
                vo.setLevel(DimensionRankEnum.TWO_RANK);
                vo.setTwoDimensionName(vo.getQuestionName());
                vo.setQuestionName(null);
                continue;
            }
        }
    }

    /**
     * 将Excel解析后的Map列表转换为ImportIndexDimensionDataVo列表
     * 
     * 职责: 将Excel文件主表格数据转换为数据模型对象列表，包含指数、维度及对比数据
     * 输入:
     *  - mapDataList: Excel表格解析后的Map数据列表
     *  - organization: 当前企业组织信息
     *  - rootOrganization: 根组织信息 (用于维度关系)
     *  - oneLevelOrganization: 一级组织信息 (用于维度关系)
     * 输出: 
     *  - 存储解析后的指数维度数据对象的列表，包含历史对比和维度类型标识
     * 
     * 主要步骤:
     *  1. 校验最少需4行数据
     *  2. 提取表头信息并合并获取所有VS对比列
     *  3. 遍历数据行构建ImportIndexDimensionDataVo对象并填充基本字段
     *  4. 处理VS对比数据并标记至每个维度对象
     *  5. 根据维度层级关系设置类型标识
     */
    public List<ImportIndexDimensionDataVo> convertExcelDataToVoList(List<Map<Integer, String>> mapDataList, SurveyOrganization organization, SurveyOrganization rootOrganization, SurveyOrganization oneLevelOrganization, boolean isOcd) {
        List<ImportIndexDimensionDataVo> resultList = new ArrayList<>();
        /* 至少需要4行数据(标题行+2行表头+1行数据) */
        if (mapDataList == null || mapDataList.size() < 4) {
            return resultList;
        }

        /* 跳过第一行标题行，获取第二行和第三行作为表头行 */
        /* 第二行(索引0) */
        Map<Integer, String> headerRow0 = mapDataList.get(0);
        /* 第二行(索引1) */
        Map<Integer, String> headerRow1 = mapDataList.get(1);
        /* 第三行(索引2) */
        Map<Integer, String> headerRow2 = mapDataList.get(2);

        /* 处理VS对比列 (从第六列开始) */
        /* 合并表头行数据为List<String> */
        List<String> headers = new ArrayList<>();
        if (!headerRow0.isEmpty()) {
            int maxColumn = Collections.max(headerRow0.keySet());
            for (int i = 0; i <= maxColumn; i++) {
                headers.add(headerRow0.get(i));
            }
        }
        Map<Integer, String> vsHeaderMap = getVsHeaderMap(headers);

        /* 处理数据行（从第四行开始，索引3） */
        for (int i = 3; i < mapDataList.size(); i++) {
            Map<Integer, String> dataRow = mapDataList.get(i);
            ImportIndexDimensionDataVo vo = new ImportIndexDimensionDataVo();

            vo.setOrgCode(organization.getCode());

            /* 从数据行对应列获取指数、一级维度、二级维度 */
            vo.setIndexName(dataRow.get(0));
            vo.setOneDimensionName(dataRow.get(1));
            vo.setTwoDimensionName(dataRow.get(2));

            /* 设置问题名称 */
            vo.setQuestionName(dataRow.get(3));

            /* 分发规则 */
            String approvalStr;
            if("分发规则".equals(headerRow0.get(4)) || "分发对象".equals(headerRow0.get(4))){
                vo.setSelectObject(dataRow.get(4));
                /* 设置分析主体数值(approval) - 第六列(索引5) */
                approvalStr = dataRow.get(5);
            } else {
                /* 设置分析主体数值(approval) - 第五列(索引4) */
                approvalStr = dataRow.get(4);
            }


            if (NumberUtils.isParsable(approvalStr)) {
                vo.setApproval(Double.parseDouble(approvalStr));
            }

            /* 处理VS对比数据 */
            LinkedHashMap<String, Double> vsDataMap = new LinkedHashMap<>();
            for (Map.Entry<Integer, String> entry : vsHeaderMap.entrySet()) {
                Integer colIndex = entry.getKey();
                String header = entry.getValue();
                String valueStr = dataRow.get(colIndex);
                if (NumberUtils.isParsable(valueStr)) {
                    vsDataMap.put(header, Double.parseDouble(valueStr));
                } else {
                    vsDataMap.put(header, null);
                }
            }
            vo.setVsDataMap(vsDataMap);

            resultList.add(vo);
        }

        markDimensionLevelType(resultList);

        return resultList;
    }

    /**
     * 获取合并表头的值（优先取第二行，第二行为空则取第一行）
     */
    private String getMergedHeaderValue(Map<Integer, String> headerRow1, Map<Integer, String> headerRow2, int columnIndex) {
        String value2 = headerRow2.get(columnIndex);
        if (StringUtils.isNotBlank(value2)) {
            return value2.trim();
        }
        String value1 = headerRow1.get(columnIndex);
        return StringUtils.isNotBlank(value1) ? value1.trim() : "";
    }

    /**
     * 获取VS对比列的表头映射
     */
    private Map<Integer, String> getVsHeaderMap(List<String> headers) {
        Map<Integer, String> vsHeaderMap = new HashMap<>();
        if (headers == null || headers.size() <= 5) {
            return vsHeaderMap;
        }

        // 从第六列(索引5)开始，收集所有VS开头的列直到遇到非VS开头的标题
        int currentColumn = 4;
        while (currentColumn < headers.size()) {
            String header = headers.get(currentColumn);
            if (header != null && header.startsWith("VS")) {
                vsHeaderMap.put(currentColumn, header);
                currentColumn++;
            } else {
                if (!vsHeaderMap.isEmpty()) {
                    break;
                }
                currentColumn++;
            }
        }
        return vsHeaderMap;
    }

    /**
     * 获取题目数据
     *
     */
    public List<SurveyQuestion> getPrismaQuestions(SurveyQuestionnaire questionnaire) {
        //题本
        List<SurveyQuestion> questions = questionService.listByQuestionnaireId(questionnaire.getId());
        //去除描述题
        questions = questions.stream().filter(d -> d.getType() != QuestionTypeEnum.DESCRIBE).collect(Collectors.toList());
        for (SurveyQuestion question : questions) {
            I18NStringUtil.removeUnescapeHtmlTags(question.getName());
            I18NStringUtil.replaceQuestion(question.getName());
        }
        return questions;
    }

    /**
     * 标记维度和指数code
     * 目的：对解析后的数据中的维度和指数进行标记
     * 输入：ImportIndexDimensionDataVo列表和维度问题信息Map
     * 输出：无
     * 主要步骤：
     *    1. 按指数名称分组
     *    2. 标记指数code
     *    3. 标记一级维度和二级维度的code
     *    4. 处理没有一级维度的二级维度
     */
    public void markLabelDimensionCode(List<ImportIndexDimensionDataVo> indexDimensionDataVos, Map<String, DimensionQuestionInfoVo> dimensionQuestionLevelMap,boolean isOcd) {
        // 设置非分发题数据
        Map<String, List<ImportIndexDimensionDataVo>> labelNameDimensionsMap = indexDimensionDataVos.stream()
                .filter(d -> StringUtils.isBlank(d.getSelectObject()))
                .filter(d -> StringUtils.isNotBlank(d.getIndexName()))
                .collect(Collectors.groupingBy(ImportIndexDimensionDataVo::getIndexName));

        for (String labelName : labelNameDimensionsMap.keySet()) {
            DimensionQuestionInfoVo labelData = dimensionQuestionLevelMap.get(labelName);
            if (labelData == null) {
                continue;
            }
            List<ImportIndexDimensionDataVo> dimensionDataVos = labelNameDimensionsMap.get(labelName);

            // 标记指数code
            dimensionDataVos.forEach(d -> d.setLabel(IasParentDimensionCodeEnum.valueOf(labelData.getRealCode())));

            // 标记指数-一级维度
            Map<String, List<ImportIndexDimensionDataVo>> oneLevelDimensionsMap = dimensionDataVos.stream()
                    .filter(d -> StringUtils.isNotBlank(d.getOneDimensionName()))
                    .collect(Collectors.groupingBy(ImportIndexDimensionDataVo::getOneDimensionName));
            Map<String, DimensionQuestionInfoVo> oneLevelChildDimensions = labelData.getChild()
                    .stream().filter(d -> d.getDimensionRank() == DimensionRankEnum.ONE_RANK)
                    .collect(Collectors.toMap(d -> d.getNameI18().getZh_CN(), d -> d, (d1, d2) -> d1));
            for (String oneLevelName : oneLevelDimensionsMap.keySet()) {
                DimensionQuestionInfoVo oneLevelDimension = oneLevelChildDimensions.get(oneLevelName);
                if (oneLevelDimension == null) {
                    continue;
                }
                String oneDimensionCode = oneLevelDimension.getRealCode().replace(oneLevelDimension.getParent().getRealCode() + "_", "");
                List<ImportIndexDimensionDataVo> oneLevelDimensionChilds = oneLevelDimensionsMap.get(oneLevelName);
                oneLevelDimensionChilds.forEach(d -> d.setOneDimensionCode(oneDimensionCode));

                // 标记指数-二级维度
                Map<String, List<ImportIndexDimensionDataVo>> twoLevelDimensionsMap = oneLevelDimensionChilds.stream()
                        .filter(d -> StringUtils.isNotBlank(d.getTwoDimensionName()))
                        .collect(Collectors.groupingBy(ImportIndexDimensionDataVo::getTwoDimensionName));
                Map<String, DimensionQuestionInfoVo> twoLevelChildDimensions = oneLevelDimension.getChild()
                        .stream().filter(d -> d.getDimensionRank() == DimensionRankEnum.TWO_RANK)
                        .collect(Collectors.toMap(d -> d.getNameI18().getZh_CN(), d -> d, (d1, d2) -> d1));
                for (String twoLevelName : twoLevelDimensionsMap.keySet()) {
                    DimensionQuestionInfoVo twoLevelDimension = twoLevelChildDimensions.get(twoLevelName);
                    if (twoLevelDimension == null) {
                        continue;
                    }
                    String twoDimensionCode = twoLevelDimension.getRealCode().replace(twoLevelDimension.getParent().getRealCode() + "_", "");
                    List<ImportIndexDimensionDataVo> twoLevelDimensionChilds = twoLevelDimensionsMap.get(twoLevelName);
                    // 设置二级维度
                    twoLevelDimensionChilds.forEach(d -> d.setTwoDimensionCode(twoDimensionCode));
                }
            }

            // 标记指数-二级维度
            Map<String, List<ImportIndexDimensionDataVo>> twoLevelDimensionsMap = dimensionDataVos.stream()
                    .filter(d -> StringUtils.isBlank(d.getOneDimensionName()) && StringUtils.isNotBlank(d.getTwoDimensionName()))
                    .collect(Collectors.groupingBy(ImportIndexDimensionDataVo::getTwoDimensionName));
            Map<String, DimensionQuestionInfoVo> twoLevelChildDimensions = labelData.getChild()
                    .stream().filter(d -> d.getDimensionRank() == DimensionRankEnum.TWO_RANK)
                    .collect(Collectors.toMap(d -> d.getNameI18().getZh_CN(), d -> d, (d1, d2) -> d1));
            for (String twoLevelName : twoLevelDimensionsMap.keySet()) {
                DimensionQuestionInfoVo twoLevelDimension = twoLevelChildDimensions.get(twoLevelName);
                if (twoLevelDimension == null) {
                    continue;
                }
                String twoDimensionCode = twoLevelDimension.getRealCode().replace(twoLevelDimension.getParent().getRealCode() + "_", "");
                List<ImportIndexDimensionDataVo> twoLevelDimensionChilds = twoLevelDimensionsMap.get(twoLevelName);
                // 设置二级维度
                twoLevelDimensionChilds.forEach(d -> d.setTwoDimensionCode(twoDimensionCode));
            }
        }
    }

    private void repairOcd(List<ImportIndexDimensionDataVo> indexDimensionDataVos,boolean isOcd){
        if(isOcd){
            ImportIndexDimensionDataVo indexDimensionDataVo = indexDimensionDataVos.stream().filter(d -> d.getLabel() == IasParentDimensionCodeEnum.OCD).findFirst().orElse(null);
            if (indexDimensionDataVo != null && indexDimensionDataVo.getLevel() != DimensionRankEnum.LABEL) {
                int index = indexDimensionDataVos.indexOf(indexDimensionDataVo);

                ImportIndexDimensionDataVo labelOcd =  new ImportIndexDimensionDataVo();
                labelOcd.setIndexName(IasParentDimensionCodeEnum.OCD.getNameZh());
                labelOcd.setLabel(IasParentDimensionCodeEnum.OCD);
                labelOcd.setLevel(DimensionRankEnum.LABEL);
                labelOcd.setProjectId(indexDimensionDataVo.getProjectId());
                labelOcd.setProjectYear(indexDimensionDataVo.getProjectYear());
                labelOcd.setOrgCode(indexDimensionDataVo.getOrgCode());
                labelOcd.setMainlandType(indexDimensionDataVo.getMainlandType());
                labelOcd.setDemographicCode(indexDimensionDataVo.getDemographicCode());
                indexDimensionDataVos.add(index, labelOcd);
            }
        }
    }

    /**
     * 设置分发题的维度的题目数据
     *
     * @param indexDimensionDataVos
     * @param questions
     */
    public void markSelectQuestionDimensionCode(List<ImportIndexDimensionDataVo> indexDimensionDataVos, List<SurveyQuestion> questions) {
        Map<String, SurveyQuestion> questionNameMap = questions.stream()
                .collect(Collectors.toMap(d -> d.getName().getZh_CN(), d -> d, (d1, d2) -> d1));

        // 设置非分发题数据
        List<ImportIndexDimensionDataVo> selectQuestionDataVos = indexDimensionDataVos.stream()
                .filter(d -> d.getLevel() == DimensionRankEnum.QUESTION)
                .filter(d -> StringUtils.isNotBlank(d.getSelectObject()))
                .collect(Collectors.toList());

        for (ImportIndexDimensionDataVo selectQuestionDataVo : selectQuestionDataVos) {
            SurveyQuestion question = questionNameMap.get(selectQuestionDataVo.getQuestionName());
            if (question == null) {
                throw new BusinessException(selectQuestionDataVo.getQuestionName()+"题目不存在",SurveyErrorCode.PARAM_ERROR);
            }

            selectQuestionDataVo.setQuestionCode(question.getCode());
            selectQuestionDataVo.setLabel(question.getLabelEnum());
            selectQuestionDataVo.setOneDimensionCode(question.getParentCustomDimension());
            selectQuestionDataVo.setTwoDimensionCode(question.getCustomDimension());
        }
    }

    /**
     * 标记题目code
     * 方法职责：遍历列表中所有数据行数据，并给每行数据正确标记题目code
     * 输入：ImportIndexDimensionDataVo列表，题目名称与题目对象Map
     * 输出：无
     * 处理逻辑：
     *    1. 过滤仅处理问题类型的数据行
     *    2. 遍历每行，通过题目名称与题目对象Map获取题目code并标注
     */
    public void markQuestionCode(List<ImportIndexDimensionDataVo> indexDimensionDataVos, Map<String, SurveyQuestion> questionNameMap) {
        indexDimensionDataVos.stream().filter(d -> d.getLevel() == DimensionRankEnum.QUESTION).forEach(d -> {
            SurveyQuestion question = questionNameMap.get(d.getQuestionName());
            if (question != null) {
                d.setQuestionCode(question.getCode());
            }
        });
    }

    /**
     * 标记VS数据
     * 
     * 职责：遍历解析后的各维度数据，对其中涉及的VS对比数据进行处理，包括历史数据VS、同级组织VS、根组织VS、常模对比等
     * 
     * 输入：
     *  - result：处理后的数据结果对象
     *  - oneLevelOrg：当前组织的上一级组织
     *  - rootOrg：根部组织
     *  - historyDataLists：历史数据列表
     *  - norms：常模列表
     * 
     * 输出：无
     * 
     * 处理逻辑：
     *  1. 提取第一个非空的VS数据字段作为默认历史对比字段
     *  2. 设置主题数据的历史对比字段及ID
     *  3. 逐个设置上级组织对比、背景组织对比、一级组织对比和标杆群体对比等
     *  4. 处理剩余字段中的常模对比
     */
    public void markVsData(ImportDimensionScoreResultVo result, SurveyOrganization oneLevelOrg, SurveyOrganization rootOrg, List<PrismaHistoryDataVO> historyDataLists, List<PrismaNormVO> norms) {
        if (MapUtils.isEmpty(result.getDems())) {
            return;
        }

        String vsHistoryKey = null;
        String vsHistoryId = null;
        LinkedHashMap<String, Double> vsHistoryDefaultDataMap = new LinkedHashMap<>();
        for (String demKey : result.getDems().keySet()) {
            Map<String, List<ImportIndexDimensionDataVo>> oneDemRootIndexDimensionDatasMap = result.getDems().get(demKey);
            for (String oneChildKey : oneDemRootIndexDimensionDatasMap.keySet()) {
                for (ImportIndexDimensionDataVo indexDimensionDataVo : oneDemRootIndexDimensionDatasMap.get(oneChildKey)) {
                    if (StringUtils.isBlank(vsHistoryKey)) {
                        if (MapUtils.isEmpty(indexDimensionDataVo.getVsDataMap())) {
                            break;
                        }
                        vsHistoryDefaultDataMap = indexDimensionDataVo.getVsDataMap();

                        vsHistoryKey = vsHistoryDefaultDataMap.entrySet().stream().findFirst().get().getKey();
                        String vsHistoryName = vsHistoryKey.substring(2);
                        vsHistoryId = historyDataLists.stream().filter(d -> d.getName().getZh_CN().equals(vsHistoryName)).findFirst().orElse(new PrismaHistoryDataVO()).getId();
                    } else {
                        indexDimensionDataVo.setVsHistory(indexDimensionDataVo.getVsDataMap().get(vsHistoryKey));
                        indexDimensionDataVo.setHistoryId(vsHistoryId);
                    }
                }
            }
        }

        List<ImportIndexDimensionDataVo> subject = result.getSubject();
        LinkedHashMap<String, Double> subjectVsDataMap = new LinkedHashMap<>();
        subjectVsDataMap.putAll(subject.get(0).getVsDataMap());

        // 设置主历史
        if (StringUtils.isNotBlank(vsHistoryKey)) {
            for (ImportIndexDimensionDataVo indexDimensionDataVo : subject) {
                indexDimensionDataVo.setVsHistory(indexDimensionDataVo.getVsDataMap().get(vsHistoryKey));
                indexDimensionDataVo.setHistoryId(vsHistoryId);
            }

            for (String key : vsHistoryDefaultDataMap.keySet()) {
                subjectVsDataMap.remove(key);
            }
        }

        if (MapUtils.isEmpty(subjectVsDataMap)) {
            return;
        }

        // 上一级组织
        String superiorOrgKey = "VS上一级组织";
        if (subjectVsDataMap.containsKey(superiorOrgKey)) {
            for (ImportIndexDimensionDataVo indexDimensionDataVo : subject) {
                indexDimensionDataVo.setVsParentOrg(indexDimensionDataVo.getVsDataMap().get(superiorOrgKey));
            }
            subjectVsDataMap.remove(superiorOrgKey);
        }

        // 公司整体
        String rootOrgKey = "VS" + rootOrg.getName().getZh_CN();
        if (subjectVsDataMap.containsKey(rootOrgKey)) {
            for (ImportIndexDimensionDataVo indexDimensionDataVo : subject) {
                indexDimensionDataVo.setVsBG(indexDimensionDataVo.getVsDataMap().get(rootOrgKey));
            }
            subjectVsDataMap.remove(rootOrgKey);
        }  else {
            // 去掉一级组织
            if (oneLevelOrg != null) {
                String oneLevelKey = "VS" + oneLevelOrg.getName().getZh_CN();
                if (subjectVsDataMap.containsKey(oneLevelKey)) {
                    for (ImportIndexDimensionDataVo indexDimensionDataVo : subject) {
                        indexDimensionDataVo.setVsBG(indexDimensionDataVo.getVsDataMap().get(oneLevelKey));
                    }
                }
                subjectVsDataMap.remove(oneLevelKey);
            }
        }



        // 去掉标杆群体
        subjectVsDataMap.remove("VS标杆群体");

        // 剩下的是常模
        if (MapUtils.isEmpty(subjectVsDataMap)) {
            return;
        }

        for (String normKey : subjectVsDataMap.keySet()) {
            String normName = normKey.substring(2);
            if (normName.startsWith("腾讯整体")) {
                PrismaNormVO norm = norms.stream().filter(d -> normName.equals(d.getName().getZh_CN())).findFirst().orElse(null);
                if (norm != null) {
                    for (ImportIndexDimensionDataVo indexDimensionDataVo : subject) {
                        indexDimensionDataVo.setVsCompany(indexDimensionDataVo.getVsDataMap().get(normKey));
                        indexDimensionDataVo.setCompanyNormCode(norm.getCode());
                    }
                } else {
                    throw new BusinessException(normName+"没有常模数据",SurveyErrorCode.PARAM_ERROR);
                }

            }

            if (normName.startsWith("互联网")) {
                PrismaNormVO norm = norms.stream().filter(d -> normName.equals(d.getName().getZh_CN())).findFirst().orElse(null);
                if (norm != null) {
                    for (ImportIndexDimensionDataVo indexDimensionDataVo : subject) {
                        indexDimensionDataVo.setVsInternet(indexDimensionDataVo.getVsDataMap().get(normKey));
                        indexDimensionDataVo.setInternetNormCode(norm.getCode());
                    }
                } else {
                    throw new BusinessException(normName+"没有常模数据",SurveyErrorCode.PARAM_ERROR);
                }
            }
        }
    }

    /**
     * 获取远程活动列表
     *
     * @param remoteServerAddress
     * @param token
     * @return
     */
    @Override
    public List<ProjectVO> getRemoteProjects(String remoteServerAddress, String token){
        List<ProjectVO> projectList = new ArrayList<>();
        HttpRequest getRemoteProjectRequest = HttpUtil.createGet(remoteServerAddress + "survey/project/listProject");
        getRemoteProjectRequest = getRemoteProjectRequest.header("Authorization", token);
        HttpResponse response = getRemoteProjectRequest.execute();
        if (response.isOk()) {
            String reponseStr = response.body();
            JSONObject reponseJson = JSONObject.parseObject(reponseStr);
            if (reponseJson.getJSONObject("result") != null
                    && reponseJson.getJSONObject("result").getInteger("code") != null
                    && Integer.valueOf(0).compareTo(reponseJson.getJSONObject("result").getInteger("code")) == 0
                    && reponseJson.getJSONArray("data") != null) {
                projectList = reponseJson.getJSONArray("data").toJavaList(ProjectVO.class);
            } else {
                throw new BusinessException("getRemoteProjects request fail", SurveyErrorCode.PARAM_ERROR);
            }
        } else {
            throw new BusinessException("getRemoteProjects request fail", SurveyErrorCode.PARAM_ERROR);
        }

        return projectList.stream().filter(d->d.getStatus() == ProjectStatusEnum.OVER || d.getStatus() == ProjectStatusEnum.SUSPEND).collect(Collectors.toList());
    }

    /**
     * 获取远程历史
     *
     * @param remoteServerAddress
     * @param token
     * @return
     */
    @Override
    public List<PrismaHistoryDataVO> getRemotePrismaHistoryDatas(String remoteServerAddress, String token, String projectId) {
        List<PrismaHistoryDataVO> historyDataVOS = new ArrayList<>();
        HttpRequest getRemoteHistoryDatasRequest = HttpUtil.createGet(remoteServerAddress + "survey/prisma/history/data/listByProjectId/"+ projectId);
        getRemoteHistoryDatasRequest = getRemoteHistoryDatasRequest.header("Authorization", token);
        HttpResponse response = getRemoteHistoryDatasRequest.execute();
        if (response.isOk()) {
            String reponseStr = response.body();
            JSONObject reponseJson = JSONObject.parseObject(reponseStr);
            if (reponseJson.getJSONObject("result") != null
                    && reponseJson.getJSONObject("result").getInteger("code") != null
                    && Integer.valueOf(0).compareTo(reponseJson.getJSONObject("result").getInteger("code")) == 0
                    && reponseJson.getJSONArray("data") != null) {
                historyDataVOS = reponseJson.getJSONArray("data").toJavaList(PrismaHistoryDataVO.class);
            } else {
                throw new BusinessException("getRemotePrismaHistoryDatas request fail", SurveyErrorCode.PARAM_ERROR);
            }
        } else {
            throw new BusinessException("getRemotePrismaHistoryDatas request fail", SurveyErrorCode.PARAM_ERROR);
        }

        return historyDataVOS;
    }

    /**
     * 获取
     *
     * @param remoteServerAddress
     * @param token
     * @param projectId
     * @return
     */
    @Override
    public List<PrismaNormVO> getRemotePrismaNorms(String remoteServerAddress, String token, String projectId) {
        List<PrismaNormVO> normVOS = new ArrayList<>();
        PrismNormReqVO reqVO = new PrismNormReqVO();
        reqVO.setProjectId(projectId);
        HttpRequest getRemoteHistoryDatasRequest = HttpUtil.createPost(remoteServerAddress + "survey/norm/prisma/normListByProjectId").body(JSONObject.toJSONString(reqVO));
        getRemoteHistoryDatasRequest = getRemoteHistoryDatasRequest.header("Authorization", token);
        HttpResponse response = getRemoteHistoryDatasRequest.execute();
        if (response.isOk()) {
            String reponseStr = response.body();
            JSONObject reponseJson = JSONObject.parseObject(reponseStr);
            if (reponseJson.getJSONObject("result") != null
                    && reponseJson.getJSONObject("result").getInteger("code") != null
                    && Integer.valueOf(0).compareTo(reponseJson.getJSONObject("result").getInteger("code")) == 0
                    && reponseJson.getJSONArray("data") != null) {
                normVOS = reponseJson.getJSONArray("data").toJavaList(PrismaNormVO.class);
            } else {
                throw new BusinessException("getRemotePrismaHistoryDatas request fail", SurveyErrorCode.PARAM_ERROR);
            }
        } else {
            throw new BusinessException("getRemotePrismaHistoryDatas request fail", SurveyErrorCode.PARAM_ERROR);
        }

        return normVOS;
    }
    
    @Override
    public void syncOrganizationDemographicApprovalData(List<PrismaOrganizationDemographicApprovalData> datas, String remoteServerAddress, String token) {
        if (CollectionUtils.isEmpty(datas)) {
            log.warn("同步数据为空，跳过远程调用");
            return;
        }

        HttpRequest request = HttpUtil.createPost(remoteServerAddress + "survey/assistant/saveOrganizationDemographicApprovalData")
                .body(JSONObject.toJSONString(datas))
                .header("Authorization", token)
                .header("Content-Type", "application/json");

        HttpResponse response = request.execute();
        if (!response.isOk()) {
            throw new BusinessException("同步数据到远程服务失败，HTTP状态码：" + response.getStatus(), SurveyErrorCode.PARAM_ERROR);
        }

        JSONObject result = JSONObject.parseObject(response.body());
        if (result.getJSONObject("result") == null || result.getJSONObject("result").getInteger("code") != 0) {
            throw new BusinessException("远程服务返回错误：" + result.toJSONString(), SurveyErrorCode.PARAM_ERROR);
        }
        
        log.info("成功同步{}条组织人口标签维度赞成度数据到远程服务", datas.size());
    }

    @Override
    public void remoteAnalyseTencentOpenQuestion(String remoteServerAddress, String token, String projectId) {
        HttpRequest getRemoteHistoryDatasRequest = HttpUtil.createPost(remoteServerAddress + "survey/assistant/analyseTencentOpenQuestion?projectId="+projectId);
        getRemoteHistoryDatasRequest = getRemoteHistoryDatasRequest.header("Authorization", token);
        HttpResponse response = getRemoteHistoryDatasRequest.execute();
        log.info(response.body());
        if (response.isOk()) {
            String reponseStr = response.body();
            JSONObject reponseJson = JSONObject.parseObject(reponseStr);
            if (reponseJson.getJSONObject("result") != null
                    && reponseJson.getJSONObject("result").getInteger("code") != null
                    && Integer.valueOf(0).compareTo(reponseJson.getJSONObject("result").getInteger("code")) == 0) {
            } else {
                log.error("remoteAnalyseTencentOpenQuestion request fail projectId:{} error:{}", projectId,reponseStr);
            }
        } else {
            throw new BusinessException("remoteAnalyseTencentOpenQuestion request fail", SurveyErrorCode.PARAM_ERROR);
        }
    }
}
