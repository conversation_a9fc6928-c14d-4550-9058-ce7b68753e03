DROP TABLE IF EXISTS "public"."survey_standard_report_interpretation";
CREATE TABLE survey_standard_report_interpretation (
	id varchar(36) NOT NULL,
    tel_num varchar(50) NULL,
    report_type varchar(50) NULL,
    report_human_interpretation varchar(50) NULL,
    amount decimal NULL,
    is_group bool NULL DEFAULT false,
    tenant_id varchar(50) NULL,
    tenant_name varchar(50) NULL,
    user_id varchar(50) NULL,
    user_name varchar(50) NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__survey_standard_report_interpretation___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."survey_standard_report_interpretation"."tel_num" IS '手机号';
COMMENT ON COLUMN "public"."survey_standard_report_interpretation"."report_type" IS '报告类型';
COMMENT ON COLUMN "public"."survey_standard_report_interpretation"."report_human_interpretation" IS '人工方式';
COMMENT ON COLUMN "public"."survey_standard_report_interpretation"."amount" IS '金额';
COMMENT ON COLUMN "public"."survey_standard_report_interpretation"."is_group" IS '是否是团队报告';
COMMENT ON COLUMN "public"."survey_standard_report_interpretation"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."survey_standard_report_interpretation"."tenant_name" IS '租户名称';
COMMENT ON COLUMN "public"."survey_standard_report_interpretation"."user_id" IS '用户名称';
COMMENT ON COLUMN "public"."survey_standard_report_interpretation"."user_name" IS '用户名称';
COMMENT ON TABLE  "public"."survey_standard_report_interpretation" IS '人工解读';
