package com.knx.db.updater.survey;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * User: 林超
 * Date: 2020/8/10
 * Time: 9:44 上午
 */
@SpringBootApplication(scanBasePackages = {"com.knx"}, exclude = {DataSourceAutoConfiguration.class})
@ServletComponentScan
@EnableFeignClients(basePackages = "com.knx.common.updater.client")
@EnableDiscoveryClient
public class TenantSurveyDBUpdaterApplication {
    public static void main(String[] args) {
        SpringApplication.run(TenantSurveyDBUpdaterApplication.class, args);
    }
}
