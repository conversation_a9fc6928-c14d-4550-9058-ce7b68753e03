DROP TABLE IF EXISTS report_template_job;
CREATE TABLE report_template_job
(
    "id"                              VARCHAR(36)  NOT NULL,
    "project_id"                      VARCHAR(36)  NOT NULL,
--     "standard_report_template_id"     VARCHAR(36)  NOT NULL,
    "report_template_id"              VARCHAR(36)  NOT NULL,
    "standard_report_template_job_id" VARCHAR(36)  NOT NULL,
    "name"                            json,
    "create_by"                       VARCHAR(36)  NOT NULL,
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___report_template_job___" PRIMARY KEY ("id")
);

COMMENT ON TABLE  "public"."report_template_job" IS '活动岗位';
COMMENT ON COLUMN "public"."report_template_job"."project_id" IS '活动id';
-- COMMENT ON COLUMN "public"."report_template_job"."standard_report_template_id" IS '标准报告模板id';
COMMENT ON COLUMN "public"."report_template_job"."report_template_id" IS '报告模板id';
COMMENT ON COLUMN "public"."report_template_job"."standard_report_template_job_id" IS '标准报告岗位id';
COMMENT ON COLUMN "public"."report_template_job"."name" IS '标准报告岗位名称';
