package com.knx.survey.common.util;

import com.google.common.base.Enums;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.knx.survey.common.base.field.SurveyField;
import com.knx.survey.model.common.question.AssessmentNorm;
import com.knx.survey.model.common.report.sag.config.data.payment.PaymentConfig;
import com.knx.survey.model.enums.ReportStyleEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.util.RamUsageEstimator;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 工具类
 */
public class ToolUtil {

    public static final String A2Z_STRING = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    public static final String DIGITAL_STRING = "0123456789";

    private static final String LOCALHOST = "127.0.0.1";

    private static final String LOCALHOST1 = "localhost";

    private static final String LOCALHOST2 = "0.0.0.0";

    private static final String LOCALHOST3 = "0:0:0:0:0:0:0:1";

    /**
     * 中文数字
     */
    private static final String[] CN_NUM = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

    /**
     * 中文数字单位
     */
    private static final String[] CN_UNIT = {"", "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千"};

    /**
     * 特殊中文数字
     */
    private static final String[] SPECIAL_CN_NUM = {"一十", "一十一", "一十二", "一十三", "一十四", "一十五", "一十六", "一十七", "一十八", "一十九"};

    /**
     * 特殊字符：负
     */
    private static final String CN_NEGATIVE = "负";

    /**
     * 特殊字符：点
     */
    private static final String CN_POINT = "点";

    public static String[] chars = new String[]{"0", "1", "2", "3", "4", "5",
            "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I",
            "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V",
            "W", "X", "Y", "Z"};

    public static String[] allChars = new String[]{"a", "b", "c", "d", "e", "f",
            "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s",
            "t", "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5",
            "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I",
            "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V",
            "W", "X", "Y", "Z"};

    /**
     * list按大小分组（默认分组）
     */
    public static <T> List<List<T>> splitListUseDefaultGroupSize(List<T> list) {
        return splitList(list, 500);
    }

    /**
     * list按大小分组
     */
    public static <T> List<List<T>> splitList(List<T> list, int groupSize) {
        if (list == null || list.size() == 0) {
            return Collections.EMPTY_LIST;
        }
        return Lists.partition(list, groupSize); // 使用guava进行分组
    }

    /**
     * list按大小分组
     */
    public static <T> List<List<T>> splitList(Set<T> set, int groupSize) {
        if (set == null || set.size() == 0) {
            return Collections.EMPTY_LIST;
        }
        return Lists.partition(new ArrayList<>(set), groupSize); // 使用guava进行分组
    }

    /**
     * list分页-获取页数
     */
    public static <T> long getPageSum(List<T> list, long pageSize) {
        int total = list.size();
        if (pageSize < 1L) {
            pageSize = 10L;
        }
        long pageSum = total % pageSize == 0 ? total / pageSize : total / pageSize + 1;
        return pageSum;
    }

    /**
     * list分页
     */
    public static <T> List<T> pageList(List<T> list, long pageNo, long pageSize) {
        if (list == null || list.size() == 0) {
            return Collections.EMPTY_LIST;
        }
        if (pageNo < 1L) {
            pageNo = 1L;
        }
        if (pageSize < 1L) {
            pageSize = 10L;
        }
        List<T> subList = list.stream().skip((pageNo - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        return subList;
    }

    /**
     * 创建验证码<br />
     * PS: 5位字符串，大写字母A-Z数字0-9
     *
     * @return
     */
    public static String generateSurveyCaptcha() {
        return RandomStringUtils.random(5, A2Z_STRING + DIGITAL_STRING);
    }

    /**
     * 创建验证码<br />
     * PS: 5位字符串，大写字母A-Z数字0-9
     *
     * @return
     */
    public static String generateSurveyPassword() {
        return RandomStringUtils.random(5, A2Z_STRING + DIGITAL_STRING);
    }

    /**
     * 创建问卷编码<br />
     * PS: 5位字符串，大写字母A-Z数字0-9
     *
     * @return
     */
    public static String generateQuestionnaireCode() {
        return RandomStringUtils.random(5, A2Z_STRING + DIGITAL_STRING);
    }

    /**
     * 租户端创建人员编码
     */
    public static String generateTenantSurveyPersonCode() {
        return generateEightShortUuid();
    }

    /**
     * 填答端创建人员编码
     */
    public static String generateAnswerSurveyPersonCode() {
        return generateEightShortUuid();
    }

    /**
     * 创建5位UUID字符串
     *
     * @return
     */
    public static String generateFiveShortUuid() {
        return generateShortUuid(5);
    }

    /**
     * 8位uuid
     */
    public static String generateEightShortUuid() {
        StringBuffer shortBuffer = new StringBuffer();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < 8; i++) {
            String str = uuid.substring(i * 4, i * 4 + 4);
            int x = Integer.parseInt(str, 16);
            shortBuffer.append(allChars[x % 0x3E]);
        }
        return shortBuffer.toString();
    }

    /**
     * 创建指定长度的UUID
     *
     * @param length
     * @return
     */
    public static String generateShortUuid(Integer length) {
        if (length <= 0) {
            return null;
        }
        StringBuffer shortBuffer = new StringBuffer();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < length; i++) {
            String str = uuid.substring(i * 4, i * 4 + 4);
            int x = Integer.parseInt(str, 16);
            shortBuffer.append(chars[x % chars.length]);
        }
        return shortBuffer.toString();

    }


    /**
     * 获取客户端的ip信息
     */
    public static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Real-IP");
        if (ip == null || ip.length() == 0 || " unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Forwarded-For");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknow".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (LOCALHOST1.equals(ip) || LOCALHOST2.equals(ip) || LOCALHOST3.equals(ip) || LOCALHOST.equals(ip)) {
            ip = "127.0.0.1";
        }
        return ip;
    }


    /**
     * 判断请求是否是ajax请求
     */
    public static boolean isAjax(HttpServletRequest request) {
        String accept = request.getHeader("accept");
        return accept != null && accept.contains("application/json") || (request.getHeader("X-Requested-With") != null && request.getHeader("X-Requested-With").contains("XMLHttpRequest"));
    }

    /**
     * 获取操作系统,浏览器及浏览器版本信息
     */
    public static Map<String, String> getOsAndBrowserInfo(HttpServletRequest request) {
        Map<String, String> map = Maps.newHashMap();
        String userAgent = request.getHeader("User-Agent");
        if (userAgent == null) {
            return map;
        }
        String user = userAgent.toLowerCase();
        String os = "";
        String browser = "";
        try {
            //=================OS Info=======================
            if (userAgent.toLowerCase().contains("windows")) {
                os = "Windows";
            } else if (userAgent.toLowerCase().contains("mac")) {
                os = "Mac";
            } else if (userAgent.toLowerCase().contains("x11")) {
                os = "Unix";
            } else if (userAgent.toLowerCase().contains("android")) {
                os = "Android";
            } else if (userAgent.toLowerCase().contains("iphone")) {
                os = "IPhone";
            } else {
                os = "UnKnown, More-Info: " + userAgent;
            }
            //===============Browser===========================
            if (user.contains("edge")) {
                browser = (userAgent.substring(userAgent.indexOf("Edge")).split(" ")[0]).replace("/", "-");
            } else if (user.contains("msie")) {
                String substring = userAgent.substring(userAgent.indexOf("MSIE")).split(";")[0];
                browser = substring.split(" ")[0].replace("MSIE", "IE") + "-" + substring.split(" ")[1];
            } else if (user.contains("safari") && user.contains("version")) {
                browser = (userAgent.substring(userAgent.indexOf("Safari")).split(" ")[0]).split("/")[0]
                        + "-" + (userAgent.substring(userAgent.indexOf("Version")).split(" ")[0]).split("/")[1];
            } else if (user.contains("opr") || user.contains("opera")) {
                if (user.contains("opera")) {
                    browser = (userAgent.substring(userAgent.indexOf("Opera")).split(" ")[0]).split("/")[0]
                            + "-" + (userAgent.substring(userAgent.indexOf("Version")).split(" ")[0]).split("/")[1];
                } else if (user.contains("opr")) {
                    browser = ((userAgent.substring(userAgent.indexOf("OPR")).split(" ")[0]).replace("/", "-"))
                            .replace("OPR", "Opera");
                }

            } else if (user.contains("chrome")) {
                browser = (userAgent.substring(userAgent.indexOf("Chrome")).split(" ")[0]).replace("/", "-");
            } else if ((user.contains("mozilla/7.0")) || (user.contains("netscape6")) ||
                    (user.contains("mozilla/4.7")) || (user.contains("mozilla/4.78")) ||
                    (user.contains("mozilla/4.08")) || (user.contains("mozilla/3"))) {
                browser = "Netscape-?";

            } else if (user.contains("firefox")) {
                browser = (userAgent.substring(userAgent.indexOf("Firefox")).split(" ")[0]).replace("/", "-");
            } else if (user.contains("rv")) {
                String IEVersion = (userAgent.substring(userAgent.indexOf("rv")).split(" ")[0]).replace("rv:", "-");
                browser = "IE" + IEVersion.substring(0, IEVersion.length() - 1);
            } else {
                browser = "UnKnown, More-Info: " + userAgent;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        map.put("os", os);
        map.put("browser", browser);
        return map;
    }

    /**
     * 过滤重复key
     *
     * @param keyExtractor
     * @return
     * @param <T>
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return object -> seen.putIfAbsent(keyExtractor.apply(object), Boolean.TRUE) == null;
    }

    /**
     * 获取总价
     *
     * @param paymentConfigs
     * @return
     */
    public static BigDecimal getTotal(List<PaymentConfig> paymentConfigs) {
        Map<String, BigDecimal> payMap = new HashMap<>();
        for (PaymentConfig paymentConfig : paymentConfigs) {
            Class<? extends PaymentConfig> paymentConfigClass = paymentConfig.getClass();
            Field[] declaredFields = paymentConfigClass.getDeclaredFields();
            for (Field declaredField : declaredFields) {
                try {
                    declaredField.setAccessible(true);
                    String fieldName = declaredField.getName();
                    BigDecimal bigDecimal = (BigDecimal) declaredField.get(paymentConfig);
                    if (bigDecimal != null) {
                        BigDecimal money = payMap.get(fieldName);
                        if (money == null) {
                            payMap.put(fieldName, bigDecimal);
                        } else {
                            int i = bigDecimal.compareTo(money);
                            if (i > 0) {
                                payMap.put(fieldName, bigDecimal);
                            }
                        }
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
        BigDecimal total = BigDecimal.valueOf(0D);
        for (String fieldName : payMap.keySet()) {
            if (!Objects.equals(fieldName, PaymentConfig.Fields.total)) {
                total = total.add(payMap.get(fieldName));
            }
        }
        return total;

    }

    /**
     * 获取url中指定参数对应的值
     *
     * @param url
     * @param key
     * @return
     */
    public static String getUrlParameter(String url, String key) {
        if (StringUtils.isBlank(url)) {
            return "";
        }
        String[] urlParts = url.split("\\?");
        //没有参数
        if (urlParts.length == 1) {
            return "";
        }
        //有参数
        String[] params = urlParts[1].split("&");
        Map<String, String> paramsMap = new HashMap<>();
        for (String param : params) {
            String[] keyValue = param.split("=");
            paramsMap.put(keyValue[0], keyValue[1]);
        }
        return paramsMap.getOrDefault(key, "");
    }

    /**
     * 获取url中指定参数对应的值
     *
     * @param url
     * @param key
     * @return
     */
    public static Map<String, String> getUrlParametersMap(String url) {
        if (StringUtils.isBlank(url)) {
            return new HashMap<>();
        }
        String[] urlParts = url.split("\\?");
        //没有参数
        if (urlParts.length == 1) {
            return new HashMap<>();
        }
        //有参数
        String[] params = urlParts[1].split("&");
        Map<String, String> paramsMap = new HashMap<>();
        for (String param : params) {
            String[] keyValue = param.split("=");
            paramsMap.put(keyValue[0], keyValue[1]);
        }
        return paramsMap;
    }

    /**
     * 获取耗时<br />
     * 例: 1分15秒
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static String getCostTimeString(Date startTime, Date endTime) {
        String result = "";
        if (startTime == null || endTime == null) {
            return result;
        }
        long seconds = (endTime.getTime() - startTime.getTime()) / 1000;
        return getCostTimeString(seconds);
    }

    /**
     * 秒数转换成分秒格式字符串
     * 例: 1分15秒
     *
     * @param seconds
     * @return
     */
    public static String getCostTimeString(Long seconds) {
        if(seconds == null){
            return null;
        }
        long minute = seconds / 60;
        long second = seconds % 60;
        return minute + "分" + second + "秒";
    }

    /**
     * 秒数转换成分秒格式字符串
     * 例: 1分15秒
     *
     * @param seconds
     * @return
     */
    public static String getCostTimeStringV2(Integer seconds) {
        if (seconds == null) {
            return null;
        }
        long minute = seconds / 60;
        long second = seconds % 60;
        return (minute > 0 ? minute + "分钟" : "") + (second > 0 ? second + "秒" : "");
    }

    /**
     * 获取时间间隔秒
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static String getCostTimeSecondString(Date startTime, Date endTime) {
        Long costTime = getCostTimeSecond(startTime, endTime);
        if (costTime == null) {
            return "";
        } else {
            return costTime + "秒";
        }
    }

    /**
     * 获取时间间隔秒
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static Long getCostTimeSecond(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return null;
        }
        return (endTime.getTime() - startTime.getTime()) / 1000;
    }

    /**
     * int 转 中文数字
     * 支持到int最大值
     *
     * @param intNum 要转换的整型数
     * @return 中文数字
     */
    public static String int2chineseNum(int intNum) {
        StringBuffer sb = new StringBuffer();
        boolean isNegative = false;
        if (intNum < 0) {
            isNegative = true;
            intNum *= -1;
        }
        int count = 0;
        while (intNum > 0) {
            sb.insert(0, CN_NUM[intNum % 10] + CN_UNIT[count]);
            intNum = intNum / 10;
            count++;
        }
        if (isNegative) {
            sb.insert(0, CN_NEGATIVE);
        }
        String result = sb.toString().replaceAll("零[千百十]", "零").replaceAll("零+万", "万")
                .replaceAll("零+亿", "亿").replaceAll("亿万", "亿零")
                .replaceAll("零+", "零").replaceAll("零$", "");

        // 特殊处理10-19中文数字
        for (String specialCnNum : SPECIAL_CN_NUM) {
            if (Objects.equals(specialCnNum, result)) {
                result = result.replaceFirst("一", "");
                break;
            }
        }
        return result;
    }

    /**
     * bigDecimal 转 中文数字
     * 整数部分只支持到int的最大值
     *
     * @param bigDecimalNum 要转换的BigDecimal数
     * @return 中文数字
     */
    public static String bigDecimal2chineseNum(BigDecimal bigDecimalNum) {
        if (bigDecimalNum == null) {
            return CN_NUM[0];
        }
        StringBuffer sb = new StringBuffer();

        //将小数点后面的零给去除
        String numStr = bigDecimalNum.abs().stripTrailingZeros().toPlainString();
        String[] split = numStr.split("\\.");
        String integerStr = int2chineseNum(Integer.parseInt(split[0]));
        sb.append(integerStr);
        //如果传入的数有小数，则进行切割，将整数与小数部分分离
        if (split.length == 2) {
            //有小数部分
            sb.append(CN_POINT);
            String decimalStr = split[1];
            char[] chars = decimalStr.toCharArray();
            for (int i = 0; i < chars.length; i++) {
                int index = Integer.parseInt(String.valueOf(chars[i]));
                sb.append(CN_NUM[index]);
            }
        }
        //判断传入数字为正数还是负数
        int signum = bigDecimalNum.signum();
        if (signum == -1) {
            sb.insert(0, CN_NEGATIVE);
        }
        return sb.toString();
    }

    /**
     * 获取url中风格类型
     *
     * @param url
     * @return
     */
    public static ReportStyleEnum getStyle(String url) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        String reportStyle = ToolUtil.getUrlParameter(url, SurveyField.REPORT_STYLE);
        if (StringUtils.isNotBlank(reportStyle)) {
            return Enums.getIfPresent(ReportStyleEnum.class, reportStyle).orNull();
        }
        String[] split = url.split("/");
        if (split.length < 5) {
            return null;
        }
        String s = split[4];
        String[] split1 = s.split("\\?");
        if (split1.length == 0) {
            return null;
        }
        String style = split1[0];
        ReportStyleEnum reportStyleEnum = ReportStyleEnum.valueOf(style);
        return reportStyleEnum;
    }

    /**
     * 取交集
     *
     * @param list1
     * @param list2
     * @return
     */
    public static List<String> retainAllByGuava(List<String> list1, List<String> list2) {
        Set set = new HashSet<>(list1);
        Set set2 = new HashSet<>(list2);
        Sets.SetView intersection = Sets.intersection(set, set2);
        return new ArrayList<>(intersection);
    }

    /**
     * 取交集
     *
     * @param set1
     * @param set2
     * @return
     */
    public static Set<String> retainAllByGuava(Set<String> set1, Set<String> set2) {
        if (CollectionUtils.isEmpty(set1) || CollectionUtils.isEmpty(set2)) {
            return new HashSet<>();
        }
        return new HashSet<>(Sets.intersection(set1, set2));
    }

    /**
     * 取交集
     *
     * @param groups
     * @return
     */
    public static List<String> retainAllByGuava(List<List<String>> groups) {
        List<String> result = groups.stream()
                .reduce((list1, list2) -> {
                    return retainAllByGuava(list1, list2);
                }).orElse(new ArrayList<>());
        return result;
    }

    /**
     * 多个list笛卡尔积
     */
    public static <T> List<List<T>> getDescartes(List<List<T>> list) {
        List<List<T>> returnList = new ArrayList<>();
        descartesRecursive(list, 0, returnList, new ArrayList<>());
        return returnList;
    }

    /**
     * 递归实现
     * 原理：从原始list的0开始依次遍历到最后
     *
     * @param originalList 原始list
     * @param position     当前递归在原始list的position
     * @param returnList   返回结果
     * @param cacheList    临时保存的list
     */
    private static <T> void descartesRecursive(List<List<T>> originalList, int position, List<List<T>> returnList, List<T> cacheList) {
        List<T> originalItemList = originalList.get(position);
        for (int i = 0; i < originalItemList.size(); i++) {
            //最后一个复用cacheList，节省内存
            List<T> childCacheList = (i == originalItemList.size() - 1) ? cacheList : new ArrayList<>(cacheList);
            childCacheList.add(originalItemList.get(i));
            if (position == originalList.size() - 1) {//遍历到最后退出递归
                returnList.add(childCacheList);
                continue;
            }
            descartesRecursive(originalList, position + 1, returnList, childCacheList);
        }
    }

    /**
     * 计算对象大小(单位M)
     */
    public static String getObjectSize(Object obj) {
        //return ObjectSizeCalculator.getObjectSize(obj) / 1024.0 / 1024;
        return RamUsageEstimator.humanSizeOf(obj);
    }

    /**
     * 格式化常模数据
     *
     * @return
     */
    public static String formatAssessmentNorms(List<AssessmentNorm> assessmentNorms) {
        if (CollectionUtils.isEmpty(assessmentNorms)) {
            return SurveyField.EMPTY_STRING;
        }
        StringBuffer sb = new StringBuffer();
        for (AssessmentNorm assessmentNorm : assessmentNorms) {
            sb.append(Objects.isNull(assessmentNorm.getCount()) ? SurveyField.EMPTY_STRING : assessmentNorm.getCount().toString());
            sb.append(SurveyField.COLON);
            sb.append(Objects.isNull(assessmentNorm.getScore()) ? SurveyField.EMPTY_STRING : assessmentNorm.getScore().toString());
            sb.append("\n");
        }
        return sb.toString();
    }

    /**
     * 字符串转换成常模数据
     *
     * @return
     */
    public static List<AssessmentNorm> stringToAssessmentNorms(String norm) {
        if (StringUtils.isBlank(norm)) {
            return Collections.EMPTY_LIST;
        }
        norm = norm.trim();
        String[] groups = norm.split("\n");
        List<AssessmentNorm> assessmentNorms = new ArrayList<>(groups.length);
        for (String group : groups) {
            group = group.trim();
            if (!group.contains(SurveyField.COLON)) {
                continue;
            }
            String[] stringArray = group.split(SurveyField.COLON);
            if (stringArray.length != 2) {
                continue;
            }
            String stringCount = stringArray[SurveyField.INTEGER_VALUE_ZERO];
            String stringScore = stringArray[SurveyField.INTEGER_VALUE_ONE];
            AssessmentNorm assessmentNorm = new AssessmentNorm();
            assessmentNorm.setCount(Integer.parseInt(stringCount));
            assessmentNorm.setScore(Double.valueOf(stringScore));
            assessmentNorms.add(assessmentNorm);
        }
        return assessmentNorms;
    }

    /**
     * 判定对象字段是否全空
     *
     * @param obj
     * @return
     * @throws IllegalAccessException
     */
    public static boolean isObjectEmpty(Object obj) {
        // 如果对象本身为空，则直接返回 true
        if (obj == null) {
            return true;
        }

        // 获取对象的所有字段（包括私有字段）
        Field[] fields = obj.getClass().getDeclaredFields();

        for (Field field : fields) {
            // 设置字段为可访问
            field.setAccessible(true);

            // 检查字段的值是否为空
            Object fieldVal = null;
            try {
                fieldVal = field.get(obj);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }

            if (fieldVal != null && StringUtils.isNotBlank(String.valueOf(fieldVal))) {
                return false;
            }
        }

        return true;
    }

    public static <T> List<List<T>> paginate(List<Integer> pageSizes, List<T> allData) {
        List<List<T>> paginatedData = new ArrayList<>();
        int dataIndex = 0;

        for (int pageSize : pageSizes) {
            List<T> page = new ArrayList<>();
            for (int i = 0; i < pageSize && dataIndex < allData.size(); i++) {
                page.add(allData.get(dataIndex++));
            }
            if (CollectionUtils.isNotEmpty(page)) {
                paginatedData.add(page);
            }
        }

        return paginatedData;
    }

//    public static void main(String[] args) {
//        List<String> list1 = new ArrayList<>();
//        list1.add("A");
//        list1.add("B");
//        list1.add("C");
//
//        List<String> list2 = new ArrayList<>();
//        list2.add("D");
//        list2.add("B");
//        list2.add("C");
//        System.out.println(retainAllByGuava(list1, list2));
//
//        List<List<String>> groups = new ArrayList<>();
//        List<String> list3 = new ArrayList<>();
//        list3.add("1");
//        list3.add("2");
//        list3.add("3");
//        List<String> list4 = new ArrayList<>();
//        list4.add("1");
//        list4.add("2");
//        list4.add("3");
//        list4.add("4");
//        List<String> list5 = new ArrayList<>();
//        list5.add("3");
//        list5.add("4");
//        list5.add("5");
//        list5.add("6");
//        groups.add(list3);
//        groups.add(list4);
//        groups.add(list5);
//        System.out.println(retainAllByGuava(groups));
//    }

    public static List<String> extractTextBetweenChars(String content, String startChar, String endChar) {
        List<String> result = new ArrayList<>();
        // 构建正则表达式，匹配 startChar 和 endChar 之间的内容
        String regex = Pattern.quote(startChar) + "(.*?)" + Pattern.quote(endChar);
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);

        // 查找所有匹配的内容并添加到结果列表中
        while (matcher.find()) {
            result.add(matcher.group(1));
        }

        return result;
    }

    /**
     * 计算列表中最小值的最接近且小于它的5的倍数。
     *
     * @param approvalVals 包含双精度浮点数的列表，不能为空
     * @return 返回最接近但小于最小值的5的倍数。若最小值本身是5的倍数，则返回比其小5的数
     */
    public static Double findClosestLowerMultipleOfFive(List<Double> approvalVals) {
        if(CollectionUtils.isEmpty(approvalVals)){
            return null;
        }

        // 1. 获取列表最小值
        double minValue = Collections.min(approvalVals);

        // 2. 计算初始候选值（向下取整到最近的5的倍数）
        double candidate = Math.floor(minValue / 5) * 5;

        // 3. 处理刚好能被5整除的情况（需比最小值小）
        if (candidate == minValue) {
            candidate -= 5;
        }

        return candidate;
    }

    /**
     * 计算列表中最大值的最接近且大于等于它的5的倍数。
     *
     * @param approvalVals 包含双精度浮点数的列表，不能为空
     * @return 返回大于等于最大值的最接近5的倍数。若最大值本身是5的倍数则直接返回该值
     */
    public static Double findClosestUpperMultipleOfFive(List<Double> approvalVals) {
        if( CollectionUtils.isEmpty(approvalVals)){
            return null;
        }

        // 获取列表最大值
        double maxValue = Collections.max(approvalVals);

        // 计算不小于最大值的最近5的倍数（向上取整）
        double candidate = Math.ceil(maxValue / 5) * 5;

        return candidate;
    }

    public static void main(String[] args) {
        // 测试示例
        System.out.println(isOnlySpecialCharacters("!@#$%"));    // true
        System.out.println(isOnlySpecialCharacters("abc123"));   // false
        System.out.println(isOnlySpecialCharacters("   "));       // true（全是空格）
        System.out.println(isOnlySpecialCharacters("a@#"));      // false
        System.out.println(isOnlySpecialCharacters(""));         // true（空字符串）
    }

    /**
     * 判断字符串是否仅包含特殊字符
     * @param input 要检查的字符串
     * @return 如果字符串仅包含特殊字符返回true，否则返回false
     */
    public static boolean isOnlySpecialCharacters(String input) {
        if (input == null || input.isEmpty()) {
            return true;
        }

        for (char c : input.toCharArray()) {
            // 检查是否是字母、数字
            if (Character.isLetterOrDigit(c)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断是否整数
     *
     * @param score
     * @return
     */
    public static boolean isNotIntegerScore(Double score) {
        return score != null && score.intValue() != score;
    }

    public static String removeFromMarker(String fileName, String marker) {
        int index = fileName.indexOf(marker);
        return index != -1 ? fileName.substring(0, index) : fileName;
    }
}
