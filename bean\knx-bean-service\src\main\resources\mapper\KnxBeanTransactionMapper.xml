<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.knx.bean.dao.mapper.KnxBeanTransactionMapper">

    <resultMap type="com.knx.bean.model.entity.KnxBeanTransaction" id="knxBeanTransactionMapper">
        <result property="tenantId" column="tenant_id"/>
        <result property="amount" column="amount"/>
        <result property="type" column="type" javaType="Long"/>
        <result property="contractNo" column="contract_no"/>
        <result property="orderNo" column="order_no"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="lastUpdateBy" column="last_update_by"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>
    <select id="selectTransactionOrders" resultType="com.knx.bean.model.entity.KnxBeanTransaction">
        select * from sag_knx_bean_transaction
        <where>
            1=1
            <if test="params.tenantId !=null and params.tenantId !=''">
                and tenant_id = #{params.tenantId}
            </if>
            <if test="params.type !=null and params.type !=''">
                and type = #{params.type}
            </if>
            <if test="params.contractNo !=null and params.contractNo !=''">
                and contract_no = #{params.contractNo}
            </if>
            <if test="params.orderNo !=null and params.orderNo !=''">
                and order_no = #{params.orderNo}
            </if>
            <if test="params.beginTime !=null and params.beginTime !='' and params.endTime !=null and params.endTime !=''">
                and create_time BETWEEN to_date(#{params.beginTime}, 'YYYY-MM-DD') AND to_date(#{params.endTime},
                'YYYY-MM-DD')+1
            </if>
        </where>
    </select>

</mapper>
