package com.knx.feign.survey.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@ApiModel("MarketingActivityPurchaseRecordCreateReqVo")
@Data
@FieldNameConstants
public class MarketingActivityPurchaseRecordCreateReqVo implements Serializable {

    @ApiModelProperty(value = "营销活动ID")
    @NotBlank(message = "marketingActivityId不能为空")
    private String marketingActivityId;

    @ApiModelProperty(value = "租户ID")
    @NotBlank(message = "marketingActivityId不能为空")
    private String tenantId;

    @ApiModelProperty(value = "活动ID")
    @NotBlank(message = "projectId不能为空")
    private String projectId;

    @ApiModelProperty(value = "报告人员ID")
    @NotBlank(message = "reportPersonId不能为空")
    private String reportPersonId;
}
