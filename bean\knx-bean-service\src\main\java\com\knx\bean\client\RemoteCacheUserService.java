package com.knx.bean.client;

import com.knx.bean.model.dto.client.SystemUserSimpleVO;
import com.knx.bean.model.dto.client.UserDTO;
import com.knx.common.base.service.ServiceNameConstants;
import com.knx.common.base.web.response.WebResponse;
import com.knx.common.security.FeignFilter;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(contextId = "knxCacheUserService", value = ServiceNameConstants.USER_ACCOUNT_SERVICE, configuration = FeignFilter.class)
public interface RemoteCacheUserService {
    /**
     * 根据userId数组批量获取User数据
     * 已废弃，勿使用
     */
    @PostMapping("/userAccount/sysUser/listUserListByIds")
    @Deprecated
    WebResponse<List<UserDTO>> listUserListByIds(@RequestBody List<String> userIds);

    /**
     * 根据用户ID集查询系统用户的基本信息
     *
     * @param userIds
     * @return
     */
    @ApiOperation(value = "根据用户ID集查询系统用户的基本信息", notes = "根据用户ID集查询系统用户的基本信息")
    @PostMapping("/userAccount/sysUser/getSystemUserByIds")
    WebResponse<List<SystemUserSimpleVO>> getSystemUserByIds(@RequestBody List<String> userIds);

}
