package com.knx.bean.client;

import com.knx.bean.model.dto.client.TenantDTO;
import com.knx.common.base.service.ServiceNameConstants;
import com.knx.common.base.web.response.WebResponse;
import com.knx.common.security.FeignFilter;
import com.knx.survey.client.uerAcount.model.TenantVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@FeignClient(contextId = "knxTenantService", value = ServiceNameConstants.USER_ACCOUNT_SERVICE, configuration = FeignFilter.class)
public interface RemoteTenantService {

    @ApiOperation(value = "批量查询租户信息接口", notes = "批量查询租户信息接口")
    @PostMapping("/knx/tenant/batchListByIds")
    WebResponse<List<TenantDTO>> knxTenantBatchListByIds(@Valid @RequestBody List<String> ids);

    @ApiOperation(value = "按名字模糊查询", notes = "按名字模糊查询")
    @GetMapping("/knx/tenant/listLikeName")
    WebResponse<List<TenantDTO>> listLikeName(@RequestParam("name") String name);

    @ApiOperation(value = "按照主键id查询顾问数据", notes = "按照主键id查询顾问数据")
    @GetMapping("/knx/tenant/get/{id}")
    WebResponse<TenantVO> get(@PathVariable("id") String id);
}
