package com.knx.survey.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PersonIdEncUtil.java
 * @Description TODO
 * @createTime 2023年09月06日 16:45:00
 */
@Slf4j
public class PersonIdEncUtil {

    private static final String SECRET = "z%Oy4KUK";

    /**
     * 加密
     * @param personId
     * @return
     */
    public static String encryptAndUrlEncode(String personId) {
        if (StringUtils.isBlank(personId)) {
            return null;
        }
        String encMsg = null;
        try {
            encMsg = AesEncryption.encrypt(personId, SECRET);
        } catch (Exception e) {
            log.warn("PersonIdEncUtil encryptAndUrlEncode fail "+personId,e);
        }
        return encMsg;
    }

    // 解密方法
    public static String decrypt(String personId) {
        if (StringUtils.isBlank(personId)) {
            return null;
        }
        String realPersonId = null;
        try {
            realPersonId = AesEncryption.decrypt(personId, SECRET);
        } catch (Exception e) {
            log.warn("PersonIdEncUtil decrypt fail" + personId, e);
        }
        return realPersonId;
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
//        String message = "1699246776541294593";
//        String encMsg = PersonIdEncUtil.encryptAndUrlEncode(message);
//        System.out.println(encMsg);
        System.out.println(PersonIdEncUtil.decrypt("59e46974889eb307a118521f2acb6ce4d85cab4c6ca74465b16cadfb52f0b6d5"));
    }
}
