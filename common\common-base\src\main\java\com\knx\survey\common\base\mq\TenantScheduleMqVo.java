package com.knx.survey.common.base.mq;

import com.knx.survey.model.common.other.TenantScheduleParam;
import com.knx.survey.model.enums.TenantScheduleStatusEnum;
import com.knx.survey.model.enums.TenantScheduleTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.util.Date;


@Data
@FieldNameConstants
@ApiModel("租户任务消息实体")
public class TenantScheduleMqVo {

    /**
     * id
     */
    private String id;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 任务类型
     */
    private TenantScheduleTypeEnum type;

    /**
     * 任务状态
     */
    private TenantScheduleStatusEnum status;

    /**
     * 任务参数
     */
    private TenantScheduleParam param;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 任务重试,重置任务状态为新建
     */
    private Boolean retry;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 实例信息
     */
    private String instance;
}
