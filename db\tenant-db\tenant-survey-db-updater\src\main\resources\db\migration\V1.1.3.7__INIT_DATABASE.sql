/*常模*/
DROP TABLE IF EXISTS  prisma_norm;
CREATE TABLE prisma_norm
(
    "id"                                     VARCHAR(36)  NOT NULL,
    "name"                                   json,
    "year"                                   VARCHAR(10) NULL,
    "industry"                               VARCHAR(50) NULL,
    "quantile"                               VARCHAR(20) NULL,
    "code"                                   VARCHAR(36)  NOT NULL,
    "amount"                                 decimal ,
    "standard_questionnaire_id"              VARCHAR(36) NULL,
    "is_free"                                BOOL         DEFAULT false,
    "is_buy"                                 BOOL         DEFAULT false,
    "create_by"                              VARCHAR(36),
    "create_time"                            TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                            TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                         VARCHAR(36),
    "is_deleted"                             BOOL         DEFAULT false,
    "app_id"                                 VARCHAR(36),
    CONSTRAINT  "___pk___prisma_norm___" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."prisma_norm" IS '常模表';
COMMENT ON COLUMN "public"."prisma_norm"."code" IS '常模编码';
COMMENT ON COLUMN "public"."prisma_norm"."amount" IS '常模金额';
COMMENT ON COLUMN "public"."prisma_norm"."standard_questionnaire_id" IS '标准问卷id';
COMMENT ON COLUMN "public"."prisma_norm"."is_free" IS '是否免费';
COMMENT ON COLUMN "public"."prisma_norm"."is_buy" IS '是否购买';
COMMENT ON COLUMN "public"."prisma_norm"."year" IS '年份';
COMMENT ON COLUMN "public"."prisma_norm"."industry" IS '行业';
COMMENT ON COLUMN "public"."prisma_norm"."quantile" IS '分位';


/*常模问题mapping*/
DROP TABLE IF EXISTS norm_question_mapping;
CREATE TABLE norm_question_mapping
(
    "id"                                     VARCHAR(36)  NOT NULL,
    "norm_id"                                VARCHAR(36)  NOT NULL,
    "question_code"                          VARCHAR(36)  NOT NULL,
    "value"                                  numeric(53,2)  NULL,
    "create_by"                              VARCHAR(36),
    "create_time"                            TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                            TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                         VARCHAR(36),
    "is_deleted"                             BOOL         DEFAULT false,
    "app_id"                                 VARCHAR(36),
    CONSTRAINT  "___pk___norm_question_mapping___" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."norm_question_mapping" IS '常模题目表';
COMMENT ON COLUMN "public"."norm_question_mapping"."norm_id" IS '常模id';
COMMENT ON COLUMN "public"."norm_question_mapping"."question_code" IS '问题code';
COMMENT ON COLUMN "public"."norm_question_mapping"."value" IS '常模值';