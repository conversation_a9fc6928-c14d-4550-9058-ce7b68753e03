package com.knx.bean.mq;

import com.alibaba.fastjson.JSONObject;
import com.knx.common.base.message.KnxMessage;
import com.knx.survey.common.base.mq.KnxMessageBuilder;
import com.knx.survey.common.base.mq.MqConstant;
import com.knx.survey.common.base.mq.SurveyMqPayload;
import com.knx.survey.model.enums.MqPurposeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MqProducer {
    @Autowired
    private KafkaConfigProperties configProperties;

    @Autowired
    private KafkaTemplate<String, String> template;

    public void sendTenantBeanChargeMessage(String tenantId) {
        JSONObject params = new JSONObject();
        params.put("mqPurpose", MqPurposeEnum.TENANT_BEAN_RECHARGE);
        doSend(MqConstant.SURVEY_SERVICE_GENERAL_TOPIC, KnxMessageBuilder.buildWithLogin(tenantId, params));
    }


    private void doSend(String topic, KnxMessage message) {
        SurveyMqPayload payload = (SurveyMqPayload) message.getPayload();
        log.info("groupId={} topic={} id={} tenantId={} payload={} enqueue", configProperties.getGroupId(), topic, message.getHeader().getId(), payload.getTenantId(), JSONObject.toJSONString(message.getPayload()));
        template.send(topic, JSONObject.toJSONString(message));
    }
}
