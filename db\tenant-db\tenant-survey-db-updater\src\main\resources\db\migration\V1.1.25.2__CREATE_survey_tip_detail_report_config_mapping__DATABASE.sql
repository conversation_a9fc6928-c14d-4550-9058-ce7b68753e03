DROP TABLE IF EXISTS "public"."survey_tip_detail_report_config_mapping";
CREATE TABLE survey_tip_detail_report_config_mapping (
	id varchar(36) NOT NULL,
    questionnaire_id varchar(50) NOT NULL,
    tip_report_template_id varchar(36) NOT NULL,
    detail_report_template_id varchar(36) NOT NULL,
    report_type varchar(50) NOT NULL,
    report_style varchar(50) NULL,
    product_type varchar(50) NULL,
    is_used bool NULL DEFAULT false,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__survey_tip_detail_report_config_mapping___" PRIMARY KEY (id)
);
COMMENT ON TABLE  "public"."survey_tip_detail_report_config_mapping" IS 'tip细分报告样式和tip报告mapping';
COMMENT ON COLUMN "public"."survey_tip_detail_report_config_mapping"."questionnaire_id" IS '问卷id';
COMMENT ON COLUMN "public"."survey_tip_detail_report_config_mapping"."tip_report_template_id" IS 'tip报告模板id';
COMMENT ON COLUMN "public"."survey_tip_detail_report_config_mapping"."detail_report_template_id" IS '细分报告模板id';
COMMENT ON COLUMN "public"."survey_tip_detail_report_config_mapping"."report_type" IS '细分报告类型';
COMMENT ON COLUMN "public"."survey_tip_detail_report_config_mapping"."report_style" IS '细分报告样式';
COMMENT ON COLUMN "public"."survey_tip_detail_report_config_mapping"."product_type" IS '产品类型';
COMMENT ON COLUMN "public"."survey_tip_detail_report_config_mapping"."is_used" IS '是否使用';