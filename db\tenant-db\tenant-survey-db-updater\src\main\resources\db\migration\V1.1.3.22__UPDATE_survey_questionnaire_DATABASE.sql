-- 替换换行符
update survey_questionnaire set answer_description = replace(answer_description ,CHR(10), '') where answer_description is not null;
-- 替换回车符
update survey_questionnaire set answer_description = replace(answer_description,CHR(13), '') where answer_description is not null;

update survey_questionnaire set answer_description= replace(answer_description, '{"en_US":"","zh_CN":"', '')  where answer_description is not null;
update survey_questionnaire set answer_description= replace(answer_description, '"}', '')  where answer_description is not null;

update survey_questionnaire set answer_description= replace(answer_description, '"', '\"')  where answer_description is not null;

update survey_questionnaire set answer_description = concat('{"en_US":"","zh_CN":"', answer_description, '"}') where answer_description is not null and position('zh_CN' in answer_description) = 0;