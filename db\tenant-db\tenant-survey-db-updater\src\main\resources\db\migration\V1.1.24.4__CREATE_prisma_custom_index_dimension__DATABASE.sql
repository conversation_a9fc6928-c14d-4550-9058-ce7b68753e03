CREATE TABLE public.prisma_custom_index_dimension (
	id varchar(36) NOT NULL,
	project_id varchar(36) NOT NULL,
	questionnaire_id varchar(36) NOT NULL,
	"type" varchar(50) NOT NULL,
	"name" json NOT NULL,
	create_by varchar(36) NOT NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) NULL,
	CONSTRAINT "___pk___prisma_custom_index_dimension___" PRIMARY KEY (id)
);
COMMENT ON TABLE  "public"."prisma_custom_index_dimension" IS 'prisma自定义指数维度定义表';
COMMENT ON COLUMN "public"."prisma_custom_index_dimension"."project_id" IS '活动ID';
COMMENT ON COLUMN "public"."prisma_custom_index_dimension"."questionnaire_id" IS '问卷ID';
COMMENT ON COLUMN "public"."prisma_custom_index_dimension"."type" IS '类型';
COMMENT ON COLUMN "public"."prisma_custom_index_dimension"."name" IS '名称';