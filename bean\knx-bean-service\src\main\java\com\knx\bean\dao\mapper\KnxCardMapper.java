package com.knx.bean.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knx.bean.model.entity.KnxCard;
import com.knx.bean.model.vo.CardSelectVO;
import com.knx.common.base.web.response.PageRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/10/16 13:39
 */
public interface KnxCardMapper extends BaseMapper<KnxCard> {


    PageRequest pageList(IPage page, @Param("cardSelect") CardSelectVO cardSelectVO);

    List<KnxCard> listKnxCard(@Param("cardSelect") CardSelectVO cardSelectVO);

    Integer updateBatchByPassword(@Param("knxCards") List<KnxCard> knxCards);

    Integer updateRechargeBatchByPassword(@Param("knxCards") List<KnxCard> knxCards);


}
