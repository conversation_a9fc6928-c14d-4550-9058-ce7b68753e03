DROP TABLE IF EXISTS "public"."tenant_warning_log";
CREATE TABLE "public"."tenant_warning_log" (
        "id" varchar(36)  NOT NULL,
        "tenant_id" varchar(36) NULL,
        "type" varchar(50) NULL,
        "action_time" timestamp(6),
        "description" text NULL,
        "is_valid" bool DEFAULT true,
        "create_by" varchar(36) NULL,
        "create_time" timestamp(6) NULL DEFAULT now(),
        "update_time" timestamp(6) NULL DEFAULT now(),
        "last_update_by" varchar(36) NULL,
        "is_deleted" bool NULL DEFAULT false,
        "app_id" varchar(36) NULL,
        CONSTRAINT "___pk__tenant_warning_log___" PRIMARY KEY (id)
)
;
COMMENT ON COLUMN "public"."tenant_warning_log"."id" IS '主键';
COMMENT ON COLUMN "public"."tenant_warning_log"."tenant_id" IS '租户Id';
COMMENT ON COLUMN "public"."tenant_warning_log"."type" IS '警告类别';
COMMENT ON COLUMN "public"."tenant_warning_log"."action_time" IS '发生时间';
COMMENT ON COLUMN "public"."tenant_warning_log"."description" IS '描述';
COMMENT ON COLUMN "public"."tenant_warning_log"."is_valid" IS '是否有效';
COMMENT ON TABLE  "public"."tenant_warning_log" IS '租户警示信息表';
