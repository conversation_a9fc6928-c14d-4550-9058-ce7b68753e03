DROP TABLE IF EXISTS "public"."carousel";
CREATE TABLE carousel (
	id varchar(36) NOT NULL,
	name varchar(50) NULL,
	file_url varchar(36) NULL,
	status varchar(50) NULL DEFAULT 'ENABLE',
	sort int4 default 999,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__carousel___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."carousel"."name" IS '名称';
COMMENT ON COLUMN "public"."carousel"."file_url" IS '图片地址';
COMMENT ON COLUMN "public"."carousel"."status" IS '状态';
COMMENT ON COLUMN "public"."carousel"."sort" IS '排序号';
COMMENT ON TABLE  "public"."carousel" IS '轮播图表';
