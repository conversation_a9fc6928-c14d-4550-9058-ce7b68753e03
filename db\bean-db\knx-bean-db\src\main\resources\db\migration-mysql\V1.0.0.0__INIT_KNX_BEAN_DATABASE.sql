-- ----------------------------
-- Table structure for knx_bean_account
-- ----------------------------
/*租户余额信息*/
DROP TABLE IF EXISTS `knx_bean_account`;
CREATE TABLE `knx_bean_account`
(
    `id`             VARCHAR(36) NOT NULL,
    `tenant_id`      VARCHAR(36) NOT NULL COMMENT '租户id',
    `balance`        INT4 COMMENT '肯豆余额',
    `status`         VARCHAR(50) COMMENT '账户状态，0 可用 1 禁用',
    `expired_date`   TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '肯豆账户过期时间',
    `create_by`      VARCHAR(36),
    `create_time`    TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`    TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `last_update_by` VA<PERSON><PERSON><PERSON>(36),
    `is_deleted`     BOOL         DEFAULT false,
    `app_id`         VA<PERSON>HA<PERSON>(36),
    CONSTRAINT `___pk___knx_bean_account___` PRIMARY KEY (id)
);

-- ----------------------------
-- Table structure for knx_bean_transaction
-- ----------------------------
/*租户账户流水信息表*/
DROP TABLE IF EXISTS `knx_bean_transaction`;
CREATE TABLE `knx_bean_transaction`
(
    `id`              VARCHAR(36) NOT NULL,
    `tenant_id`       VARCHAR(36) NOT NULL COMMENT '租户id',
    `amount`          INT4 COMMENT '租户消费或者充值数量',
    `type`            VARCHAR(50) COMMENT '类型 0 充值 1 取消充值 2 创建活动 3 查看报告 4 下载报告',
    `contract_no`     VARCHAR(100) COMMENT '合同编号，类型为充值时，这个字段才有用',
    `order_no`        VARCHAR(100) COMMENT '订单号',
    `contract_amount` INT4 COMMENT '合同金额',
    `balance`         INT4 COMMENT '租户余额',
    `status`          VARCHAR(50) COMMENT '充值的流水明细取消和执行',
    `create_by`       VARCHAR(36),
    `create_time`    TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`    TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `last_update_by`  VARCHAR(36),
    `is_deleted`      BOOL         DEFAULT false,
    `app_id`          VARCHAR(36),
    CONSTRAINT  `___pk__knx_bean_transaction___` PRIMARY KEY (`id`)
);