package com.knx.survey.model.report.prisma.tencent;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.knx.common.base.dao.typehandler.ConvertJsonAndObjectTypeHandler;
import com.knx.survey.model.enums.MainlandEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 实体类 prisma_organization_demographic_approval_data 用于映射SQL表
 * <AUTHOR>
 */
@Data
@FieldNameConstants
@TableName(value = "prisma_organization_demographic_approval_data", autoResultMap = true)
public class PrismaOrganizationDemographicApprovalData implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(
            type = IdType.ASSIGN_ID
    )
    @ApiModelProperty("唯一标识符")
    private String id;

    @ApiModelProperty("项目的唯一标识符")
    private String projectId;

    @ApiModelProperty("数据所属的发布年份")
    private String releaseYear;

    @ApiModelProperty("组织代码")
    private String organization;

    @ApiModelProperty("历史数据的唯一标识符")
    private String historyCode;

    @ApiModelProperty("数据覆盖范围 (all/mainland/non-mainland)")
    private MainlandEnum mainlandType;

    @ApiModelProperty("参与人数")
    private Integer participantCount;

    @ApiModelProperty("有效应答人数")
    private Integer validRespondentCount;

    @ApiModelProperty("常模代码列表（存储为JSON格式）")
    @TableField(typeHandler = ConvertJsonAndObjectTypeHandler.class)
    private List<String> normCodes;

    @ApiModelProperty("赞成度数据")
    @TableField(typeHandler = ConvertJsonAndObjectTypeHandler.class)
    private List<ApprovalQuestionDimensionDetail> data;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("数据条目是否已逻辑删除（适用于软删除）")
    private Boolean isDeleted;
}
