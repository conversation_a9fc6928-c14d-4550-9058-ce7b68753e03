
DROP TABLE IF EXISTS survey_person_investigator_mapping;
CREATE TABLE survey_person_investigator_mapping
(

    "person_id"                       VARCHAR(36)       NOT NULL,
    "investigator_id"                 VARCHAR(36)       NOT NULL,
    "role_id"                         VARCHAR(36)       NOT NULL,
    "weights"                         INT               NOT NULL,
    CONSTRAINT  "___pk___survey_person_investigator_mapping___" PRIMARY KEY ("person_id","investigator_id")
);
COMMENT ON COLUMN "public"."survey_person_investigator_mapping"."person_id" IS '人员id';
COMMENT ON COLUMN "public"."survey_person_investigator_mapping"."investigator_id" IS '调查者id';
COMMENT ON COLUMN "public"."survey_person_investigator_mapping"."role_id" IS '角色id';
COMMENT ON COLUMN "public"."survey_person_investigator_mapping"."weights" IS '权重';

DROP TABLE IF EXISTS survey_role;
CREATE TABLE survey_role
(
    "id"                              VARCHAR(36)        NOT NULL,
    "project_id"                      VARCHAR(36),
    "name"                            VARCHAR(100),
    "investigator_title"              VARCHAR(100),
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3)      DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3)      DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL              DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT  "___pk___survey_role___" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."survey_role" IS '角色表';
COMMENT ON COLUMN "public"."survey_role"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_role"."name" IS '角色名';
COMMENT ON COLUMN "public"."survey_role"."investigator_title" IS '该角色对调查者的称呼';

DROP TABLE IF EXISTS survey_investigator;
CREATE TABLE survey_investigator
(
    "id"                              VARCHAR(36)        NOT NULL,
    "project_id"                      VARCHAR(36),
    "name"                            VARCHAR(100),
    "email"                           VARCHAR(100),
    "phone"                           VARCHAR(100),
    "is_self_evaluation"              BOOL,
    "person_id"                       VARCHAR(36),
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3)      DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3)      DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL              DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT  "___pk___survey_investigator___" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."survey_investigator" IS '调查者表';
COMMENT ON COLUMN "public"."survey_investigator"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_investigator"."name" IS '调查者名';
COMMENT ON COLUMN "public"."survey_investigator"."email" IS '邮箱';
COMMENT ON COLUMN "public"."survey_investigator"."phone" IS '手机';
COMMENT ON COLUMN "public"."survey_investigator"."is_self_evaluation" IS '是否自评';
COMMENT ON COLUMN "public"."survey_investigator"."person_id" IS '自评时自己的评测人员id';