package com.knx.bean.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.knx.bean.model.enums.KnxBeanTransactionStatusEnum;
import com.knx.bean.model.enums.KnxBeanTransactionTypeEnum;
import com.knx.survey.common.util.excelUtil.anno.EnumFormat;
import com.knx.survey.common.util.excelUtil.converter.EnumExcelConverter;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 肯豆交易记录
 */
@Data
@ApiModel(value = "KnxBeanTransaction")
@FieldNameConstants
public class BeanTransactionExcelVo {

    @ExcelProperty(value = "订单编号", index = 0)
    private String orderNo;

    @ExcelProperty(value = "金额", index = 1)
    private BigDecimal amount;

    @ExcelProperty(value = "类型", index = 2, converter = EnumExcelConverter.class)
    @EnumFormat(value = KnxBeanTransactionTypeEnum.class, name = KnxBeanTransactionTypeEnum.Fields.name)
    private KnxBeanTransactionTypeEnum type;

    @ExcelProperty(value = "内容/合同编号", index = 3)
    private String contractNo;

    @ExcelProperty(value = "合同金额", index = 4)
    private BigDecimal contractAmount;

    @ExcelProperty(value = "租户余额", index = 5)
    private BigDecimal balance;

    @ExcelProperty(value = "状态", index = 6, converter = EnumExcelConverter.class)
    @EnumFormat(value = KnxBeanTransactionStatusEnum.class, name = KnxBeanTransactionStatusEnum.Fields.name)
    private KnxBeanTransactionStatusEnum status;

    @ExcelProperty(value = "管理员", index = 7)
    private String userName;

    @ExcelProperty(value = "创建时间", index = 8)
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date createTime;

    @ExcelProperty(value = "备注", index = 9)
    private String memo;

}
