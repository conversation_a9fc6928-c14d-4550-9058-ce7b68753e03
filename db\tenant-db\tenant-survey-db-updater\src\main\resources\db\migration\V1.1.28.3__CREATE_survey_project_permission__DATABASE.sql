DROP TABLE IF EXISTS "public"."survey_project_permission";
CREATE TABLE survey_project_permission (
                                   id VARCHAR ( 36 ) NOT NULL,
                                   project_id VARCHAR ( 36 ) NOT NULL,
                                   user_id VARCHAR ( 36 ) NOT NULL,
                                   is_creator BOOL DEFAULT FALSE,
                                   is_main_manager BOOL DEFAULT FALSE,
                                   is_view_answer_progress BOOL DEFAULT FALSE,
--                                    answer_progress_organization_ids json DEFAULT NULL,
                                   is_select_organization  BOOL DEFAULT FALSE,
                                   organization_ids json DEFAULT NULL,
                                   is_select_factor  BOOL DEFAULT FALSE,
                                   factor_ids json DEFAULT NULL,
                                   is_select_norm  BOOL DEFAULT FALSE,
--                                    norms json DEFAULT NULL,
                                   is_select_history  BOOL DEFAULT FALSE,
--                                    history_ids json DEFAULT NULL,
                                   create_by VARCHAR ( 36 ) NULL,
                                   create_time TIMESTAMP ( 3 ) NULL DEFAULT now( ),
                                   update_time TIMESTAMP ( 3 ) NULL DEFAULT now( ),
                                   last_update_by VARCHAR ( 36 ) NULL,
                                   is_deleted BO<PERSON> NULL DEFAULT FALSE,
                                   app_id VARCHAR ( 36 ) NULL,
                                   CONSTRAINT "___pk__survey_project_permission___" PRIMARY KEY ( id )
);
COMMENT ON TABLE "public"."survey_project_permission" IS '活动权限表';
COMMENT ON COLUMN "public"."survey_project_permission"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_project_permission"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."survey_project_permission"."is_creator" IS '用户是否是活动的创建者';
COMMENT ON COLUMN "public"."survey_project_permission"."is_main_manager" IS '用户是否是主管理员(仅调研)';
COMMENT ON COLUMN "public"."survey_project_permission"."is_view_answer_progress" IS '是否可以查看填答进度(仅调研)';
COMMENT ON COLUMN "public"."survey_project_permission"."is_select_organization" IS '创建细分报告是否可以选择组织';
COMMENT ON COLUMN "public"."survey_project_permission"."organization_ids" IS '创建细分报可以选择的组织';
COMMENT ON COLUMN "public"."survey_project_permission"."is_select_factor" IS '创建细分报可以选择分析因子';
COMMENT ON COLUMN "public"."survey_project_permission"."factor_ids" IS '创建细分报可以选择的分析因子';
COMMENT ON COLUMN "public"."survey_project_permission"."is_select_norm" IS '创建细分报是否可以选择内外部常模';
COMMENT ON COLUMN "public"."survey_project_permission"."is_select_history" IS '创建细分报是否可以选择历史数据';
