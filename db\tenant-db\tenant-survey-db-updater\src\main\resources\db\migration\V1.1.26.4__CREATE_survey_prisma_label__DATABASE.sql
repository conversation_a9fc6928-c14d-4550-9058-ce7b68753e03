DROP TABLE IF EXISTS "public"."survey_prisma_label";
CREATE TABLE survey_prisma_label (
	id varchar(36) NOT NULL,
	"group" varchar(50) NOT NULL,
    project_id varchar(36) NOT NULL,
    parent_dimension_code varchar(50) NOT NULL,
    name json NOT NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__survey_prisma_label___" PRIMARY KEY (id)
);
COMMENT ON TABLE  "public"."survey_prisma_label" IS 'prisma标签';
COMMENT ON COLUMN "public"."survey_prisma_label"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_prisma_label"."group" IS '类型分组';
COMMENT ON COLUMN "public"."survey_prisma_label"."parent_dimension_code" IS '枚举';
COMMENT ON COLUMN "public"."survey_prisma_label"."name" IS '名称';
