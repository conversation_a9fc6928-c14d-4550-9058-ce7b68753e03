DROP TABLE IF EXISTS survey_project_invite_content_record;
CREATE TABLE survey_project_invite_content_record
(
    "id"                              VARCHAR(36)  NOT NULL,
    "project_id"                      VARCHAR(36)  NOT NULL,
    "message_purpose"                 VARCHAR(50)  NOT NULL,
    "message_type"                    VARCHAR(50)  NOT NULL,
    "answer_code_type"                VARCHAR(50)  DEFAULT NULL,
    "content"                         TEXT DEFAULT NULL,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___survey_project_invite_content_record___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."survey_project_invite_content_record" IS '活动邀请正文记录表';
COMMENT ON COLUMN "public"."survey_project_invite_content_record"."project_id" IS '活动ID';
COMMENT ON COLUMN "public"."survey_project_invite_content_record"."message_purpose" IS '消息用途';
COMMENT ON COLUMN "public"."survey_project_invite_content_record"."message_type" IS '消息类型';
COMMENT ON COLUMN "public"."survey_project_invite_content_record"."answer_code_type" IS '链接类型';
COMMENT ON COLUMN "public"."survey_project_invite_content_record"."content" IS '消息正文';


CREATE INDEX IF NOT EXISTS ___idx_survey_project_invite_content_record_project_id___ ON public.survey_project_invite_content_record(project_id);