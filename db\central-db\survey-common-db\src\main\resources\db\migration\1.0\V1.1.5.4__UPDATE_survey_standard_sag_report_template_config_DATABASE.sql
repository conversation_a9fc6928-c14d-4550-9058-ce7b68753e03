INSERT INTO "survey_standard_sag_report_template" ("id", "name", "code", "sample_url", "type", "standard_report_type", "standard_report_id", "status", "tenant_id", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "report_style", "config_type", "survey_type", "is_enable_job", "model_type", "model_image_url", "min_dimension_num", "max_dimension_num")
VALUES ('1279354877043542347', '{"en_US":"","zh_CN":"人才画像2.0"}', '034', NULL, 'STANDARD', 'TIP_NEW_2', NULL, 'ON', NULL, NULL, NULL, '2021-08-17 09:56:54.543', '1281140300608344066', 'f', NULL, 'TIP_NEW_2_STANDARD', NULL, 'ASSESSMENT', 't', 'DYNAMIC', '', 1, 65);


INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1410185659545969556', '1279354877043542347', 'RESPONSE_STYLE', '{"responseStyleConfig":{"childCode":null,"childCode2":null,"parentDimension":{"en_US":"responseData1.0","zh_CN":"社会称许性"},"parentDimension2":{"en_US":"responseData2.0","zh_CN":"掩饰性"}}}', '1281140300608344066', '2021-08-04 14:24:05', '2021-08-13 14:53:57.025', '1281140300608344066', 'f', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1427123617812054017', '1279354877043542347', 'RESPONSE_STYLE', '{"responseStyleConfig":{"childCode":null,"childCode2":null,"parentDimension":{"en_US":"responseData1.0","zh_CN":"社会称许性"},"parentDimension2":{"en_US":"responseData2.0","zh_CN":"掩饰性"}}}', '1281140300608344066', '2021-08-16 12:22:39.819', '2021-08-16 12:22:39.819', '1281140300608344066', 'f', NULL, '{"zh_CN":"人才画像2.0"}', 'TIP_NEW_2_STANDARD', 'TIP', 'TIP');

INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1427123617837219842', '1279354877043542347', 'PAYMENT', '{"paymentConfig":{"adaptationDetails":0,"appendix":0,"backCover":0,"detailedScore":0,"fit":0,"fitnessLevel":0,"frontCover":1,"overview":0,"policyDecision":0,"preface":0,"responseStyle":0,"total":1,"traitDifference":0}}', '1281140300608344066', '2021-08-16 12:22:39.824', '2021-08-16 12:22:39.824', '1281140300608344066', 'f', NULL, '{"zh_CN":"人才画像2.0"}', 'TIP_NEW_2_STANDARD', 'TIP', 'TIP');
INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1280332187908963547', '1279354877043542347', 'PAYMENT', '{"paymentConfig":{"adaptationDetails":0,"appendix":0,"backCover":0,"detailedScore":0,"fit":0,"fitnessLevel":0,"frontCover":0,"overview":0,"policyDecision":0,"preface":0,"responseStyle":0,"total":0,"traitDifference":0}}', '1281140300608344066', '2021-08-04 14:57:40', '2021-08-09 16:32:48.745', '1281140300608344066', 'f', NULL, NULL, NULL, NULL, NULL);

INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1427123617816248321', '1279354877043542347', 'OVERVIEW', '{"overviewConfig":[]}', '1281140300608344066', '2021-08-16 12:22:39.819', '2021-08-16 12:22:39.819', '1281140300608344066', 'f', NULL, '{"zh_CN":"人才画像2.0"}', 'TIP_NEW_2_STANDARD', 'TIP', 'TIP');
INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1321561534165412158', '1279354877043542347', 'OVERVIEW', '{"overviewConfig":[]}', '1281140300608344066', '2021-08-14 09:58:09.695', '2021-08-13 17:24:14.562', '1281140300608344066', 'f', NULL, NULL, NULL, NULL, NULL);

INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1427123617833025538', '1279354877043542347', 'FRONT_COVER', '{"frontCoverConfig":{"names":["NAME","ANSWER_DATE"]}}', '1281140300608344066', '2021-08-16 12:22:39.824', '2021-08-16 12:22:39.824', '1281140300608344066', 'f', NULL, '{"zh_CN":"人才画像2.0"}', 'TIP_NEW_2_STANDARD', 'TIP', 'TIP');
INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1444588555555512666', '1279354877043542347', 'FRONT_COVER', '{"frontCoverConfig":{"names":["NAME","ANSWER_DATE"]}}', '1281140301401067521', '2021-08-04 13:22:25', '2021-08-04 16:32:01', '1281140301401067521', 'f', NULL, NULL, NULL, NULL, NULL);

INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1312585954366556667', '1279354877043542347', 'FIT', '{"fitConfig":{"parentDimension":{"en_US":"","zh_CN":""}}}', '1281140300608344066', '2021-08-14 09:58:09.695', '2021-08-10 15:18:22.134', '1281140300608344066', 'f', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1427123617723973633', '1279354877043542347', 'FIT', '{"fitConfig":{"parentDimension":{"en_US":"","zh_CN":""}}}', '1281140300608344066', '2021-08-16 12:22:39.8', '2021-08-16 12:22:39.8', '1281140300608344066', 'f', NULL, '{"zh_CN":"人才画像2.0"}', 'TIP_NEW_2_STANDARD', 'TIP', 'TIP');

INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1425747634752192514', '1279354877043542347', 'DIRECTORY', '{"directoryConfig":{"directory":[],"directoryStrings":[],"groupDirectory":[],"groupDirectoryStrings":[]}}', '1281140301401067521', '2021-08-12 17:14:59.897', '2021-08-13 10:43:42.957', '1281140300608344066', 'f', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1425747634752192519', '1279354877043542347', 'DIRECTORY', '{"directoryConfig":{"directory":[],"directoryStrings":[],"groupDirectory":[],"groupDirectoryStrings":[]}}', '1281140301401067521', '2021-08-12 17:14:59.897', '2021-08-13 10:43:42.957', '1281140300608344066', 'f', NULL, '{"zh_CN":"人才画像2.0"}', 'TIP_NEW_2_STANDARD', 'TIP', 'TIP');


INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1427123617820442626', '1279354877043542347', 'DETAILED_SCORE', '{"detailedScoreConfig":[{"detailedScoreChildDimensions":[],"isSelect":true,"name":{"zh_CN":"领导力"},"parentDimension":"LEAD","status":"ENABLE","theoryType":"CAN"},{"detailedScoreChildDimensions":[],"isSelect":true,"name":{"zh_CN":"胜任力"},"parentDimension":"COMPETENCY","status":"ENABLE","theoryType":"CAN"},{"detailedScoreChildDimensions":[],"isSelect":true,"name":{"zh_CN":"认知能力"},"parentDimension":"COGNITIVE_ABILITY","status":"ENABLE","theoryType":"CAN"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"成就动机/自主"},"parentDimension":"ACHIEVEMENT_MOTIVATION_AUTONOMY","status":"ENABLE","theoryType":"WANT"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"成就动机/雄心"},"parentDimension":"ACHIEVEMENT_MOTIVATION_AMBITION","status":"ENABLE","theoryType":"WANT"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"工作价值观"},"parentDimension":"WORK_VALUES","status":"ENABLE","theoryType":"WANT"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"职业性格"},"parentDimension":"PROFESSIONAL_CHARACTER","status":"ENABLE","theoryType":"SUITABLE"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"心理危机"},"parentDimension":"PSYCHOLOGICAL_CRISIS","status":"ENABLE","theoryType":"SUITABLE"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"DISC性格类型/真实的"},"parentDimension":"DISC_PERSONALITY_TYPE_REAL","status":"ENABLE","theoryType":"SUITABLE"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"DISC性格类型/展现的"},"parentDimension":"DISC_PERSONALITY_TYPE_DISPLAY","status":"ENABLE","theoryType":"SUITABLE"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"MBTI性格类型"},"parentDimension":"MBTI_PERSONALITY_TYPE","status":"ENABLE","theoryType":"SUITABLE"}]}', '1281140300608344066', '2021-08-16 12:22:39.821', '2021-08-16 13:42:01.368', '1281140300608344066', 'f', NULL, '{"zh_CN":"人才画像2.0"}', 'TIP_NEW_2_STANDARD', 'TIP', 'TIP');
INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1445544151545415151', '1279354877043542347', 'DETAILED_SCORE', '{"detailedScoreConfig":[{"detailedScoreChildDimensions":[],"isSelect":true,"name":{"zh_CN":"领导力"},"parentDimension":"LEAD","status":"ENABLE","theoryType":"CAN"},{"detailedScoreChildDimensions":[],"isSelect":true,"name":{"zh_CN":"胜任力"},"parentDimension":"COMPETENCY","status":"ENABLE","theoryType":"CAN"},{"detailedScoreChildDimensions":[],"isSelect":true,"name":{"zh_CN":"认知能力"},"parentDimension":"COGNITIVE_ABILITY","status":"ENABLE","theoryType":"CAN"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"成就动机/自主"},"parentDimension":"ACHIEVEMENT_MOTIVATION_AUTONOMY","status":"ENABLE","theoryType":"WANT"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"成就动机/雄心"},"parentDimension":"ACHIEVEMENT_MOTIVATION_AMBITION","status":"ENABLE","theoryType":"WANT"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"工作价值观"},"parentDimension":"WORK_VALUES","status":"ENABLE","theoryType":"WANT"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"职业性格"},"parentDimension":"PROFESSIONAL_CHARACTER","status":"ENABLE","theoryType":"SUITABLE"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"心理危机"},"parentDimension":"PSYCHOLOGICAL_CRISIS","status":"ENABLE","theoryType":"SUITABLE"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"DISC性格类型/真实的"},"parentDimension":"DISC_PERSONALITY_TYPE_REAL","status":"ENABLE","theoryType":"SUITABLE"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"DISC性格类型/展现的"},"parentDimension":"DISC_PERSONALITY_TYPE_DISPLAY","status":"ENABLE","theoryType":"SUITABLE"},{"detailedScoreChildDimensions":[],"isSelect":false,"name":{"zh_CN":"MBTI性格类型"},"parentDimension":"MBTI_PERSONALITY_TYPE","status":"ENABLE","theoryType":"SUITABLE"}]}', '1281140300608344066', '2021-08-04 19:42:50', '2021-08-16 13:42:01.373', '1281140300608344066', 'f', NULL, NULL, NULL, NULL, NULL);

INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1427123617749139458', '1279354877043542347', 'ADAPTATION_DETAILS', '{"adaptationDetailsConfig":[{"adaptationDetailsChildDimension":[],"parentDimension":{"zh_CN":"工作价值观"},"parentDimensionCode":"WORK_VALUES","parentDimensionDescription":{},"reportTemplateJobId":"1424645810326470658","score":0.0,"type":"NOT"},{"adaptationDetailsChildDimension":[],"parentDimension":{"zh_CN":"成就动机/自主"},"parentDimensionCode":"ACHIEVEMENT_MOTIVATION_AUTONOMY","parentDimensionDescription":{},"reportTemplateJobId":"1424645810326470658","score":0.0,"type":"NOT"},{"adaptationDetailsChildDimension":[],"parentDimension":{"zh_CN":"成就动机/雄心"},"parentDimensionCode":"ACHIEVEMENT_MOTIVATION_AMBITION","parentDimensionDescription":{},"reportTemplateJobId":"1424645810326470658","score":0.0,"type":"NOT"},{"adaptationDetailsChildDimension":[],"parentDimension":{"zh_CN":"职业性格"},"parentDimensionCode":"PROFESSIONAL_CHARACTER","parentDimensionDescription":{},"reportTemplateJobId":"1424645810326470658","score":0.0,"type":"NOT"},{"adaptationDetailsChildDimension":[],"parentDimension":{"zh_CN":"认知能力"},"parentDimensionCode":"COGNITIVE_ABILITY","parentDimensionDescription":{},"reportTemplateJobId":"1424645810326470658","score":0.0,"type":"NOT"},{"adaptationDetailsChildDimension":[],"parentDimension":{"zh_CN":"胜任力"},"parentDimensionCode":"COMPETENCY","parentDimensionDescription":{},"reportTemplateJobId":"1424645810326470658","score":0.0,"type":"NOT"},{"adaptationDetailsChildDimension":[],"parentDimension":{"zh_CN":"领导力"},"parentDimensionCode":"LEAD","parentDimensionDescription":{},"reportTemplateJobId":"1424645810326470658","score":0.0,"type":"NOT"}]}', '1281140300608344066', '2021-08-16 12:22:39.803', '2021-08-16 16:58:36.049', '1281140301401067521', 'f', NULL, '{"zh_CN":"人才画像2.0"}', 'TIP_NEW_2_STANDARD', 'TIP', 'TIP');
INSERT INTO "survey_standard_sag_report_template_config" ("id", "report_template_id", "type", "data", "create_by", "create_time", "update_time", "last_update_by", "is_deleted", "app_id", "name", "style", "dimension_config_type", "dimension_score_config_type")
VALUES ('1415515188513625666', '1279354877043542347', 'ADAPTATION_DETAILS', '{"adaptationDetailsConfig":[{"adaptationDetailsChildDimension":[],"parentDimension":{"zh_CN":"工作价值观"},"parentDimensionCode":"WORK_VALUES","parentDimensionDescription":{},"reportTemplateJobId":"1424645810326470658","score":0.0,"type":"NOT"},{"adaptationDetailsChildDimension":[],"parentDimension":{"zh_CN":"成就动机/自主"},"parentDimensionCode":"ACHIEVEMENT_MOTIVATION_AUTONOMY","parentDimensionDescription":{},"reportTemplateJobId":"1424645810326470658","score":0.0,"type":"NOT"},{"adaptationDetailsChildDimension":[],"parentDimension":{"zh_CN":"成就动机/雄心"},"parentDimensionCode":"ACHIEVEMENT_MOTIVATION_AMBITION","parentDimensionDescription":{},"reportTemplateJobId":"1424645810326470658","score":0.0,"type":"NOT"},{"adaptationDetailsChildDimension":[],"parentDimension":{"zh_CN":"职业性格"},"parentDimensionCode":"PROFESSIONAL_CHARACTER","parentDimensionDescription":{},"reportTemplateJobId":"1424645810326470658","score":0.0,"type":"NOT"},{"adaptationDetailsChildDimension":[],"parentDimension":{"zh_CN":"认知能力"},"parentDimensionCode":"COGNITIVE_ABILITY","parentDimensionDescription":{},"reportTemplateJobId":"1424645810326470658","score":0.0,"type":"NOT"},{"adaptationDetailsChildDimension":[],"parentDimension":{"zh_CN":"胜任力"},"parentDimensionCode":"COMPETENCY","parentDimensionDescription":{},"reportTemplateJobId":"1424645810326470658","score":0.0,"type":"NOT"},{"adaptationDetailsChildDimension":[],"parentDimension":{"zh_CN":"领导力"},"parentDimensionCode":"LEAD","parentDimensionDescription":{},"reportTemplateJobId":"1424645810326470658","score":0.0,"type":"NOT"}]}', '1281140300608344066', '2021-08-04 14:57:40', '2021-08-16 16:58:36.057', '1281140301401067521', 'f', '1284443709327511553', NULL, NULL, NULL, NULL);