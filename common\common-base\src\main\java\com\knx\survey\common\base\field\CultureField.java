package com.knx.survey.common.base.field;

import com.knx.survey.model.I18NString;
import com.knx.survey.model.SagI18NString;

public class CultureField {
    public static I18NString NAME_NAME = new SagI18NString("名称", "Name");
    public static I18NString PERSON_NUMBER_NAME = new SagI18NString("人数", "Number");
    public static I18NString DATE_NAME = new SagI18NString("调研时间", "Date");
    public static I18NString INVITE_NUMBER_NAME = new SagI18NString("邀请人数", "Invite Number");
    public static I18NString VALID_NUMBER_NAME = new SagI18NString("有效填答人数", "Valid Number");
    public static I18NString VALID_RATE_NAME = new SagI18NString("有效填答率", "Valid Rate");
    public static I18NString FINISH_ANSWER_NUMBER_NAME = new SagI18NString("应答数", "Finish Number");
    public static I18NString VALID_ANSWER_NUMBER_NAME = new SagI18NString("有效应答数", "Valid Number");
    public static I18NString VALID_ANSWER_RATE_NAME = new SagI18NString("有效应答率", "Valid Rate");
    public static I18NString PERCENT_NAME = new SagI18NString("占比", "Percent");
    public static I18NString INVALID_RATE_NAME = new SagI18NString("无效填答率", "Valid Rate");
    public static I18NString APPROVAL_NAME = new SagI18NString("赞成百分比", "Approval");
    public static I18NString PROTOTYPE_NAME = new SagI18NString("原型", "Prototype");
    public static I18NString SCORE_NAME = new SagI18NString("得分", "Approval");
    public static I18NString MOTIVATION_SYSTEM_NAME = new SagI18NString("动机体系", "");
    public static I18NString HIGH_PROTOTYPE_NAME = new SagI18NString("较高原型", "");
    public static I18NString PROTOTYPE_DESCRIPTION_NAME = new SagI18NString("原型描述", "");
    public static I18NString ADVANTAGE_NAME = new SagI18NString("优势", "");
    public static I18NString RISK_NAME = new SagI18NString("风险", "");
    public static I18NString QUESTION_NAME = new SagI18NString("题目", "Question");
    public static I18NString BEHAVIOUR_THINKING_SUB_NAME = new SagI18NString("分差(行为-思维)", "");

    public static I18NString CATEGORY_NAME = new SagI18NString("类别", "");

    public static I18NString MANAGER_LEVEL_NAME = new SagI18NString("员工分类", "");
    public static I18NString MANAGER_NAME = new SagI18NString("管理层", "");
    public static I18NString NOT_MANAGER_NAME = new SagI18NString("非管理层", "");
    public static I18NString QUESTION_SCORE_NAME = new SagI18NString("题目得分", "");
    public static I18NString D_VALUE_NAME = new SagI18NString("差值", "");
    public static I18NString ORGANIZATIONAL_STRUCTURE_NAME = new SagI18NString("组织架构", "");
    public static I18NString DEMOGRAPHIC_NAME = new SagI18NString("人口标签", "");
    public static I18NString DEMOGRAPHIC_ITEM_NAME = new SagI18NString("人口标签项目", "");
    public static I18NString EXPECTATION_CULTURE_NAME = new SagI18NString("期望文化", "");
    public static I18NString ORGANIZATION_THINKING_NAME = new SagI18NString("组织思维", "");
    public static I18NString ORGANIZATION_BEHAVIOUR_NAME = new SagI18NString("组织行为", "");

    /* 12 文化原型 */
    public static I18NString PROTOTYPE_RULER_NAME = new SagI18NString("统治型", "Ruler");
    public static I18NString PROTOTYPE_CREATOR_NAME = new SagI18NString("创造型", "Creator");
    public static I18NString PROTOTYPE_SAGE_NAME = new SagI18NString("哲人型", "Sage");
    public static I18NString PROTOTYPE_EXPLORER_NAME = new SagI18NString("探索型", "Explorer");
    public static I18NString PROTOTYPE_INNOCENT_NAME = new SagI18NString("纯真型", "Innocent");
    public static I18NString PROTOTYPE_HERO_NAME = new SagI18NString("英雄型", "Hero");
    public static I18NString PROTOTYPE_REBEL_NAME = new SagI18NString("革新型", "Rebel");
    public static I18NString PROTOTYPE_MAGICIAN_NAME = new SagI18NString("魔术师型", "Magician");
    public static I18NString PROTOTYPE_JESTER_NAME = new SagI18NString("快乐型", "Jester");
    public static I18NString PROTOTYPE_LOVER_NAME = new SagI18NString("爱心型", "Lover");
    public static I18NString PROTOTYPE_MEMBER_NAME = new SagI18NString("人人平等型", "Member");
    public static I18NString PROTOTYPE_CAREGIVER_NAME = new SagI18NString("呵护型", "Caregiver");

    /* 4大动机体系 */
    public static I18NString MOTIVATION_SAFE_STABILITY_NAME = new SagI18NString("安全与稳定", "");
    public static I18NString MOTIVATION_LEARNING_GROWTH_NAME = new SagI18NString("学习与成长", "");
    public static I18NString MOTIVATION_ACHIEVEMENTS_RESULTS_NAME = new SagI18NString("成就与结果", "");
    public static I18NString MOTIVATION_RELATIONSHIP_BELONGING_NAME = new SagI18NString("关系与归属", "");
}
