package com.knx.bean.model.dto.client;

import com.knx.bean.model.dto.client.enums.TenantStatusEnum;
import com.knx.common.base.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Package com.knx.feign.tenant.vo.tenant
 * @date 2020/6/24 10:50
 */
@Data
@ApiModel("Tenant")
public class TenantDTO extends BaseModel {

    /**
     * 企业全称
     */
    @ApiModelProperty("企业全称")
    @NotBlank(message = "企业全称不能为空")
    private String name;

    /**
     * 公司的简称 用于创建租户的数据库 全局唯一
     */
    @ApiModelProperty("公司简称")
    @NotBlank(message = "公司简称不能为空")
    private String shortName;

    /**
     * 公司地址
     */
    @ApiModelProperty("公司地址")
    private String address;

    /**
     * 租户状态
     */
    @ApiModelProperty("租户状态")
    private TenantStatusEnum status;

    /**
     * 公司编号
     */
    @ApiModelProperty("公司编号")
    @NotBlank(message = "公司编号不能为空")
    private String companyNo;

    /**
     * 租户备注
     */
    @ApiModelProperty("租户备注")
    private String remark;
}
