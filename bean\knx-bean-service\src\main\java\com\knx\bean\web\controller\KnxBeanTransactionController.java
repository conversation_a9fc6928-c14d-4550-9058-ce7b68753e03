package com.knx.bean.web.controller;


import com.alibaba.fastjson.JSON;
import com.knx.bean.client.RemoteTenantService;
import com.knx.bean.model.entity.KnxBeanTransaction;
import com.knx.bean.model.enums.KnxBeanTransactionStatusEnum;
import com.knx.bean.model.vo.BeanTransactionExcelVo;
import com.knx.bean.model.vo.BeanTransactionVO;
import com.knx.bean.service.api.IKnxBeanTransactionService;
import com.knx.common.base.utils.KnxListUtils;
import com.knx.common.base.web.response.IDValue;
import com.knx.common.base.web.response.WebResponse;
import com.knx.common.security.CommonRole;
import com.knx.platform.common.topic.consts.OperationLogConst;
import com.knx.platform.operationLog.anno.EnableOperationLog;
import com.knx.platform.operationLog.context.OperationLogContextHolder;
import com.knx.platform.operationLog.enums.AppEnum;
import com.knx.survey.client.uerAcount.model.TenantVO;
import com.knx.survey.common.base.field.SagField;
import com.knx.survey.common.util.excelUtil.utils.EasyExcelParams;
import com.knx.survey.common.util.excelUtil.utils.ExcelUtil;
import com.knx.survey.common.util.excelUtil.utils.SheetData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 租户肯豆账户明细
 *
 * <AUTHOR>
 * @version 1.0.0
 * @email <EMAIL>
 * @date 2020-05-18 15:53:19
 */
@EnableOperationLog(
        eventType = OperationLogConst.OPERATION_LOG_EVENT_TYPE_MANAGEMENT,
        appCode= AppEnum.SAG_MANAGEMENT
)
@RestController
@RequestMapping("/knx/bean/transaction")
@Api(value = "/transaction", tags = "租户肯豆账户明细")
public class KnxBeanTransactionController {
    @Autowired
    private IKnxBeanTransactionService knxbeanTransactionService;
    @Autowired
    private RemoteTenantService remoteTenantService;

    @EnableOperationLog(
            permissionCode = "SAG:MANAGEMENT:TENANT_LIST:RECHARGE",
            groupCode = "SAG:MANAGEMENT:TENANT_LIST",
            tenantIds = "#knxBeanTransaction.tenantId"
    )
    @ApiOperation(value = "肯豆充值", notes = "肯豆充值")
    @PostMapping("/recharge")
    @RequiresRoles(CommonRole.ROLE_KNX_BEAN_ADMIN)
    public WebResponse<IDValue> recharge(@RequestBody KnxBeanTransaction knxBeanTransaction) {

        //添加操作日志参数记录
        OperationLogContextHolder.setCustomData(JSON.toJSONString(knxBeanTransaction));

        knxBeanTransaction.setStatus(KnxBeanTransactionStatusEnum.USED);
        knxbeanTransactionService.recharge(knxBeanTransaction);
        return WebResponse.returnCreateResult(knxBeanTransaction);
    }

    @ApiOperation(value = "肯豆充值回撤", notes = "肯豆充值回撤")
    @PostMapping("/cancelRecharge/{seqNo}")
    @RequiresRoles(CommonRole.ROLE_KNX_BEAN_ADMIN)
    public WebResponse<Boolean> cancelRecharge(@ApiParam(value = "订单账号", name = "seqNo", required = true) @PathVariable String seqNo) {
        boolean result = knxbeanTransactionService.cancelRecharge(seqNo);
        return WebResponse.returnSuccessData(result);
    }

    @ApiOperation(value = "肯豆扣费", notes = "肯豆扣费")
    @PostMapping("/spend")
    @RequiresRoles(value = {CommonRole.ROLE_TENANT_ADMIN, CommonRole.ROLE_TENANT_USER}, logical = Logical.OR)
    public WebResponse<Boolean> spend(@RequestBody KnxBeanTransaction knxBeanTransaction) {
        knxBeanTransaction.setStatus(KnxBeanTransactionStatusEnum.USED);
        boolean result = knxbeanTransactionService.spend(knxBeanTransaction);
        return WebResponse.returnSuccessData(result);
    }

    @EnableOperationLog(
            permissionCode = "SAG:MANAGEMENT:TENANT_LIST:MESSAGE_RECHARGE",
            groupCode = "SAG:MANAGEMENT:TENANT_LIST",
            tenantIds = "#knxBeanTransaction.tenantId"
    )
    @ApiOperation(value = "短信充值", notes = "短信充值")
    @PostMapping("/messageRecharge")
    @RequiresRoles(CommonRole.ROLE_KNX_BEAN_ADMIN)
    public WebResponse<IDValue> messageRecharge(@RequestBody KnxBeanTransaction knxBeanTransaction) {

        //添加操作日志参数记录
        OperationLogContextHolder.setCustomData(JSON.toJSONString(knxBeanTransaction));

        knxBeanTransaction.setStatus(KnxBeanTransactionStatusEnum.USED);
        knxbeanTransactionService.messageRecharge(knxBeanTransaction);
        return WebResponse.returnCreateResult(knxBeanTransaction);
    }

    @ApiOperation(value = "短信扣费", notes = "短信扣费")
    @PostMapping("/messageSpend")
    @RequiresRoles(value = {CommonRole.ROLE_TENANT_ADMIN, CommonRole.ROLE_TENANT_USER}, logical = Logical.OR)
    public WebResponse<Boolean> messageSpend(@RequestBody KnxBeanTransaction knxBeanTransaction) {
        knxBeanTransaction.setStatus(KnxBeanTransactionStatusEnum.USED);
        boolean result = knxbeanTransactionService.messageSpend(knxBeanTransaction);
        return WebResponse.returnSuccessData(result);
    }

    @ApiOperation(value = "查看肯豆流水明细", notes = "查看肯豆流水明细")
    @GetMapping("/list")
    public WebResponse<List<KnxBeanTransaction>> list() {
        List<KnxBeanTransaction> list = knxbeanTransactionService.list();
        KnxListUtils.createTimeSortByDesc(list);
        return WebResponse.returnSuccessData(list);
    }

    @ApiOperation(value = "按id查看肯豆流水明细", notes = "按id查看肯豆流水明细")
    @GetMapping("/get/{id}")
    public WebResponse<KnxBeanTransaction> get(@ApiParam(value = "账号id", name = "id", required = true) @PathVariable String id) {
        KnxBeanTransaction knxBeanTransaction = knxbeanTransactionService.getById(id);
        return WebResponse.returnSuccessData(knxBeanTransaction);
    }

    @EnableOperationLog(
            permissionCode = "SAG:MANAGEMENT:TENANT_LIST:USE_DEATIL",
            groupCode = "SAG:MANAGEMENT:TENANT_LIST"
    )
    @ApiOperation(value = "查看租户的肯豆流水明细", notes = "查看租户的肯豆流水明细")
    @GetMapping("/listByTenantId/{tenantId}")
    public WebResponse<List<BeanTransactionVO>> listByTenantId(@ApiParam(value = "租户id", name = "tenantId", required = true) @PathVariable("tenantId") String tenantId) {
        List<KnxBeanTransaction> list = knxbeanTransactionService.listByTenantId(tenantId);
        List<BeanTransactionVO> voList = list.stream().map(n -> {
            // BaseModel中createBy、lastUpdateBy字段有JsonIgnore注解，需要定义VO对象重写这两个字段，才能会返回到前端
            BeanTransactionVO vo = new BeanTransactionVO();
            BeanUtils.copyProperties(n, vo);
            return vo;
        }).collect(Collectors.toList());
        return WebResponse.returnSuccessData(voList);
    }

    @EnableOperationLog(
            permissionCode = "SAG:MANAGEMENT:TENANT_LIST:EXPORT_RECHARGE_DETAIL",
            groupCode = "SAG:MANAGEMENT:TENANT_LIST",
            tenantIds = "#tenantId"
    )
    @ApiOperation(value = "导出肯豆流水明细", notes = "导出肯豆流水明细", produces = "application/octet-stream")
    @PostMapping("/export")
    public void export(@RequestParam("tenantId") String tenantId, HttpServletResponse response) throws IOException {
        EasyExcelParams easyExcelParams = new EasyExcelParams();
        SheetData sheetData = new SheetData();
        sheetData.setDataModelClazz(BeanTransactionExcelVo.class);
        String sheetName = tenantId;
        // 租户名称
        WebResponse<TenantVO> webResponse = remoteTenantService.get(tenantId);
        if (webResponse != null && 0 == webResponse.getResult().getCode()) {
            sheetName = webResponse.getData().getName();
        }
        sheetData.setSheetName(sheetName);
        sheetData.setData(knxbeanTransactionService.export(tenantId));
        sheetData.checkValid();
        List<SheetData> sheetDataList = new ArrayList<>();
        sheetDataList.add(sheetData);
        easyExcelParams.setExcelNameWithoutExt(SagField.KENDOU_DETAIL);
        easyExcelParams.setResponse(response);
        easyExcelParams.setSheetDatas(sheetDataList);
        easyExcelParams.checkValid();
        ExcelUtil.exportExcel2007(easyExcelParams);
    }
}
