DROP TABLE IF EXISTS "public"."order_pay_record";
CREATE TABLE order_pay_record (
	id varchar(36) NOT NULL,
	tenant_id varchar(36) NOT NULL,
    order_id varchar(36) NOT NULL,
    type varchar(50) DEFAULT NULL,
    amount decimal NOT NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__order_pay_record___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."order_pay_record"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."order_pay_record"."order_id" IS '订单ID';
COMMENT ON COLUMN "public"."order_pay_record"."type" IS '类型';
COMMENT ON COLUMN "public"."order_pay_record"."amount" IS '金额';
COMMENT ON TABLE  "public"."order_pay_record" IS '包年订单支付记录表';

CREATE INDEX ___idx_order_pay_record_tenant_id___ ON public.order_pay_record(tenant_id);