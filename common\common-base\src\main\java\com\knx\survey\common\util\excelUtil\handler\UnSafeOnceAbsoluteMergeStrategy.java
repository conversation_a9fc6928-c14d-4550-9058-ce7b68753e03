package com.knx.survey.common.util.excelUtil.handler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UnSafeOnceAbsoluteMergeStrategy.java
 * @Description TODO
 * @createTime 2022年07月08日 14:49:00
 */
public class UnSafeOnceAbsoluteMergeStrategy extends AbstractMergeStrategy {

    private int firstRowIndex;
    private int lastRowIndex;
    private int firstColumnIndex;
    private int lastColumnIndex;

    public UnSafeOnceAbsoluteMergeStrategy(int firstRowIndex, int lastRowIndex, int firstColumnIndex, int lastColumnIndex) {
        if (firstRowIndex < 0 || lastRowIndex < 0 || firstColumnIndex < 0 || lastColumnIndex < 0) {
            throw new IllegalArgumentException("All parameters must be greater than 0");
        }
        this.firstRowIndex = firstRowIndex;
        this.lastRowIndex = lastRowIndex;
        this.firstColumnIndex = firstColumnIndex;
        this.lastColumnIndex = lastColumnIndex;
    }

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, int relativeRowIndex) {
        if (cell.getRowIndex() == firstRowIndex && cell.getColumnIndex() == firstColumnIndex) {
            CellRangeAddress cellRangeAddress =
                    new CellRangeAddress(firstRowIndex, lastRowIndex, firstColumnIndex, lastColumnIndex);
            sheet.addMergedRegionUnsafe(cellRangeAddress);
        }
    }
}
