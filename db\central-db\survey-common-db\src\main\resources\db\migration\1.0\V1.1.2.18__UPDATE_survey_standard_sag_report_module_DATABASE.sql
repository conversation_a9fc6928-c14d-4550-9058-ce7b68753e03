ALTER TABLE survey_standard_sag_report_module DROP CONSTRAINT ___pk__survey_standard_sag_report_module___;
ALTER TABLE survey_standard_sag_report_module ADD COLUMN id SERIAL PRIMARY KEY;
ALTER TABLE survey_standard_sag_report_module ALTER COLUMN id TYPE varchar(36) USING id::varchar;
ALTER TABLE survey_standard_sag_report_module ALTER COLUMN id DROP DEFAULT;
CREATE INDEX ___idx_survey_standard_sag_report_module_key___ ON survey_standard_sag_report_module(key);

ALTER TABLE survey_standard_sag_report_module ADD "create_by" varchar(36) NULL;
ALTER TABLE survey_standard_sag_report_module ADD "create_time" TIMESTAMP(3) DEFAULT NOW();
ALTER TABLE survey_standard_sag_report_module ADD "update_time" TIMESTAMP(3) DEFAULT NOW();
ALTER TABLE survey_standard_sag_report_module ADD "last_update_by" varchar(36) NULL;
ALTER TABLE survey_standard_sag_report_module ADD is_deleted bool NULL DEFAULT false;
ALTER TABLE survey_standard_sag_report_module ADD "app_id" varchar(36) NULL;