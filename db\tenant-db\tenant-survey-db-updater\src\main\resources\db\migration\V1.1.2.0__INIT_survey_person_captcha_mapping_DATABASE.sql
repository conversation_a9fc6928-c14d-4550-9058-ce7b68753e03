DROP TABLE IF EXISTS survey_person_captcha_mapping;
CREATE TABLE survey_person_captcha_mapping (
	id varchar(36) NULL,
	project_id varchar(36) NOT NULL,
	captcha varchar(5) NOT NULL,
	person_id varchar(36) NOT NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) NULL,
	CONSTRAINT survey_person_captcha_mapping_pk PRIMARY KEY (id),
	CONSTRAINT survey_person_captcha_mapping_un UNIQUE (project_id,captcha)
);
COMMENT ON TABLE public.survey_person_captcha_mapping IS '问卷用户验证码映射表';

COMMENT ON COLUMN public.survey_person_captcha_mapping.person_id IS '问卷用户id';
COMMENT ON COLUMN public.survey_person_captcha_mapping.project_id IS '活动id';
COMMENT ON COLUMN public.survey_person_captcha_mapping.captcha IS '验证码';
