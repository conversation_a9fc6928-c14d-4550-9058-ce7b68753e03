DROP TABLE IF EXISTS "public"."prisma_on_line_report_select_data";
CREATE TABLE public.prisma_on_line_report_select_data (
    id varchar(36) NOT NULL,
    prisma_report_data_id varchar(36) DEFAULT NULL,
    prisma_on_line_report_data json DEFAULT NULL,
    create_by varchar(36) DEFAULT NULL,
    create_time timestamp(3) NULL DEFAULT now(),
    update_time timestamp(3) NULL DEFAULT now(),
    last_update_by varchar(36) NULL,
    is_deleted bool NULL DEFAULT false,
    app_id varchar(36) NULL,
    CONSTRAINT "___pk___prisma_on_line_report_select_data___" PRIMARY KEY (id)
);
COMMENT ON TABLE  "public"."prisma_on_line_report_select_data" IS '调研在线报告数据表';
COMMENT ON COLUMN "public"."prisma_on_line_report_select_data"."prisma_on_line_report_data" IS '在线报告数据';
COMMENT ON COLUMN "public"."prisma_on_line_report_select_data"."prisma_report_data_id" IS 'prisma报告ID';