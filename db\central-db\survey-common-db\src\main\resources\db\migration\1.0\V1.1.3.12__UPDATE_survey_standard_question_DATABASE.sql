ALTER TABLE public.survey_standard_question ADD "description_match_condition" varchar(10) NULL ;
COMMENT ON COLUMN public.survey_standard_question."description_match_condition" IS '填答说明规则匹配条件';

ALTER TABLE public.survey_standard_question ADD "description_rules" text NULL ;
COMMENT ON COLUMN public.survey_standard_question."description_rules" IS '填答说明匹配规则';

alter table public.survey_standard_option_description_rule drop column if exists "description_match_condition";
alter table public.survey_standard_option_description_rule drop column if exists "description_rules";
alter table public.survey_standard_option_description_rule drop column if exists "match_condition";
alter table public.survey_standard_option_description_rule drop column if exists "rules";

alter table public.survey_standard_option_description_rule rename to survey_standard_option_introduction;