DROP TABLE IF EXISTS "public"."prisma_report_keyword_frequency";
CREATE TABLE public.prisma_report_keyword_frequency (
    id varchar(36) NOT NULL,
    prisma_report_data_id varchar(36) DEFAULT NULL,
    question_id varchar(36) DEFAULT NULL,
    keyword varchar(200) DEFAULT NULL,
    frequency INT4 DEFAULT 0,
    type varchar(50) DEFAULT NULL,
    create_by varchar(36) DEFAULT NULL,
    create_time timestamp(3) NULL DEFAULT now(),
    update_time timestamp(3) NULL DEFAULT now(),
    last_update_by varchar(36) NULL,
    is_deleted bool NULL DEFAULT false,
    app_id varchar(36) NULL,
    CONSTRAINT "___pk___prisma_report_keyword_frequency___" PRIMARY KEY (id)
);
COMMENT ON TABLE  "public"."prisma_report_keyword_frequency" IS 'prisma报告关键词词频';
COMMENT ON COLUMN "public"."prisma_report_keyword_frequency"."prisma_report_data_id" IS 'prisma报告ID';
COMMENT ON COLUMN "public"."prisma_report_keyword_frequency"."question_id" IS '题目id';
COMMENT ON COLUMN "public"."prisma_report_keyword_frequency"."keyword" IS '关键词';
COMMENT ON COLUMN "public"."prisma_report_keyword_frequency"."frequency" IS '频次';
COMMENT ON COLUMN "public"."prisma_report_keyword_frequency"."type" IS '类型';
