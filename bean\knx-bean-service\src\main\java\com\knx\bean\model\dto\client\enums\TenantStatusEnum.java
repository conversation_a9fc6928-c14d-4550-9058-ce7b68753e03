package com.knx.bean.model.dto.client.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Package com.knx.feign.tenant.vo.tenant
 * @date 2020/6/24 10:53
 */
@Getter
@ToString(callSuper = true)
public enum TenantStatusEnum {
    PENDING("等待审批"),
    PAUSE("暂停使用"),
    ACTIVE("活动");

    private String name;

    private TenantStatusEnum(String name) {
        this.name = name;
    }

}
