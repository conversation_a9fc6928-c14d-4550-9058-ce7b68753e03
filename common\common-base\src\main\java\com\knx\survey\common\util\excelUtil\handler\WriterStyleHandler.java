package com.knx.survey.common.util.excelUtil.handler;


import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.knx.survey.common.util.excelUtil.pojo.OneCellStyle;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/6/23 17:07
 */

@Slf4j
@Data
public class WriterStyleHandler implements CellWriteHandler {

    private List<OneCellStyle> cellStyles;

    private Map<String, List<OneCellStyle>> cellStylesMap;

    public WriterStyleHandler(List<OneCellStyle> cellStyles) {
        this.cellStyles = cellStyles;
        cellStylesMap = cellStyles.stream().collect(Collectors.groupingBy(d -> d.getRowIndex().toString() + d.getColumnIndex().toString()));
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, int relativeRowIndex, boolean isHead) {

    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, CellData cellData, Cell cell, Head head, int relativeRowIndex, boolean isHead) {
        List<OneCellStyle> cellStyles = cellStylesMap.get(String.valueOf(cell.getRowIndex()) + String.valueOf(cell.getColumnIndex()));
        if (CollectionUtils.isEmpty(cellStyles)) {
            return;
        }

        OneCellStyle oneCellStyle = cellStyles.stream().filter(d -> d.getRowIndex() == cell.getRowIndex() && d.getColumnIndex() == cell.getColumnIndex()).findFirst().orElse(null);
        if (oneCellStyle == null) {
            return;
        }

        XSSFCellStyle cellStyle = (XSSFCellStyle) writeSheetHolder.getSheet().getWorkbook().createCellStyle();
        XSSFFont xssfFont = (XSSFFont) writeSheetHolder.getSheet().getWorkbook().createFont();
        if (BooleanUtils.isTrue(oneCellStyle.getItalic())) {
            xssfFont.setItalic(true);
        }

        if (BooleanUtils.isTrue(oneCellStyle.getBold())) {
            xssfFont.setBold(true);
        }

        xssfFont.setFontName("宋体");
        xssfFont.setFontHeightInPoints((short) 11);
        cellStyle.setFont(xssfFont);

        if (oneCellStyle.getBColor() != null) {
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            cellStyle.setFillForegroundColor(oneCellStyle.getBColor());
        }

        if (oneCellStyle.getHorizontalAlignment() != null) {
            cellStyle.setAlignment(oneCellStyle.getHorizontalAlignment());
        }

        cell.setCellStyle(cellStyle);
    }
}
