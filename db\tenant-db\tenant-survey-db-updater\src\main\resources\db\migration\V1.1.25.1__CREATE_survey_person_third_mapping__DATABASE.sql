DROP TABLE IF EXISTS "public"."survey_person_third_mapping";
CREATE TABLE survey_person_third_mapping (
	id varchar(36) NOT NULL,
	third_type varchar(50) NOT NULL,
	project_id varchar(36) NOT NULL,
	person_id varchar(36) NOT NULL,
	third_person_id varchar(50) NOT NULL,
	exam_id varchar(50) NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__survey_person_third_mapping___" PRIMARY KEY (id)
);
COMMENT ON TABLE  "public"."survey_person_third_mapping" IS '人员第三方关系表';
COMMENT ON COLUMN "public"."survey_person_third_mapping"."third_type" IS '第三方类型';
COMMENT ON COLUMN "public"."survey_person_third_mapping"."project_id" IS '活动ID';
COMMENT ON COLUMN "public"."survey_person_third_mapping"."person_id" IS '人员ID';
COMMENT ON COLUMN "public"."survey_person_third_mapping"."third_person_id" IS '第三方人员ID';
COMMENT ON COLUMN "public"."survey_person_third_mapping"."exam_id" IS '第三方考试ID';


CREATE INDEX ___idx_survey_person_third_mapping_person_id___ ON public.survey_person_third_mapping(person_id);
CREATE INDEX ___idx_survey_person_third_mapping_third_person_id___ ON public.survey_person_third_mapping(third_person_id);