/* update tenant DB report_template table for CA */
alter table "public"."report_template" add report_style varchar(36) ;
COMMENT ON COLUMN "public"."report_template"."report_style" IS '报告样式';

alter table "public"."report_template" add code varchar(36) ;
COMMENT ON COLUMN "public"."report_template"."code" IS '报告编码';

alter table "public"."report_template" add config_type varchar(36) ;
COMMENT ON COLUMN "public"."report_template"."config_type" IS '配置类型';

/* update tenant DB sag_norm_transform table for CA */
alter table "public"."sag_norm_transform" add code varchar(36) ;
COMMENT ON COLUMN "public"."sag_norm_transform"."code" IS '报告编码';

alter table "public"."sag_norm_transform"  alter column id type varchar (36);

/* update tenant DB sag_norm_transform table for CA */
alter table "public"."sag_report_dimension" add reflective_question json ;
COMMENT ON COLUMN "public"."sag_report_dimension"."reflective_question" IS '反思问题';

alter table "public"."sag_report_dimension" add recommended_books json ;
COMMENT ON COLUMN "public"."sag_report_dimension"."recommended_books" IS '推荐书籍';

alter table "public"."sag_report_dimension" add interview_questions json ;
COMMENT ON COLUMN "public"."sag_report_dimension"."interview_questions" IS '面试问题';

alter table "public"."sag_report_dimension" add task_training json ;
COMMENT ON COLUMN "public"."sag_report_dimension"."task_training" IS '任务训练';

alter table "public"."sag_report_dimension" add recommended_courses json ;
COMMENT ON COLUMN "public"."sag_report_dimension"."recommended_courses" IS '推荐课程';