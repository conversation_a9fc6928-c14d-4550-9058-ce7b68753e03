package com.knx.bean.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.knx.bean.model.entity.KnxBeanTransaction;
import com.knx.bean.model.enums.KnxBeanTransactionTypeEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

/**
 * 租户肯豆账户明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-05-21 15:00:28
 */
@Mapper
public interface KnxBeanTransactionMapper extends BaseMapper<KnxBeanTransaction> {

    Page<KnxBeanTransaction> selectTransactionOrders(@Param("pages") Page<KnxBeanTransaction> pages, @Param("params") Map<String, Object> params);


    @Select("SELECT\n" +
            "\t*\n" +
            "FROM\n" +
            "\tknx_bean_transaction\n" +
            "WHERE\n" +
            "\ttenant_id = #{tenantId}\n" +
            "\t and \"type\" not in ('MESSAGE_RECHARGE','CANCEL_MESSAGE_RECHARGE')\n" +
            "ORDER BY\n" +
            "\tupdate_time DESC\n" +
            "LIMIT 1")
    KnxBeanTransaction getNewest(@Param(KnxBeanTransaction.Fields.tenantId) String tenantId );




    @Select("SELECT\n" +
            "\t*\n" +
            "FROM\n" +
            "\tknx_bean_transaction\n" +
            "WHERE\n" +
            "\ttenant_id = #{tenantId}\n" +
            "\t and \"type\" in ('MESSAGE_RECHARGE','CANCEL_MESSAGE_RECHARGE')\n" +
            "ORDER BY\n" +
            "\tupdate_time DESC\n" +
            "LIMIT 1")
    KnxBeanTransaction getMessageNewest(@Param(KnxBeanTransaction.Fields.tenantId) String tenantId );
}
