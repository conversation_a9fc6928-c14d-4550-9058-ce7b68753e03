/*报告模块更新*/
alter table "public"."survey_standard_sag_report_module" add type varchar (50);
COMMENT ON COLUMN "public"."survey_standard_sag_report_module"."type" IS '报告模块类型';

update survey_standard_sag_report_module set "type" = 'ADAPTATION_DETAILS' where "key"='adaptationDetails';
update survey_standard_sag_report_module set "type" = 'APPENDIX' where "key"='appendix';
update survey_standard_sag_report_module set "type" = 'DETAILED_SCORE' where "key"='detailedScore';
update survey_standard_sag_report_module set "type" = 'EPILOGUE' where "key"='epilogue';
update survey_standard_sag_report_module set "type" = 'FIT' where "key"='fit';
update survey_standard_sag_report_module set "type" = 'FITNESS_LEVEL' where "key"='fitnessLevel';
update survey_standard_sag_report_module set "type" = 'FRONT_COVER' where "key"='frontCover';
update survey_standard_sag_report_module set "type" = 'OVERVIEW' where "key"='overview';
update survey_standard_sag_report_module set "type" = 'PERSONAL_DEVELOPMENT' where "key"='personalDevelopment';
update survey_standard_sag_report_module set "type" = 'POLICY_DECISION' where "key"='policyDecision';
update survey_standard_sag_report_module set "type" = 'PREFACE' where "key"='preface';
update survey_standard_sag_report_module set "type" = 'RESPONSE_STYLE' where "key"='responseStyle';
update survey_standard_sag_report_module set "type" = 'TRAIT_DIFFERENCE' where "key"='traitDifference';
update survey_standard_sag_report_module set "type" = 'BACK_COVER' where "key"='backCover';
update survey_standard_sag_report_module set "type" = 'INTERVIEW_SUGGESTIONS' where "key"='interviewSuggestions';
update survey_standard_sag_report_module set "type" = 'DEVELOPMENT_SUGGESTIONS' where "key"='developmentSuggestions';

/*标准报告配置表更新*/
alter table "public"."survey_standard_sag_report_template_config" add name json;
COMMENT ON COLUMN "public"."survey_standard_sag_report_template_config"."name" IS '报告名';
alter table "public"."survey_standard_sag_report_template_config" add style varchar (50);
COMMENT ON COLUMN "public"."survey_standard_sag_report_template_config"."style" IS '报告样式';
