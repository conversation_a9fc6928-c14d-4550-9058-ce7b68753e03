package com.knx.survey.common.util.excelUtil.utils;

import com.google.common.collect.Lists;
import com.knx.common.base.enums.FileTypeEnum;
import com.knx.common.base.service.exception.BusinessException;
import com.knx.common.base.service.exception.ErrorCode;
import com.knx.survey.common.base.field.SurveyField;
import com.knx.survey.common.util.DateUtil;
import com.knx.survey.common.util.IDUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.util.UriUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ZipUtil.java
 * @Description TODO
 * @createTime 2021年07月22日 14:05:00
 */
@Slf4j
public class ZipUtil {


    public static void dynamicExportExcelsZip(List<EasyExcelParams> excels, HttpServletResponse response, String fileName) throws IOException {
        ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream());
        prepareResponds(response, fileName + ".zip");
        try {
            for (EasyExcelParams excel : excels) {
                zipOut.putNextEntry(new ZipEntry(excel.getExcelNameWithoutExt() + excel.getExcelType().getValue()));
                zipOut.write(excel.getOut().toByteArray());
                zipOut.flush();
                zipOut.closeEntry();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            zipOut.close();
        }
    }


    public static void downloadExcelsToZip(List<EasyExcelParams> excelParams, HttpServletResponse response, String fileName) throws IOException {
        int fileIndex = 0;
        if (excelParams.size() == 1) {
            excelParams.get(0).setResponse(response);
            excelParams.get(0).setExcelNameWithoutExt(fileName);
            ExcelUtil.dynamicExportExcel2007(excelParams.get(0));
        } else {
            List<EasyExcelParams> exportParams = new ArrayList<>();
            for (EasyExcelParams excel : excelParams) {
                for (SheetData sheetData : excel.getSheetDatas()) {
                    newSheetName(sheetData, ++fileIndex);
                }
                exportParams.add(ExcelUtil.dynamicGetExcel2007OutputStream(excel));
            }

            resetExcelFileName(exportParams);

            ZipUtil.dynamicExportExcelsZip(exportParams, response, fileName);
        }
    }

    /**
     * 重命名excel名称防止重名
     *
     * @param exportParams
     */
    public static void resetExcelFileName(List<EasyExcelParams> exportParams) {
        Map<String, List<EasyExcelParams>> nameSheetsMap = exportParams.stream().collect(Collectors.groupingBy(EasyExcelParams::getExcelNameWithoutExt));
        for (String excelName : nameSheetsMap.keySet()) {
            List<EasyExcelParams> oneNameSheets = nameSheetsMap.get(excelName);
            if (oneNameSheets.size() > 1) {
                for (int i = 0; i < oneNameSheets.size(); i++) {
                    EasyExcelParams easyExcelParams = oneNameSheets.get(i);
                    easyExcelParams.setExcelNameWithoutExt(easyExcelParams.getExcelNameWithoutExt() + SurveyField.UNDER_LINE + i);
                }
            }
        }
    }

    public static void writeZipToDisk(List<EasyExcelParams> excelParams, String path) throws IOException {
        writeZipToDisk(excelParams, path, true);
    }


    public static void writeZipToDisk(List<EasyExcelParams> excelParams, String path, Boolean resetSheetName) throws IOException {

        List<EasyExcelParams> exportParams = new ArrayList<>();
        for (EasyExcelParams excel : excelParams) {
            if (excel.getOut() == null) {
                if (resetSheetName) {
                    resetSheetFileName(excel);
                }
                exportParams.add(ExcelUtil.dynamicGetExcel2007OutputStream(excel));
            } else {
                exportParams.add(excel);
            }
        }

        if (resetSheetName) {
            resetExcelFileName(exportParams);
        }

        writeZipToPath(exportParams, path);
    }

    public static void resetSheetFileName(EasyExcelParams excelParam){
        Map<String, List<SheetData>> nameSheets = excelParam.getSheetDatas().stream().filter(d->{
            if(d.getSheetName().length() > 28){
                String sheetName = stringOverstepLimitReplace(d.getSheetName(), 28, "...");
                d.setSheetName(sheetName);
            }
            return true;
        }).collect(Collectors.groupingBy(SheetData::getSheetName));
        for (String sheetName : nameSheets.keySet()) {
            if (nameSheets.get(sheetName).size() > 1) {
                //如果出现重名sheet，添加防重名
                int fileIndex = 0;
                for (SheetData sheetData : nameSheets.get(sheetName)) {
                    ZipUtil.newSheetName(sheetData, ++fileIndex);
                }
            } else {
                for (SheetData sheetData : nameSheets.get(sheetName)) {
                    ZipUtil.newSheetName(sheetData, null);
                }
            }
        }
    }

    /**
     * 字符串超长替换
     *
     * @param str
     * @param limit
     * @param replace
     * @return
     */
    private static String stringOverstepLimitReplace(String str, int limit, String replace) {
        if (str.length() <= limit) {
            return str;
        }

        if (replace == null) {
            throw new BusinessException("替换的字符串不能为空", ErrorCode.FAIL);
        }

        if (replace.length() >= limit) {
            throw new BusinessException("替换的字符串不能为空", ErrorCode.FAIL);
        }

        //剩余长度
        int leftLength = limit - replace.length();
        //文字前长度
        int beforeLength = leftLength / 2;
        //文字后长度
        int afterLength = leftLength - beforeLength;

        //截取的前字符串
        String strBefore = str.substring(0, beforeLength);
        //截取的后字符串
        String strAfter = str.substring(str.length() - afterLength);
        return strBefore + replace + strAfter;
    }

    public static void newSheetName(SheetData sheetData, Integer fileIndex) {
        List<String> invalidChars = Lists.newArrayList("[", "]", "/", "?", "/", "*", ":", "'", "\"", "\\");
        String fileAppend = SurveyField.EMPTY_STRING;
        if (fileIndex != null) {
            fileAppend = SurveyField.UNDER_LINE + (fileIndex);
        }
        String sheetName = sheetData.getSheetName();
        if (sheetName.length() > (30 - fileAppend.length())) {
            sheetName = sheetName.substring(0, 30 - fileAppend.length());
        }

        for (String invalidChar : invalidChars) {
            if (sheetName.contains(invalidChar)) {
                if (invalidChar.equals("\\")) {
                    sheetName = sheetName.replaceAll("\\\\", "");
                } else if (invalidChar.equals("*")) {
                    sheetName = sheetName.replaceAll("\\*", "");
                } else if (invalidChar.equals("?")) {
                    sheetName = sheetName.replaceAll("\\?", "");
                } else if (invalidChar.equals("[")) {
                    sheetName = sheetName.replaceAll("\\[", "");
                } else {
                    sheetName = sheetName.replaceAll(invalidChar, "");
                }
            }
        }
        sheetData.setSheetName(sheetName + fileAppend);
    }

    public static boolean deleteZip(String path) {
        boolean res = false;
        try {
            File file = new File(path);
            if (file.exists() && file.isFile()) {
                res = file.delete();
            }
        } catch (Exception e) {
            log.info("删除文件失败：{}", path, e);
        }
        return res;
    }

    public static void writeZipToPath(List<EasyExcelParams> excels, String path) throws IOException {
        Long current = System.currentTimeMillis();
        log.info("excel write to local file start,path:{}", path);
        OutputStream outputStream = new FileOutputStream(path);
        ZipOutputStream zipOut = new ZipOutputStream(outputStream);
        try {
            for (EasyExcelParams excel : excels) {
                zipOut.putNextEntry(new ZipEntry(excel.getExcelNameWithoutExt() + excel.getExcelType().getValue()));
                zipOut.write(excel.getOut().toByteArray());
                zipOut.flush();
                zipOut.closeEntry();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            zipOut.close();
        }
        log.info("excel write to local file end,path:{},cost:{}", path, System.currentTimeMillis() - current);
    }


    /**
     * 设置response相关参数
     *
     * @param response response
     * @param fileName 文件名
     * @throws UnsupportedEncodingException e
     */
    public static void prepareResponds(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
        response.setContentType("application/x-msdownload");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-disposition", String.format("attachment; filename=%s", UriUtils.encode(fileName, StandardCharsets.UTF_8)));

    }


    public static void writeExcelToDisk(EasyExcelParams excelParam, String path) throws IOException {
        writeExcelToDisk(excelParam, path, true);
    }


    public static void writeExcelToDisk(EasyExcelParams excelParam, String path, Boolean resetSheetName) throws IOException {
        if (resetSheetName) {
            resetSheetFileName(excelParam);
        }

        ExcelUtil.dynamicGetExcel2007OutputStream(excelParam);

        writeExcelToPath(excelParam, path);
    }


    public static void writeExcelToPath(EasyExcelParams easyExcelParam, String exportFilePath) throws IOException {
        try {
            File exportFile = new File(exportFilePath);
            if (!exportFile.exists()) {
                exportFile.createNewFile();
            }
            OutputStream fileOut = new FileOutputStream(exportFilePath);
            fileOut.write(easyExcelParam.getOut().toByteArray());
            fileOut.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String replaceFileInvalidChar(String fileName) {
        Pattern pattern = Pattern.compile("[\\s\\\\/:\\*\\?\\\"<>\\|]");
        Matcher matcher = pattern.matcher(fileName);

        return matcher.replaceAll("");
    }

    /**
     * 返回新文件名称（过滤非法字符，超过80个字符从后面截取）
     *
     * @param fileName
     * @return
     */
    public static String newFileName(String fileName) {
        String name = replaceFileInvalidChar(fileName);
        if (name.length() > SurveyField.IAS_FILE_NAME_LENGTH) {
            name = name.substring(name.length() - SurveyField.IAS_FILE_NAME_LENGTH, name.length());
        }
        return name;
    }
}
