package com.knx.bean.model.vo;

import com.knx.bean.model.entity.KnxBeanTransaction;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 肯豆交易记录VO
 */
@Data
@ApiModel(value = "BeanTransactionVO")
@EqualsAndHashCode
public class BeanTransactionVO extends KnxBeanTransaction {

    @ApiModelProperty(value = "创建人", example = "1281140300608344066")
    private String createBy;

    @ApiModelProperty(value = "最后修改人", example = "1281140300608344066")
    private String lastUpdateBy;
}
