DROP TABLE IF EXISTS "public"."survey_questionnaire_relation_permission";
CREATE TABLE survey_questionnaire_relation_permission (
	id varchar(36) NOT NULL,
	project_id varchar(36) NOT NULL,
	questionnaire_id varchar(36) NOT NULL,
	type varchar(50) NULL,
    is_confirmed bool NULL DEFAULT false,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__survey_questionnaire_relation_permission___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."survey_questionnaire_relation_permission"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_questionnaire_relation_permission"."questionnaire_id" IS '问卷id';
COMMENT ON COLUMN "public"."survey_questionnaire_relation_permission"."type" IS '权限类型';
COMMENT ON COLUMN "public"."survey_questionnaire_relation_permission"."is_confirmed" IS '是否确认';
COMMENT ON TABLE  "public"."survey_questionnaire_relation_permission" IS '问卷关联权限表';
