ALTER TABLE public.survey_standard_question ADD "description_status" varchar(10) NULL DEFAULT 'DISABLE';
COMMENT ON COLUMN public.survey_standard_question."description_status" IS '是否启用说明';

DROP TABLE IF EXISTS "public"."survey_standard_option_description_rule";
CREATE TABLE survey_standard_option_description_rule (
     id varchar(36) NOT NULL,
     question_id varchar(36) NULL,
     option_id varchar(36) NULL,
     match_condition varchar(10) NULL,
     rules text NULL,
     introduction text NULL,
     introduction_status varchar(10) NULL DEFAULT 'DISABLE'::character varying,
     create_by varchar(36) NULL,
     create_time timestamp(3) NULL DEFAULT now(),
     update_time timestamp(3) NULL DEFAULT now(),
     last_update_by varchar(36) NULL,
     is_deleted bool NULL DEFAULT false,
     app_id varchar(36) null,
     CONSTRAINT "___pk__survey_standard_option_description_rule___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."survey_standard_option_description_rule"."question_id" IS '问题';
COMMENT ON COLUMN "public"."survey_standard_option_description_rule"."option_id" IS '选项';
COMMENT ON COLUMN "public"."survey_standard_option_description_rule"."introduction" IS '选项介绍';
COMMENT ON COLUMN "public"."survey_standard_option_description_rule"."match_condition" IS '填答说明规则匹配条件';
COMMENT ON COLUMN "public"."survey_standard_option_description_rule"."rules" IS '填答说明匹配规则';
COMMENT ON COLUMN "public"."survey_standard_option_description_rule"."introduction_status" IS '选项介绍是否启用';
COMMENT ON TABLE  "public"."survey_standard_option_description_rule" IS '选项填答说明和规则';