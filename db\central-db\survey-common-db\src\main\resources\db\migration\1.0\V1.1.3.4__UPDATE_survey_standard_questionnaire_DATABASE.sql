ALTER TABLE public.survey_standard_questionnaire ADD solution_type varchar(50) DEFAULT 'STANDARD_PRODUCT';
COMMENT ON COLUMN public.survey_standard_questionnaire."solution_type" IS '问卷方案';

ALTER TABLE public.survey_standard_questionnaire ADD type_id varchar(50) DEFAULT '158341880269115403';
COMMENT ON COLUMN public.survey_standard_questionnaire."type_id" IS '问卷类型';

ALTER TABLE public.survey_standard_questionnaire ADD is_standalone bool NULL DEFAULT false;
COMMENT ON COLUMN public.survey_standard_questionnaire.is_standalone IS '是否独立链接';

ALTER TABLE public.survey_standard_questionnaire ADD corner_mark varchar(50) DEFAULT '';
COMMENT ON COLUMN public.survey_standard_questionnaire."corner_mark" IS '问卷角标';

ALTER TABLE public.survey_standard_questionnaire ADD description text DEFAULT '';
COMMENT ON COLUMN public.survey_standard_questionnaire.description IS '问卷说明';

ALTER TABLE public.survey_standard_questionnaire ALTER COLUMN status SET DEFAULT 'ENABLE';
update survey_standard_questionnaire set status ='ENABLE';