package com.knx.survey.service.api.domain.report.tencent;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knx.survey.model.report.prisma.tencent.PrismaOrganizationDemographicApprovalData;
import com.knx.survey.model.enums.MainlandEnum;
import com.knx.survey.vo.tencent.ImportDimensionScoreResultVo;

import com.knx.survey.vo.tencent.ProjectDataCountVO;
import com.knx.survey.vo.tencent.ScaleDataWarehouseResponse;
import java.util.List;

/**
 * 组织-人口标签维度赞成度数据服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-08
 */
public interface IPrismaOrganizationDemographicApprovalDataService extends IService<PrismaOrganizationDemographicApprovalData> {
    /**
     * 保存维度分数数据
     * @param resultVo 导入的维度分数结果
     */
    void saveDimensionScore(ImportDimensionScoreResultVo resultVo);

    /**
     * 根据项目ID、组织编码列表和大陆类型查询数据
     * @param projectId 项目ID
     * @param organizationCodes 组织编码列表
     * @param mainlandType 大陆类型
     * @return 匹配的数据列表
     */
    List<PrismaOrganizationDemographicApprovalData> getByProjectIdAndOrganizationCodesAndMainlandType(
            String projectId, List<String> organizationCodes, MainlandEnum mainlandType);

    /**
     * 获取规模数据
     * @param projectCode 项目ID
     * @param organizationCodes 组织编码列表
     * @param isMainland 是否大陆 (all/mainland/non-mainland)
     * @return 规模数据响应
     */
    ScaleDataWarehouseResponse getScaleData(String projectCode, List<String> organizationCodes, String isMainland);
    
    /**
     * 根据项目ID、组织编码和大陆类型删除数据
     * @param projectId 项目ID
     * @param organization 组织编码
     * @param mainlandType 大陆类型
     */
    void deleteByProjectAndOrgAndMainlandType(String projectId, String organization, String mainlandType);

    /**
     * 先删除后批量保存组织-人口标签维度赞成度数据
     * @param projectId 项目ID
     * @param organization 组织编码
     * @param mainlandType 大陆类型
     * @param datas 数据列表
     */
    void deleteAndSaveBatch(String projectId, String organization, String mainlandType, List<PrismaOrganizationDemographicApprovalData> datas);

    /**
     * 根据项目ID查询所有PrismaOrganizationDemographicApprovalData的ID
     * @param projectId 项目ID
     * @return ID列表
     */
    List<String> listIdsByProjectId(String projectId);

    /**
     * 统计所有远程项目的组织-人口标签维度赞成度数据中data的数量
     * @param remoteServerAddress 远程服务器地址
     * @param token 认证token
     * @return 统计结果列表，包含项目信息和data数量
     */
    List<ProjectDataCountVO> countAllProjectsOrganizationDemographicApprovalData(String remoteServerAddress, String token);
}
