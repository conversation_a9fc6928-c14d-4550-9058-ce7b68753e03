ALTER TABLE public.survey_question ADD label varchar(100) NULL;
COMMENT ON COLUMN public.survey_question.label IS '标签';
ALTER TABLE public.survey_question ADD sub_survey_type varchar(50) NULL;
COMMENT ON COLUMN public.survey_question.sub_survey_type IS '子调研类型';
ALTER TABLE public.survey_question ADD is_force_select bool NULL DEFAULT true;
COMMENT ON COLUMN public.survey_question.is_force_select IS '是否必选';
ALTER TABLE public.survey_question ADD code varchar(50) NULL;
COMMENT ON COLUMN public.survey_question.code IS '题目编码';


ALTER TABLE public.survey_questionnaire ADD color_config json NULL;
COMMENT ON COLUMN public.survey_questionnaire.color_config IS '颜色配置';
ALTER TABLE public.survey_questionnaire ADD is_show_organization bool NULL DEFAULT false;
COMMENT ON COLUMN public.survey_questionnaire.is_show_organization IS '是否显示组织架构';
ALTER TABLE public.survey_questionnaire ADD is_show_question_book bool NULL DEFAULT false;
COMMENT ON COLUMN public.survey_questionnaire.is_show_question_book IS '是否展示题本';
ALTER TABLE public.survey_questionnaire ADD norm_code varchar(50) NULL;
COMMENT ON COLUMN public.survey_questionnaire.norm_code IS '常模编码';
ALTER TABLE public.survey_questionnaire ADD optional_norm_codes json DEFAULT '[]';
COMMENT ON COLUMN public.survey_questionnaire.optional_norm_codes IS '可选常模编码';

ALTER TABLE public.survey_questionnaire_dimension ADD "scope" varchar(50) NULL DEFAULT 'ALL';
COMMENT ON COLUMN public.survey_questionnaire_dimension."scope" IS '适用对象 全员 普通员工 高管';
ALTER TABLE public.survey_questionnaire_dimension ADD description json NULL;
COMMENT ON COLUMN public.survey_questionnaire_dimension.description IS '维度描述';

ALTER TABLE public.survey_package ADD survey_type varchar(50) NULL DEFAULT 'ASSESSMENT';
COMMENT ON COLUMN public.survey_package.survey_type IS '调研类型';

ALTER TABLE public.survey_demographic_question ADD survey_type varchar(50) NULL DEFAULT 'ASSESSMENT';
COMMENT ON COLUMN public.survey_demographic_question.survey_type IS '调研类型';

ALTER TABLE public.survey_demographic ADD survey_type varchar(50) NULL DEFAULT 'ASSESSMENT';
COMMENT ON COLUMN public.survey_demographic.survey_type IS '调研类型';

ALTER TABLE public.survey_demographic ADD "scope" varchar(50) NULL DEFAULT 'ALL';
COMMENT ON COLUMN public.survey_demographic."scope" IS '适用对象 全员 普通员工 高管';

ALTER TABLE public.survey_person ADD "scope" varchar(50) NULL DEFAULT 'ALL';
COMMENT ON COLUMN public.survey_person."scope" IS '适用对象 全员 普通员工 高管';

CREATE TABLE public.survey_organization_captcha_mapping (
	id varchar(36) NOT NULL,
	project_id varchar(36) NOT NULL,
	captcha varchar(5) NOT NULL,
	organization_id varchar(36) NOT NULL,
	organization_code varchar(36) NOT NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) NULL,
	CONSTRAINT "___pk___survey_organization_captcha_mapping___" PRIMARY KEY ("id"),
	CONSTRAINT "___uk_project_id_captcha___" UNIQUE ("project_id", "captcha")
);
COMMENT ON COLUMN "public"."survey_organization_captcha_mapping"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_organization_captcha_mapping"."captcha" IS '验证码';
COMMENT ON COLUMN "public"."survey_organization_captcha_mapping"."organization_id" IS '组织id';
COMMENT ON COLUMN "public"."survey_organization_captcha_mapping"."organization_code" IS '组织code';
COMMENT ON TABLE "public"."survey_organization_captcha_mapping" IS '问卷组织验证码映射表';

CREATE TABLE public.survey_person_organization_mapping (
    id varchar(36) NOT NULL,
    project_id varchar(36) NOT NULL,
    person_id varchar(36) NOT NULL,
    organization_id varchar(36) NOT NULL,
	organization_code varchar(36) NOT NULL,
    create_by varchar(36) NULL,
    create_time timestamp(3) NULL DEFAULT now(),
    update_time timestamp(3) NULL DEFAULT now(),
    last_update_by varchar(36) NULL,
    is_deleted bool NULL DEFAULT false,
    app_id varchar(36) NULL,
    CONSTRAINT "___pk___survey_person_organization_mapping___" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "public"."survey_person_organization_mapping"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_person_organization_mapping"."person_id" IS '人员id';
COMMENT ON COLUMN "public"."survey_person_organization_mapping"."organization_id" IS '组织id';
COMMENT ON COLUMN "public"."survey_organization_captcha_mapping"."organization_code" IS '组织code';
COMMENT ON TABLE "public"."survey_person_organization_mapping" IS '问卷人员组织映射表';
CREATE INDEX ___idx_project_id___ ON public.survey_person_organization_mapping (project_id);
CREATE INDEX ___idx_person_id___ ON public.survey_person_organization_mapping (person_id);
CREATE INDEX ___idx_organization_id___ ON public.survey_person_organization_mapping (organization_id);

ALTER TABLE public.survey_person_demographic_preset ADD demographic_id varchar(36) NULL;
COMMENT ON COLUMN public.survey_person_demographic_preset.demographic_id IS '人口信息学id';
ALTER TABLE public.survey_person_demographic_preset ADD json_value json NULL;
COMMENT ON COLUMN public.survey_person_demographic_preset.json_value IS '敬业度类型的值';
ALTER TABLE public.survey_person_demographic_preset ADD survey_type varchar(50) NULL DEFAULT 'ASSESSMENT';
COMMENT ON COLUMN public.survey_person_demographic_preset.survey_type IS '调研类型';

ALTER TABLE public.report_template ADD survey_type varchar(50) NULL DEFAULT 'ASSESSMENT';
COMMENT ON COLUMN public.report_template.survey_type IS '调研类型';
