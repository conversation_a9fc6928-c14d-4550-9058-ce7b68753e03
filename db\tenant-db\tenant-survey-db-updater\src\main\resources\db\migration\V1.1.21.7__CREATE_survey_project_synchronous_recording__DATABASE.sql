DROP TABLE IF EXISTS "public"."survey_project_synchronous_recording";
CREATE TABLE public.survey_project_synchronous_recording (
    id varchar(36) NOT NULL,
    step_number int4 NULL DEFAULT 1,
    prisma_project_id varchar(36) NOT NULL,
    project_id varchar(36) NOT NULL,
    prisma_questionnaire_id varchar(50) NULL DEFAULT NULL,
    questionnaire_id varchar(50) NULL DEFAULT NULL,
    page_index int4 NULL DEFAULT 0,
    question_mapping_redis_name varchar(100) NULL DEFAULT NULL,
    organization_mapping_redis_name varchar(100) NULL DEFAULT NULL,
    demographic_mapping_redis_name varchar(100) NULL DEFAULT NULL,
    create_by varchar(36) NULL,
    create_time timestamp(3) NULL DEFAULT now(),
    update_time timestamp(3) NULL DEFAULT now(),
    last_update_by varchar(36) NULL,
    is_deleted bool NULL DEFAULT false,
    app_id varchar(36) NULL,
    CONSTRAINT "___pk___survey_project_synchronous_recording___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."survey_project_synchronous_recording"."id" IS '主键';
COMMENT ON COLUMN "public"."survey_project_synchronous_recording"."step_number" IS '步数';
COMMENT ON COLUMN "public"."survey_project_synchronous_recording"."prisma_project_id" IS 'prisma活动id';
COMMENT ON COLUMN "public"."survey_project_synchronous_recording"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_project_synchronous_recording"."prisma_questionnaire_id" IS 'prisma问卷id';
COMMENT ON COLUMN "public"."survey_project_synchronous_recording"."questionnaire_id" IS '问卷id';
COMMENT ON COLUMN "public"."survey_project_synchronous_recording"."page_index" IS '人员同步页码';
COMMENT ON COLUMN "public"."survey_project_synchronous_recording"."question_mapping_redis_name" IS '题本mapping关系Redis记录名称';
COMMENT ON COLUMN "public"."survey_project_synchronous_recording"."organization_mapping_redis_name" IS '组织mapping关系Redis记录名称';
COMMENT ON COLUMN "public"."survey_project_synchronous_recording"."demographic_mapping_redis_name" IS '人口学mapping关系Redis记录名称';


