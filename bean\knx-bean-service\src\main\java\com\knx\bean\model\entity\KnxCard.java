package com.knx.bean.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.knx.bean.model.enums.KnxCardTypeEnum;
import com.knx.common.base.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/10/16 13:13
 */

@Data
@ApiModel("肯豆卡")
public class KnxCard extends BaseModel {

    @ApiModelProperty(value = "卡密", example = "1331184718107910145")
    @NotBlank(message = "卡密不能为空")
    private String cardPassword;

    @ApiModelProperty("是否被使用")
    private Boolean isUsed;

    @ApiModelProperty("过期时间")
    @NotNull(message = "过期时间不能为空")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private Date expireDate;

    @ApiModelProperty("金额")
    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

    @ApiModelProperty(value = "租户id", example = "1331184718107910145")
    private String tenantId;

    @ApiModelProperty("卡片类型")
    @NotNull(message = "卡片类型不能为空")
    private KnxCardTypeEnum cardType;

    @ApiModelProperty("是否充值")
    private Boolean isRecharge;

    @ApiModelProperty("使用时间")
    private Date useDate;

    @ApiModelProperty("充值时间")
    private Date rechargeDate;

    @ApiModelProperty("卡片数量")
    @NotNull(message = "卡片数量不能为空")
    @TableField(exist = false)
    private Integer num;

}
