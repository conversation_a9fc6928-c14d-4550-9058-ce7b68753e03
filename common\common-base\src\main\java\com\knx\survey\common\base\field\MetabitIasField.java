package com.knx.survey.common.base.field;

import com.knx.survey.model.I18NString;
import com.knx.survey.model.SagI18NString;

public class MetabitIasField {

    public static I18NString DIMENSION = new SagI18NString("维度", "Dimension");

    public static I18NString QUESTION = new SagI18NString("", "Item");

    public static I18NString QUESTION_NAME = new SagI18NString("题目", "Item");

    public static I18NString DEPARTMENT_NAME = new SagI18NString("部门", "Department");

    public static I18NString NEXT_DEPARTMENT_NAME = new SagI18NString("下一级部门", "Department");
    public static I18NString ANALYSIS_OBJECT = new SagI18NString("分析主体", "Analysis Object");

    public static I18NString CORRELATION_NAME = new SagI18NString("相关系数", "Correlation");

    public static I18NString CORRELATION_TOP_10_NAME = new SagI18NString("相关前10", "Correlation Top 10");

    public static I18NString IS_LOWEST_ITEM = new SagI18NString("是否最低分", "Among Lowest 10 Scores");

    public static I18NString SCORE_BOTTOM_10_NAME = new SagI18NString("得分后10", "Score Bottom 10");

    public static I18NString SCORE_TOP_10_NAME = new SagI18NString("得分前10", "Score Top 10");

    public static I18NString LAST_10_NAME = new SagI18NString("后10", " Bottom 10");

    public static I18NString TOP_10_NAME = new SagI18NString("前10", " Top 10");

    public static I18NString FOCUS_NAME = new SagI18NString("关注度", "Focus");

    public static I18NString DIMENSION_QUESTION = new SagI18NString("维度/题目", "Dimension/Item");

    public static I18NString NORM_DIVIDED_AREA_NAME = new SagI18NString("常模切分区域", "Norm-divided Area");

    public static I18NString ORGANIZATION_NAME = new SagI18NString("组织", "Organization");

    public static I18NString ANALYSIS_OBJECT_NAME = new SagI18NString("分析主体", "Analysis Object");

    public static I18NString NORM_NAME = new SagI18NString("常模", "Norm");

    public static I18NString AREA_NAME = new SagI18NString("所属区域", "Area");

    public static I18NString NO_NAME = new SagI18NString("序号", "No");

    public static I18NString NAME_NAME = new SagI18NString("名称", "Name");

    public static I18NString APPROVAL_ABBR_NAME = new SagI18NString("赞成百分比", "Agreement");

    public static I18NString NEUTRAL_ABBR_NAME = new SagI18NString("中立百分比", "Neutral");

    public static I18NString DISAPPROVAL_ABBR_NAME = new SagI18NString("不赞成百分比", "Disagreement");

    public static I18NString VALID_NUMBER_NAME = new SagI18NString("有效填答人数", "Valid Number");

    public static I18NString EMPLOYEE_GROUP_NAME = new SagI18NString("人群分类", "Employee group");

    public static I18NString INDEX_NAME = new SagI18NString("指数", "Index");

    public static I18NString EEI_NAME = new SagI18NString("敬业度指数", "EEI");

    public static I18NString EEI_ABBR_NAME = new SagI18NString("敬业度", "Engagement");

    public static I18NString ESI_NAME = new SagI18NString("满意度指数", "ESI");

    public static I18NString ESI_ABBR_NAME = new SagI18NString("满意度", "Satisfaction");

    public static I18NString OCI_NAME = new SagI18NString("组织能力指数", "OCI");

    public static I18NString EMPTY_NAME = new SagI18NString("", "");

    public static I18NString COLON_NAME = new SagI18NString("：", ": ");

    public static I18NString COMMA_NAME = new SagI18NString("，", ", ");

    public static I18NString HIGHEST_NAME = new SagI18NString("最高", "");

    public static I18NString LOWEST_NAME = new SagI18NString("最低", "");

    public static I18NString SUPERIOR_DEPARTMENT_NAME = new SagI18NString("上一级", "Superior Department");

    public static I18NString HISTORY_CONTRAST_NAME = new SagI18NString("VS历史文字","VS History Word");

    public static I18NString INNER_NORM_NAME = new SagI18NString("VS公司整体","VS All Company");
    public static I18NString CONTRAST_MAIN = new SagI18NString("VS分析主体", "VS Analysis Subject");
    public static I18NString OVERALL_RATING_AVERAGE = new SagI18NString("总评均分", "Overall rating average");

    public static I18NString ANALYSIS_OBJECT_EEI_NAME = new SagI18NString("分析主体敬业度指数", "Analysis Object EEI Name");

    public static I18NString ANALYSIS_OBJECT_OCI_NAME = new SagI18NString("分析主体组织能力指数", "Analysis Object OCI Name");

    public static I18NString KEY_DRIVING_FACTOR_QUESTION_NAME = new SagI18NString("关键驱动因素问题", "Key driving factor issues");

    public static I18NString BELONGING_DIMENSION_NAME = new SagI18NString("所属维度", "Belonging Dimension");

    public static I18NString CORRELATION_COEFFICIENT = new SagI18NString("关联系数", "Correlation Coefficient");

    public static I18NString QUESTION_SCORE = new SagI18NString("题目得分", "Question Score");

    public static I18NString IS_LOWEST_SCORE_ITEM = new SagI18NString("是否最低分项目", "Is Lowest Score Item");

    public static I18NString IS_HIS_GAP_LARGEST = new SagI18NString("是否历史差距最大", "Is Historical Gap The Largest");

    public static I18NString IS_INNER_DIFF_LARGEST = new SagI18NString("是否与内部比较差距最大", "Is Biggest Difference Compared To The Internal Comparison");

    public static I18NString INVITATION_NUMBER_NAME = new SagI18NString("邀请人数", "Invitation Number");

    public static I18NString ANSWER_NUMBER_NAME = new SagI18NString("填答人数", "Answer Number");

    public static I18NString VALID_RANK_NAME = new SagI18NString("有效填答率", "Valid RANK");

    public static I18NString VS = new SagI18NString("VS", "VS");

    public static I18NString QUADRANT = new SagI18NString("象限", "Quadrant");
}
