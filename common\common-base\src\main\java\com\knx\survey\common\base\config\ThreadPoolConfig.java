package com.knx.survey.common.base.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.util.concurrent.*;

@Slf4j
@Configuration
@ConfigurationProperties(prefix = "sag.thread.pool")
public class ThreadPoolConfig {
    /**
     * 线程池核心池的大小
     */
    @Value("${corePoolSize:5}")
    private int corePoolSize;

    /**
     * 线程池的最大线程数
     */
    @Value("${maxPoolSize:10}")
    private int maxPoolSize;

    /**
     * 队列容量
     */
    @Value("${queueCapacity:100}")
    private int queueCapacity;

    /**
     * 空闲线程存活时间
     */
    @Value("${keepAliveSeconds:10}")
    private long keepAliveSeconds;

    /**
     * 常规服务线程池
     */
    @Lazy
    @Bean(value = "baseThreadPool")
    public ExecutorService buildBaseThreadPool() {
        log.info("TreadPoolConfig-baseThreadPool-创建线程数:{},最大线程数:{}，队列容量:{},空闲线程存活时间:{}", +corePoolSize, maxPoolSize, queueCapacity, keepAliveSeconds);
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat("baseThreadPool" + "-%d")
                .build();
        ThreadPoolExecutor pool = new ThreadPoolExecutor(corePoolSize, maxPoolSize, keepAliveSeconds, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(100), threadFactory);
        return pool;
    }

    /**
     * 定时任务线程池
     */
    @Lazy
    @Bean(value = "jobThreadPool")
    public ExecutorService buildJobThreadPool() {
        int jobCorePoolSize = 5;
        log.info("TreadPoolConfig-jobThreadPool-创建线程数:{}", jobCorePoolSize);
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat("jobThreadPool" + "-%d")
                .build();
        //ExecutorService pool = Executors.newFixedThreadPool(jobCorePoolSize, threadFactory);
        ThreadPoolExecutor pool = new ThreadPoolExecutor(jobCorePoolSize, jobCorePoolSize, keepAliveSeconds, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), threadFactory);
        return pool;
    }

    /**
     * prisma定时任务线程池
     */
    @Lazy
    @Bean(value = "prismaJobThreadPool")
    public ExecutorService buildPrismaJobThreadPool() {
        int jobCorePoolSize = 2;
        log.info("TreadPoolConfig-prismaJobThreadPool-创建线程数:{}", jobCorePoolSize);
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat("prismaJobThreadPool" + "-%d")
                .build();
        //ExecutorService pool = Executors.newFixedThreadPool(jobCorePoolSize, threadFactory);
        ThreadPoolExecutor pool = new ThreadPoolExecutor(jobCorePoolSize, jobCorePoolSize, keepAliveSeconds, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), threadFactory);
        return pool;
    }

    /**
     * 总后台定时任务线程池
     */
    @Lazy
    @Bean(value = "adminJobThreadPool")
    public ExecutorService buildAdminJobThreadPool() {
        int jobCorePoolSize = 0;
        log.info("TreadPoolConfig-adminJobThreadPool-创建线程数:{}", jobCorePoolSize);
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat("adminJobThreadPool" + "-%d")
                .build();
        //ExecutorService pool = Executors.newFixedThreadPool(jobCorePoolSize, threadFactory);
        ThreadPoolExecutor pool = new ThreadPoolExecutor(jobCorePoolSize, 2, keepAliveSeconds, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), threadFactory);
        return pool;
    }

    /**
     * 调研计算线程池
     */
    @Lazy
    @Bean(value = "iasThreadPool")
    public ExecutorService buildIasThreadPool() {
        int jobCorePoolSize = 4;
        log.info("TreadPoolConfig-iasThreadPool-创建线程数:{}", jobCorePoolSize);
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat("iasThreadPool" + "-%d")
                .build();
        //ExecutorService pool = Executors.newFixedThreadPool(jobCorePoolSize, threadFactory);
        ThreadPoolExecutor pool = new ThreadPoolExecutor(jobCorePoolSize, jobCorePoolSize * 2, keepAliveSeconds, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), threadFactory);
        pool.allowCoreThreadTimeOut(true);
        return pool;
    }
}