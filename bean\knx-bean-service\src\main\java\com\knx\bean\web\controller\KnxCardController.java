package com.knx.bean.web.controller;

import com.alibaba.excel.write.handler.WriteHandler;
import com.knx.bean.model.entity.KnxCard;
import com.knx.bean.model.enums.KnxCardTypeEnum;
import com.knx.bean.model.vo.CardResultExcelVO;
import com.knx.bean.model.vo.CardSelectVO;
import com.knx.bean.service.api.IKnxCardService;
import com.knx.common.base.web.response.IDValues;
import com.knx.common.base.web.response.PageRequest;
import com.knx.common.base.web.response.WebResponse;
import com.knx.common.util.excelUtil.handler.SpinnerWriteHandler;
import com.knx.common.util.excelUtil.utils.EasyExcelParams;
import com.knx.common.util.excelUtil.utils.ExcelUtil;
import com.knx.common.util.excelUtil.utils.SheetData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/10/16 14:11
 */

@RestController
@Api(tags = "肯豆卡")
@RequestMapping("/knx/bean/card")
public class KnxCardController {

    @Autowired
    private IKnxCardService cardService;

    @ApiOperation(value = "创建卡", notes = "创建卡")
    @PostMapping("/create")
    public WebResponse<IDValues> create(@RequestBody @Valid List<KnxCard> cards) {
        List<KnxCard> knxCards = cardService.saveBatchCard(cards);
        return WebResponse.returnCreateResults(knxCards);
    }

    @ApiOperation(value = "修改卡使用状态", notes = "修改卡使用状态")
    @PostMapping("/updateBatch")
    public WebResponse<Integer> updateBatch(@RequestBody List<KnxCard> cards) {
        Integer num = cardService.updateBatchByPassword(cards);
        return WebResponse.returnSuccessData(num);
    }

    @ApiOperation(value = "修改卡充值状态", notes = "修改卡充值状态")
    @PostMapping("/updateRechargeBatch")
    public WebResponse<Integer> updateRechargeBatch(@RequestBody List<KnxCard> cards) {
        Integer num = cardService.updateRechargeBatchByPassword(cards);
        return WebResponse.returnSuccessData(num);
    }

    @ApiOperation(value = "按卡密批量查询", notes = "按卡密批量查询")
    @PostMapping("/listByCardPasswords")
    public WebResponse<List<KnxCard>> listByCardPasswords(@RequestBody List<String> cardPasswords) {
        List<KnxCard> knxCards = cardService.listByCardPassword(cardPasswords);
        return WebResponse.returnSuccessData(knxCards);
    }


    @ApiOperation(value = "分页条件查询", notes = "分页条件查询")
    @PostMapping("/list")
    public WebResponse list(@RequestBody CardSelectVO cardSelectVO) {
        PageRequest pageRequest = cardService.pageList(cardSelectVO);
        return WebResponse.returnPageResult(pageRequest);
    }

    @ApiOperation(value = "获取卡片类型和值", notes = "获取卡片类型和值")
    @GetMapping("/getKnxCardTypeEnum")
    public WebResponse<Map<String, String>> getKnxCardTypeEnum() {
        KnxCardTypeEnum[] values = KnxCardTypeEnum.values();
        Map<String, String> map = new HashMap<>();
        for (KnxCardTypeEnum value : values) {
            String key = value.name();
            String name = value.getName();
            map.put(key, name);
        }
        return WebResponse.returnSuccessData(map);
    }

    @ApiOperation(value = "校验卡密", notes = "校验卡密")
    @GetMapping("/checkoutCardPassword")
    public WebResponse<String> checkoutCardPassword(@ApiParam(value = "卡密", name = "password") String password) {
        String message = cardService.checkoutCardPassword(password);
        return WebResponse.returnSuccessData(message);
    }

    @ApiOperation(value = "批量校验卡密", notes = "批量校验卡密")
    @PostMapping("/batchCheckoutCardPassword")
    public WebResponse<Boolean> batchCheckoutCardPassword(@RequestBody List<String> passwords) {
        Boolean result = cardService.batchCheckoutCardPassword(passwords);
        return WebResponse.returnSuccessData(result);
    }

    @ApiOperation(value = "批量校验卡密是否已充值", notes = "批量校验卡密是否已充值")
    @PostMapping("/batchCheckoutCardPasswordIsRecharge")
    public WebResponse<Boolean> batchCheckoutCardPasswordIsRecharge(@RequestBody List<String> passwords) {
        Boolean result = cardService.batchCheckoutCardPasswordIsRecharge(passwords);
        return WebResponse.returnSuccessData(result);
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出卡密", notes = "导出卡密", produces = "application/octet-stream")
    public void export(HttpServletResponse response, @RequestBody CardSelectVO cardSelectVO) throws IOException {
        List<CardResultExcelVO> cardResultExcelVOS = cardService.listExcel(cardSelectVO);
        EasyExcelParams easyExcelParams = new EasyExcelParams();
        SheetData sheetData = new SheetData();
        sheetData.setDataModelClazz(CardResultExcelVO.class);
        sheetData.setSheetName("第一个sheet");
        sheetData.setData(cardResultExcelVOS);
        easyExcelParams.setResponse(response);
        easyExcelParams.setExcelNameWithoutExt("卡片");
        List<SheetData> sheetDatas = new ArrayList<>();
        sheetDatas.add(sheetData);
        WriteHandler spinnerWriteHandler = new SpinnerWriteHandler();
        List<WriteHandler> handlers = new ArrayList<>();
        handlers.add(spinnerWriteHandler);
        easyExcelParams.setWriteHandlers(handlers);
        easyExcelParams.setSheetDatas(sheetDatas);
        ExcelUtil.exportExcel2007(easyExcelParams);

    }
}
