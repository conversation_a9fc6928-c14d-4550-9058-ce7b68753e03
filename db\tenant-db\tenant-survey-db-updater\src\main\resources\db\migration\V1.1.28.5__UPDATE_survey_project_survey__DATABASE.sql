DROP TABLE IF EXISTS "public"."survey_project_permission";
CREATE TABLE survey_project_permission (
                                           id VARCHAR ( 36 ) NOT NULL,
                                           project_id VARCHAR ( 36 ) NOT NULL,
                                           user_id VARCHAR ( 36 ) NOT NULL,
                                           answer_progress_organization_id VARCHAR ( 36 ),
                                           organization_id VARCHAR ( 36 ),
                                           factor_id VARCHAR ( 36 ),
                                           answer_progress_factor_id VARCHAR ( 36 ),
                                           inner_norm VARCHAR ( 50 ),
                                           prisma_norm_id VARCHAR ( 36 ),
                                           history_id VARCHAR ( 36 ),
                                           type VARCHAR ( 50 ) NOT NULL,
                                           create_by VARCHAR ( 36 ) NULL,
                                           create_time TIMESTAMP ( 3 ) NULL DEFAULT now( ),
                                           update_time TIMESTAMP ( 3 ) NULL DEFAULT now( ),
                                           last_update_by VARCHAR ( 36 ) NULL,
                                           is_deleted BOOL NULL DEFAULT FALSE,
                                           app_id VARCHAR ( 36 ) NULL,
                                           CONSTRAINT "___pk__survey_project_permission___" PRIMARY KEY ( id )
);
COMMENT ON TABLE "public"."survey_project_permission" IS '活动权限表';
COMMENT ON COLUMN "public"."survey_project_permission"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_project_permission"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."survey_project_permission"."organization_id" IS '创建细分报可以选择的组织';
COMMENT ON COLUMN "public"."survey_project_permission"."factor_id" IS '创建细分报可以选择的分析因子';
COMMENT ON COLUMN "public"."survey_project_permission"."prisma_norm_id" IS '创建细分报可以选择的外部常模';
COMMENT ON COLUMN "public"."survey_project_permission"."inner_norm" IS '创建细分报可以选择的内部常模';
COMMENT ON COLUMN "public"."survey_project_permission"."type" IS '权限类型';

DROP TABLE IF EXISTS "public"."survey_project_user_permission";
CREATE TABLE survey_project_user_permission (
                                           id VARCHAR ( 36 ) NOT NULL,
                                           project_id VARCHAR ( 36 ) NOT NULL,
                                           user_id VARCHAR ( 36 ) NOT NULL,
                                           is_creator BOOL DEFAULT FALSE,
                                           is_main_manager BOOL DEFAULT FALSE,
                                           is_view_answer_progress_organization BOOL DEFAULT FALSE,
                                           is_view_answer_progress_factor BOOL DEFAULT FALSE,
                                           is_select_organization  BOOL DEFAULT FALSE,
                                           is_select_factor  BOOL DEFAULT FALSE,
                                           is_select_norm  BOOL DEFAULT FALSE,
                                           is_select_history  BOOL DEFAULT FALSE,
                                           create_by VARCHAR ( 36 ) NULL,
                                           create_time TIMESTAMP ( 3 ) NULL DEFAULT now( ),
                                           update_time TIMESTAMP ( 3 ) NULL DEFAULT now( ),
                                           last_update_by VARCHAR ( 36 ) NULL,
                                           is_deleted BOOL NULL DEFAULT FALSE,
                                           app_id VARCHAR ( 36 ) NULL,
                                           CONSTRAINT "___pk__survey_project_user_permission___" PRIMARY KEY ( id )
);
COMMENT ON TABLE "public"."survey_project_user_permission" IS '活动权限表';
COMMENT ON COLUMN "public"."survey_project_user_permission"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."survey_project_user_permission"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."survey_project_user_permission"."is_creator" IS '用户是否是活动的创建者';
COMMENT ON COLUMN "public"."survey_project_user_permission"."is_main_manager" IS '用户是否是主管理员';
COMMENT ON COLUMN "public"."survey_project_user_permission"."is_view_answer_progress_organization" IS '是否可以按组织查看填答进度(仅调研)';
COMMENT ON COLUMN "public"."survey_project_user_permission"."is_view_answer_progress_factor" IS '是否可以按分析因子查看填答进度(仅调研)';
COMMENT ON COLUMN "public"."survey_project_user_permission"."is_select_organization" IS '创建细分报告是否可以选择组织';
COMMENT ON COLUMN "public"."survey_project_user_permission"."is_select_factor" IS '创建细分报可以选择分析因子';
COMMENT ON COLUMN "public"."survey_project_user_permission"."is_select_norm" IS '创建细分报是否可以选择内外部常模';
COMMENT ON COLUMN "public"."survey_project_user_permission"."is_select_history" IS '创建细分报是否可以选择历史数据';
