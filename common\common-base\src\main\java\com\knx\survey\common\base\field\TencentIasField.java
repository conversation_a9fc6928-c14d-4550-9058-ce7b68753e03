package com.knx.survey.common.base.field;

import com.knx.survey.model.I18NString;
import com.knx.survey.model.SagI18NString;

public class TencentIasField {

    public static I18NString NAME_NAME = new SagI18NString("名称", "Name");

    public static I18NString APPROVAL_ABBR_NAME = new SagI18NString("赞成", "Agreement");

    public static I18NString NEUTRAL_ABBR_NAME = new SagI18NString("中立", "Neutral");

    public static I18NString DISAPPROVAL_ABBR_NAME = new SagI18NString("不赞成", "Disagreement");

    public static I18NString VALID_NUMBER_NAME = new SagI18NString("有效应答人数", "Number of effective responses");

    public static I18NString EMPLOYEE_GROUP_NAME = new SagI18NString("人群分类", "Employee group");

    public static I18NString THE_ORGANIZATION_NAME = new SagI18NString("本组织", "Your organization");

    public static I18NString INDEX_NAME = new SagI18NString("指数", "Index");

    public static I18NString EEI_NAME = new SagI18NString("敬业度指数", "EEI");

    public static I18NString EEI_ABBR_NAME = new SagI18NString("敬业度", "Engagement");

    public static I18NString ESI_NAME = new SagI18NString("满意度指数", "ESI");

    public static I18NString ESI_ABBR_NAME = new SagI18NString("满意度", "Satisfaction");

    public static I18NString FIRST_LEVEL_DIMENSION_NAME = new SagI18NString("一级维度", "Level-1 Dimension");

    public static I18NString SECOND_LEVEL_DIMENSION_NAME = new SagI18NString("二级维度", "Level-2 Dimension");

    public static I18NString QUESTIONNAIRE_QUESTION_NAME = new SagI18NString("问卷题目", "Question");

    public static I18NString DESCRIPTION_NAME = new SagI18NString("解释", "");

    public static I18NString RELATIVELY_HIGH_SCORE_NAME = new SagI18NString("相对得分较高的是：", "Employee groups giving relatively high scores: ");

    public static I18NString RELATIVELY_LOW_SCORE_NAME = new SagI18NString("相对得分较低的是：", "Employee groups giving relatively low scores: ");

    public static I18NString SIGNIFICANTLY_INCREASED_SCORE_NAME = new SagI18NString("与上一年比，明显上升数量最多的是：", "Employee groups giving significantly-increased scores to the most dimensions: ");

    public static I18NString SIGNIFICANTLY_DECREASED_SCORE_NAME = new SagI18NString("与上一年比，明显下降数量最多的是：", "Employee groups giving significantly-decreased scores to the most dimensions: ");

    public static I18NString DIMENSION_NAME = new SagI18NString("维度", "Dimension");

    public static I18NString BELONG_DIMENSION_NAME = new SagI18NString("所属维度", "Dimension");

    public static I18NString QUESTION_SCORE_NAME = new SagI18NString("题目得分", "Score");

    public static I18NString DIMENSION_SCORE_NAME = new SagI18NString("维度得分", "Dimension score");

    public static I18NString ORGANIZATION_NAME = new SagI18NString("组织", "Organization");

    public static I18NString QUESTION_NAME = new SagI18NString("题目", "Question");

    public static I18NString BOTTOM_10_NAME = new SagI18NString("后10", "Ranking of the Largest 10 Differences Vs. ");
    public static I18NString THIS_YEAR_BOTTOM_10_NAME = new SagI18NString("题目得分后10", "Ranking of the Lowest 10 Scores");

    public static I18NString LAST_YEAR_BOTTOM_10_NAME = new SagI18NString("vs 上一年后10", "Ranking of the Largest 10 Declines from Last Year");

    public static I18NString SUPERIOR_ORGANIZATION_NAME = new SagI18NString("上一级组织", "Upper-Level Organization");

    public static I18NString SUPERIOR_ORGANIZATION_BOTTOM_10_NAME = new SagI18NString("vs 上一级组织后10", "Ranking of the Largest 10 Differences Vs. Upper-Level Organization");

    public static I18NString IS_KDA_QUESTION_NAME = new SagI18NString("是否关键驱动问题", "Key Driver");

    public static I18NString HIGHEST_SCORE_QUESTION_NAME = new SagI18NString("最高分问题", "Questions with the Highest Scores");

    public static I18NString LOWEST_SCORE_QUESTION_NAME = new SagI18NString("最低分问题", "Questions with the Lowest Scores");

    public static I18NString KDA_QUESTION_NAME = new SagI18NString("关键驱动因素问题", "Question");

    public static I18NString MODEL_NAME = new SagI18NString("模型", "Index");

    public static I18NString FOCUS = new SagI18NString("关注度(劣势)", "Level of Attention Required");

    public static I18NString EMPTY_NAME = new SagI18NString("", "");

    public static I18NString CORRELATION_NAME = new SagI18NString("关联系数", "Correlation Coefficient");

    public static I18NString IS_LOWEST_ITEM = new SagI18NString("是否最低分项目", "Among Lowest 10 Scores");

    public static I18NString PAST_YEAR_KDA = new SagI18NString("是否上年KDA", "Key Driver of Last Year");

    public static I18NString THIS_YEAR = new SagI18NString("今年", "This year");

    public static I18NString LAST_YEAR_NAME = new SagI18NString("上一年", "Last Year");

    public static I18NString OPTION_NAME = new SagI18NString("选项", "Option");

    public static I18NString PERCENT_NAME = new SagI18NString("占比", "Percent");

    public static I18NString Y_NAME = new SagI18NString("Y", "Y");

    public static I18NString AVG_NAME = new SagI18NString("平均分", "Average score");

    public static I18NString TENCENT_COMPANY = new SagI18NString("腾讯整体(Tencent)", "Tencent");
    public static I18NString TENCENT_COMPANY_ALL = new SagI18NString("公司整体", "");
    public static I18NString TENCENT_SUPER_ORG = new SagI18NString("上一级组织", "Upper-Level Organization");

    public static I18NString TENCENT_CROSS_ANALYSIS = new SagI18NString("本报告分析主体", "Target Employees for Cross-Group Analysis");

    public static I18NString TENCENT_CROSS_ANALYSIS_1 = new SagI18NString("交叉分析", "Cross Analysis");

    public static I18NString TENCENT_WHOLE = new SagI18NString("全体", "");

    public static I18NString TENCENT_ORG = new SagI18NString("本组织", "");

    public static I18NString EEI_VS_LAST_YEAR_NAME = new SagI18NString("敬业度指数 -上一年差值", "Engagement vs Last year");

    public static I18NString ESI_VS_LAST_YEAR_NAME = new SagI18NString("满意度指数 -上一年差值", "Satisfaction vs Last year");

    public static I18NString NUMBER_OF_EFFECTIVE_RESPONSES_NAME = new SagI18NString("有效应答人数（人）", "Number of effective responses");

    public static I18NString COLON_NAME = new SagI18NString("：", ": ");

    public static I18NString COMMA_NAME = new SagI18NString("，", ", ");

    public static I18NString FULL_STOP_NAME = new SagI18NString("。", ". ");

    public static I18NString SEMICOLON_NAME = new SagI18NString("；", "; ");
    public static I18NString HIGHEST_NAME = new SagI18NString("最高", "");

    public static I18NString LOWEST_NAME = new SagI18NString("最低", "");

    public static I18NString TOP_SCORE_ORGANIZATION_NAME = new SagI18NString("相对优势最多的组织是：", "Organizations with the biggest number of high scores: ");

    public static I18NString LAST_SCORE_ORGANIZATION_NAME = new SagI18NString("相对劣势最多的组织是：", "Organizations with the biggest number of low scores: ");

    public static I18NString IMPROVED_DIMENSION_ORGANIZATION_NAME = new SagI18NString("维度明显上升数量最多的组织是：", "Organizations with the highest number of improved dimensions: ");

    public static I18NString DECLINED_DIMENSION_ORGANIZATION_NAME = new SagI18NString("维度明显下降数量最多的组织是：", "Organizations with the highest number of declined dimensions: ");

    public static I18NString SQUARE_BRACKET_LEFT_NAME = new SagI18NString("【", "[");

    public static I18NString SQUARE_BRACKET_RIGHT_NAME = new SagI18NString("】", "]");

    public static I18NString SCORE_NAME = new SagI18NString("得分", "Approval");

    public static I18NString DISTRIBUTE_QUESTION_GROUP_NAME = new SagI18NString("定制题目面向群体", "");

    public static I18NString BELONG_ORGANIZATION_NAME = new SagI18NString("所属组织", "");

    public static I18NString BELONG_DEMOGRAPHIC_NAME = new SagI18NString("所属人群", "");
    public static I18NString DISTRIBUTE_QUESTION_GROUP_REMARK = new SagI18NString("备注", "");

    public static I18NString LABEL_NAME = new SagI18NString("标签类别", "Demographic Label");

    public static I18NString LABEL_CONTENT_NAME = new SagI18NString("标签内容", "Group");

    public static I18NString FOCUS_ON_NAME = new SagI18NString("重点关注", "");

}
