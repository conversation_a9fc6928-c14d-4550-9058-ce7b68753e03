ALTER TABLE public.survey_role ALTER COLUMN "name" TYPE text USING "name"::text;

UPDATE "public"."survey_role" SET "name" = '' WHERE "name" is null ;

UPDATE "public"."survey_role" SET "name" = CONCAT('{"en_US":"","zh_CN":"',name,'"}');


ALTER TABLE public.survey_role ALTER COLUMN "investigator_title" TYPE text USING "investigator_title"::text;

UPDATE "public"."survey_role" SET "investigator_title" = '' WHERE "investigator_title" is null ;

UPDATE "public"."survey_role" SET "investigator_title" = CONCAT('{"en_US":"","zh_CN":"',investigator_title,'"}');