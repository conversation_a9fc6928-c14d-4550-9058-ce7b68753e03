package com.knx.survey.common.util;

import cn.hutool.core.util.HexUtil;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AesEncryption.java
 * @Description TODO
 * @createTime 2023年09月06日 16:27:00
 */
@Slf4j
public class AesEncryption {

    private static Map<String,SecretKeySpec> secretKeyMap = new HashMap<>();
    private static Map<String,byte[]> keyMap = new HashMap<>();

    // 初始化密钥
    public static void setKey(String myKey) {
        MessageDigest sha = null;
        try {
            if(!secretKeyMap.containsKey(myKey)){
                byte[] key = myKey.getBytes(StandardCharsets.UTF_8);
                sha = MessageDigest.getInstance("SHA-1");
                key = sha.digest(key);
                key = Arrays.copyOf(key, 16);
                SecretKeySpec secretKey = new SecretKeySpec(key, "AES");

                secretKeyMap.put(myKey,secretKey);
                keyMap.put(myKey,key);
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
    }

    // 加密方法
    public static String encrypt(String strToEncrypt, String secret) {
        setKey(secret);
        try {
            SecretKeySpec secretKey = secretKeyMap.get(secret);
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            return HexUtil.encodeHexStr(cipher.doFinal(strToEncrypt.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            log.warn("Error while encrypting: ",e);
        }
        return null;
    }

    // 解密方法
    public static String decrypt(String strToDecrypt, String secret) {
        setKey(secret);
        try {
            SecretKeySpec secretKey = secretKeyMap.get(secret);
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            return new String(cipher.doFinal(HexUtil.decodeHex(strToDecrypt)));
        } catch (Exception e) {
            log.warn("Error while decrypting:",e);
        }
        return null;
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        String key = "z%Oy4KUK";
        String message = "1699246776541294593";

        String encMsg = AesEncryption.encrypt(message,key);
        encMsg = URLEncoder.encode(encMsg,"UTF-8");
        System.out.println(encMsg);
        System.out.println(AesEncryption.decrypt(URLDecoder.decode(encMsg,"UTF-8"), key));
    }
}