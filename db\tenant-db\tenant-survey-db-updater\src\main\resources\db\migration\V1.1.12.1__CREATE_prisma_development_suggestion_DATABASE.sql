DROP TABLE IF EXISTS prisma_development_suggestion;
CREATE TABLE prisma_development_suggestion
(
    "id"                              VARCHAR(36)  NOT NULL,
    "prisma_report_data_id"           VARCHAR(36)  NOT NULL,
    "category"                        json  NOT NULL,
    "content"                         json DEFAULT NULL,
    "is_default"                      BOOL DEFAULT false,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___prisma_development_suggestion___" PRIMARY KEY ("id")
);
COMMENT ON TABLE  "public"."prisma_development_suggestion" IS 'prisma发展建议表';
COMMENT ON COLUMN "public"."prisma_development_suggestion"."prisma_report_data_id" IS 'prisma报告ID';
COMMENT ON COLUMN "public"."prisma_development_suggestion"."category" IS '类别';
COMMENT ON COLUMN "public"."prisma_development_suggestion"."content" IS '内容';
COMMENT ON COLUMN "public"."prisma_development_suggestion"."is_default" IS '是否默认';

CREATE INDEX ___idx_prisma_development_suggestion_prisma_report_data_id___ ON public.prisma_development_suggestion(prisma_report_data_id);
