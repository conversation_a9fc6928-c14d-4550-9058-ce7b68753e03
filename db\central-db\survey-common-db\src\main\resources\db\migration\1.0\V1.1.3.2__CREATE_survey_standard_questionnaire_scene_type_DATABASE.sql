DROP TABLE IF EXISTS "public"."survey_standard_questionnaire_scene_type";
CREATE TABLE survey_standard_questionnaire_scene_type (
	id varchar(36) NOT NULL,
	name varchar(50) NULL,
    type varchar(50) NOT NULL,
	sort int4 default 999,
	is_default bool NULL DEFAULT false,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__survey_standard_questionnaire_scene_type___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."survey_standard_questionnaire_scene_type"."name" IS '名称';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_scene_type"."type" IS '类型（标准产品、解决方案、产品类型）';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_scene_type"."sort" IS '排序号';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_scene_type"."is_default" IS '是否默认';
COMMENT ON TABLE  "public"."survey_standard_questionnaire_scene_type" IS '标准问卷场景类型表';
