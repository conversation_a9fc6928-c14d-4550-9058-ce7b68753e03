DROP TABLE IF EXISTS "public"."prisma_report_keyword_merge";
CREATE TABLE public.prisma_report_keyword_merge (
    id varchar(36) NOT NULL,
    prisma_report_data_id varchar(36) DEFAULT NULL,
    question_id varchar(36) DEFAULT NULL,
    origin_keyword varchar(200) DEFAULT NULL,
    fix_keyword varchar(200) DEFAULT NULL,
    merge_into_keyword varchar(200) DEFAULT NULL,
    create_by varchar(36) DEFAULT NULL,
    create_time timestamp(3) NULL DEFAULT now(),
    update_time timestamp(3) NULL DEFAULT now(),
    last_update_by varchar(36) NULL,
    is_deleted bool NULL DEFAULT false,
    app_id varchar(36) NULL,
    CONSTRAINT "___pk___prisma_report_keyword_merge___" PRIMARY KEY (id)
);
COMMENT ON TABLE  "public"."prisma_report_keyword_merge" IS 'prisma关键词合并';
COMMENT ON COLUMN "public"."prisma_report_keyword_merge"."prisma_report_data_id" IS 'prisma报告ID';
COMMENT ON COLUMN "public"."prisma_report_keyword_merge"."question_id" IS '题目id';
COMMENT ON COLUMN "public"."prisma_report_keyword_merge"."origin_keyword" IS '原始词名称';
COMMENT ON COLUMN "public"."prisma_report_keyword_merge"."fix_keyword" IS '修正词名称';
COMMENT ON COLUMN "public"."prisma_report_keyword_merge"."merge_into_keyword" IS '合并到其他关键词';
