package com.knx.survey.common.util.excelUtil.pojo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/6/11 14:16
 */

@Data
@Accessors(chain = true)
public class ErrRows {
    private static final long serialVersionUID = 1L;

    /**
     * sheetNum(从0开始)
     */
    private String sheetNo;

    /**
     * 读取发生错误的行号(不包括表头)
     */
    private List<Integer> errRows;
}
