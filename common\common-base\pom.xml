<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.knx</groupId>
        <artifactId>knx-survey-service</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>./../../pom.xml</relativePath>
    </parent>

    <artifactId>common-base</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>

    <properties>
        <java.version>1.8</java.version>
        <lombok.version>1.18.12</lombok.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.knx</groupId>
            <artifactId>knx-base</artifactId>
            <version>${center.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.knx</groupId>
            <artifactId>knx-shiro</artifactId>
            <version>${center.version}</version>
        </dependency>
        <dependency>
            <groupId>com.knx</groupId>
            <artifactId>survey-model</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.6.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-core</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.knx</groupId>
            <artifactId>knx-base-context-supporter</artifactId>
            <version>${center.version}</version>
        </dependency>
    </dependencies>
</project>