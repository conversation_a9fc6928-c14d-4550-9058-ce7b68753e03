package com.knx.bean.model.dto.client.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * @Author: Erin
 * @Date: 2021/1/26 14:59
 * @Description:系统用户类型
 */
@ToString(callSuper = true)
@Getter
public enum SysUserTypeEnum {

    DOMESTIC_CONSUMER("普通用户"),

    BUILT_IN_USER("内置用户"),

    OPEN_API_USER("开发api用户");

    private String name;

    SysUserTypeEnum(String name) {
        this.name = name;
    }


}
