package com.knx.feign.bean;


import com.knx.common.base.service.ServiceNameConstants;
import com.knx.common.base.web.response.IDValue;
import com.knx.common.base.web.response.WebResponse;
import com.knx.common.security.FeignFilter;
import com.knx.survey.client.bean.model.KnxBeanAccount;
import com.knx.survey.client.bean.model.KnxBeanTransaction;
import com.knx.survey.client.bean.model.KnxCard;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 * @Package com.knx.feign.uas
 * @date 2020/6/24 17:34
 */

@FeignClient(contextId = "knxBeanService", value = ServiceNameConstants.KNX_BEAN_SERVICE, configuration = FeignFilter.class)
public interface RemoteBeanService {

    @RequestMapping(value = "/knx/bean/account/create", method = {RequestMethod.POST})
    WebResponse<IDValue> knxBeanAccountCreate(@RequestBody KnxBeanAccount knxBeanAccount);


    @PostMapping("/knx/bean/account/defaultGiveKen")
    WebResponse knxBeanAccountDefaultGiveKen(@RequestParam("tenantId") String tenantId, @RequestParam("balance") Integer balance);

    @PostMapping("/knx/bean/account/batchListByTenantId")
    WebResponse<List<KnxBeanAccount>> knxBeanAccountbatchListByTenantId(@RequestBody List<String> tenantIds);

    @GetMapping("/knx/bean/account/get/{tenantId}")
    WebResponse<KnxBeanAccount> get(@PathVariable("tenantId") String tenantId);

    @PostMapping("/knx/bean/transaction/spend")
    WebResponse<Boolean> spend(@RequestBody KnxBeanTransaction knxBeanTransaction);

    @PostMapping("/knx/bean/transaction/recharge")
    WebResponse<IDValue> recharge(@RequestBody KnxBeanTransaction knxBeanTransaction);

    @PostMapping("/knx/bean/card/updateBatch")
    WebResponse<Integer> updateBatch(@RequestBody List<KnxCard> cards);

    @PostMapping("/knx/bean/card/updateRechargeBatch")
    WebResponse<Integer> updateRechargeBatch(@RequestBody List<KnxCard> cards);


    @PostMapping("/knx/bean/card/batchCheckoutCardPassword")
    WebResponse<Boolean> batchCheckoutCardPassword(@RequestBody List<String> passwords);

    @PostMapping("/knx/bean/card/listByCardPasswords")
    WebResponse<List<KnxCard>> listByCardPasswords(@RequestBody List<String> cardPasswords);

    @PostMapping("/knx/bean/card/batchCheckoutCardPasswordIsRecharge")
    WebResponse<Boolean> batchCheckoutCardPasswordIsRecharge(@RequestBody List<String> passwords);

    @GetMapping("/knx/bean/account/getBalance/{tenantId}")
    WebResponse<BigDecimal> getBalance(@PathVariable("tenantId") String tenantId);

    @PostMapping("/knx/bean/transaction/messageSpend")
    WebResponse<Boolean> messageSpend(@RequestBody KnxBeanTransaction knxBeanTransaction);

    @GetMapping("/knx/bean/account/getMessageBalance/{tenantId}")
    WebResponse<BigDecimal> getMessageBalance(@PathVariable("tenantId") String tenantId);

}
