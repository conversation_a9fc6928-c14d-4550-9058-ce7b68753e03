package com.knx.survey.common.util;

import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

public class LanguageUtil {
    // 定义语言优先级映射
    public static final int DEFAULT_LANGUAGE_PRIORITY = 10;
    public static final Map<String, Integer> LANGUAGE_PRIORITY;
    static {
        Map<String, Integer> tempMap = new HashMap<>();
        tempMap.put("zh_CN", 0);
        tempMap.put("en_US", 1);
        tempMap.put("jp", 2);
        tempMap.put("ko", 3);
        tempMap.put("cs_1", 4);
        tempMap.put("cs_2", 5);
        tempMap.put("cs_3", 6);
        LANGUAGE_PRIORITY = Collections.unmodifiableMap(tempMap);
    }

    public static List<String> sorted(List<String> langs) {
        langs = Optional.ofNullable(langs)
                .orElseGet(Lists::newArrayList) // 处理null情况
                .stream()
                .sorted(Comparator
                        .comparing((String lang) -> !lang.equals("zh_CN")) // zh_CN排第一
                        .thenComparing(lang -> !lang.equals("en_US"))      // en_US排第二
                        .thenComparing(lang -> !lang.equals("jp"))
                        .thenComparing(lang -> !lang.equals("ko"))
                        .thenComparing(lang -> !lang.equals("cs_1"))
                        .thenComparing(lang -> !lang.equals("cs_2"))
                        .thenComparing(lang -> !lang.equals("cs_3"))
                        .thenComparing(Comparator.naturalOrder())          // 其他按字母顺序
                )
                .map(a -> a)
                .collect(Collectors.toList());
        return langs;
    }
}
