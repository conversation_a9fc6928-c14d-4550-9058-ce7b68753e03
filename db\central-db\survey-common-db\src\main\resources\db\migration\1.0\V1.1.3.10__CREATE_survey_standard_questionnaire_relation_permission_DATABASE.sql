DROP TABLE IF EXISTS "public"."survey_standard_questionnaire_relation_permission";
CREATE TABLE survey_standard_questionnaire_relation_permission (
	id varchar(36) NOT NULL,
	standard_questionnaire_id varchar(36) NOT NULL,
	type varchar(50) NULL,
	create_by varchar(36) NULL,
	create_time timestamp(3) NULL DEFAULT now(),
	update_time timestamp(3) NULL DEFAULT now(),
	last_update_by varchar(36) NULL,
	is_deleted bool NULL DEFAULT false,
	app_id varchar(36) null,
	CONSTRAINT "___pk__survey_standard_questionnaire_relation_permission___" PRIMARY KEY (id)
);
COMMENT ON COLUMN "public"."survey_standard_questionnaire_relation_permission"."standard_questionnaire_id" IS '标准问卷id';
COMMENT ON COLUMN "public"."survey_standard_questionnaire_relation_permission"."type" IS '权限类型';
COMMENT ON TABLE  "public"."survey_standard_questionnaire_relation_permission" IS '标准问卷关联权限表';
