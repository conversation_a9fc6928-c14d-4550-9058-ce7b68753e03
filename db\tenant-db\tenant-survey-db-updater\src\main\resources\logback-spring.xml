<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <springProperty scope="context" name="APP_NAME" source="spring.application.name"/>
    <property name="APP_NAME" value="tenant-survey-db-updater"/>

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <!-- 设置全局变量 定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径  -->
    <property name="LOG_HOME" value="logs"/>
    <!--日志文件保留天数 表示只保留最近10天的日志，以防止日志填满整个磁盘空间。同理，可以使用%d{yyyy-MM-dd_HH-mm}来定义精确到分的日志切分方式-->
    <property name="LOG_MAX_HISTORY" value="90"/>
    <!--用来指定日志文件的上限大小，例如设置为2GB的话，那么到了这个值，就会删除旧的日志。-->
    <property name="LOG_TOTAL_SIZE_CAPACITY" value="2GB"/>
    <!--用来指定日志控制台输出格式-->
    <property name="LOG_ENCODER_FORMAT"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] &quot;%-5level&quot; %logger{50} - %msg%n"/>
    <!--用来指定日志打印到日志文件输出格式-->
    <property name="LOG_ENCODER_FORMAT_JSON"
              value=" {&quot;app_name&quot;:&quot;${APP_NAME}&quot;,&quot;severity&quot;:&quot;%level&quot;,&quot;service&quot;: &quot;%contextName&quot;,&quot;pid&quot;: &quot;${PID:-}&quot;,&quot;thread&quot;: &quot;%thread&quot;,&quot;class&quot;: &quot;%logger{40}&quot;,&quot;rest&quot;: &quot;%message->%ex{full}&quot;}"/>

    <property name="LOG_ENCODER_FORMAT_JSON_SKY"
              value="%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} [%X{tid}] %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

    <!--打印控制台日志文件格式-->
    <appender name="consoleLog" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${LOG_ENCODER_FORMAT_JSON_SKY}</pattern>
            </layout>
        </encoder>
    </appender>

    <!--info 级别日志打印-->
    <appender name="fileInfoLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
        </filter>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>UTC</timeZone>
                </timestamp>
                <pattern>
                    <pattern>${LOG_ENCODER_FORMAT_JSON}</pattern>
                </pattern>
            </providers>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}_info.%d.log</fileNamePattern>
            <!--日志文件保留天数 表示只保留最近10天的日志，以防止日志填满整个磁盘空间。同理，可以使用%d{yyyy-MM-dd_HH-mm}来定义精确到分的日志切分方式-->
            <MaxHistory>${LOG_MAX_HISTORY}</MaxHistory>
        </rollingPolicy>
    </appender>

    <!--error 级别日志打印-->
    <appender name="fileErrorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>UTC</timeZone>
                </timestamp>
                <pattern>
                    <pattern>${LOG_ENCODER_FORMAT_JSON}</pattern>
                </pattern>
            </providers>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}_error.%d.log</fileNamePattern>
            <!--日志文件保留天数 表示只保留最近10天的日志，以防止日志填满整个磁盘空间。同理，可以使用%d{yyyy-MM-dd_HH-mm}来定义精确到分的日志切分方式-->
            <MaxHistory>${LOG_MAX_HISTORY}</MaxHistory>
        </rollingPolicy>
    </appender>

    <root level="info">
        <appender-ref ref="consoleLog"/>
        <appender-ref ref="fileErrorLog"/>
    </root>
</configuration>

