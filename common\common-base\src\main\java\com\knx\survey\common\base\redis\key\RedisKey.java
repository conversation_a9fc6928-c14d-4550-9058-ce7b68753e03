package com.knx.survey.common.base.redis.key;


import lombok.Data;

@Data
public class RedisKey {

    public static String LOTTERY_LOCK_KEY = "sag:SurveyLotteryService:doLottery:req:";

    public static String LOTTERY_CACHE_KEY = "sag:lottery:";

    public static String REPORT_MAIL_KEY  = "sag:ProjectService:sendReportMessageByFiles:";

    public static String REPORT_REPORT_MAIL_KEY  = "sag:SagReportEmailContentService:dealMail:";

    public static String TIP_OUT_REPORT_KEY  = "sag:ReportService:createTipReport:";

    public static String PROJECT_ORGANIZATION_KEY = "sag:surveyOrganizationService:getOrganization:";

    public static String PROJECT_PARENT_ORGANIZATION_KEY = "sag:surveyOrganizationService:getParentOrganization:";

    public static String PROJECT_PERSON_ORGANIZATION_MAPPING_KEY = "sag:surveyOrganizationService:getPersonOrganizationMapping:";
}
