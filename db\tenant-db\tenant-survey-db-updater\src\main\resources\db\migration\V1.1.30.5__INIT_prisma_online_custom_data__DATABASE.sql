DROP TABLE IF EXISTS "public"."prisma_online_custom_data";
CREATE TABLE public.prisma_online_custom_data (
    id varchar(36) NOT NULL,
    prisma_report_data_id varchar(36) DEFAULT NULL,
    group_key varchar(36) DEFAULT NULL,
    is_show_custom_index bool DEFAULT false,
    custom_index_name json DEFAULT NULL,
    coefficient_id varchar(36) DEFAULT NULL,
    is_show_coefficient bool DEFAULT false,
    coefficient_name json DEFAULT NULL,
    create_by varchar(36) DEFAULT NULL,
    create_time timestamp(3) NULL DEFAULT now(),
    update_time timestamp(3) NULL DEFAULT now(),
    last_update_by varchar(36) NULL,
    is_deleted bool NULL DEFAULT false,
    app_id varchar(36) NULL,
    CONSTRAINT "___pk___prisma_online_custom_data___" PRIMARY KEY (id)
);
COMMENT ON TABLE  "public"."prisma_online_custom_data" IS '调研在线报告数据表';
COMMENT ON COLUMN "public"."prisma_online_custom_data"."group_key" IS '自定义指数key';
COMMENT ON COLUMN "public"."prisma_online_custom_data"."is_show_custom_index" IS '是否显示这个自定义指数';
COMMENT ON COLUMN "public"."prisma_online_custom_data"."coefficient_id" IS '自定义相关系数key';
COMMENT ON COLUMN "public"."prisma_online_custom_data"."is_show_custom_index" IS '是否显示这个自定义指数';