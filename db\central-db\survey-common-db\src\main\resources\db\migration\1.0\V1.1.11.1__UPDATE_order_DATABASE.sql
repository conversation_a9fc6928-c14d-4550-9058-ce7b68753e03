UPDATE "public"."order" set contract_file =  null where contract_file = '';
UPDATE "public"."order" set confirm_file =  null where confirm_file = '';
UPDATE "public"."order" set finance_confirm_file =  null where finance_confirm_file = '';

UPDATE "public"."order" set contract_file =  CONCAT('["',contract_file,'"]') where contract_file > '';
UPDATE "public"."order" set confirm_file =  CONCAT('["',confirm_file,'"]') where confirm_file > '';
UPDATE "public"."order" set finance_confirm_file =  CONCAT('["',finance_confirm_file,'"]') where finance_confirm_file > '';

alter table "public"."order"  alter column contract_file type json USING contract_file::json;
alter table "public"."order"  alter column confirm_file type json USING confirm_file::json;
alter table "public"."order"  alter column finance_confirm_file type json USING finance_confirm_file::json;

UPDATE "public"."order_history" set contract_file =  null where contract_file = '';
UPDATE "public"."order_history" set confirm_file =  null where confirm_file = '';
UPDATE "public"."order_history" set finance_confirm_file =  null where finance_confirm_file = '';

UPDATE "public"."order_history" set contract_file =  CONCAT('["',contract_file,'"]') where contract_file > '';
UPDATE "public"."order_history" set confirm_file =  CONCAT('["',confirm_file,'"]') where confirm_file > '';
UPDATE "public"."order_history" set finance_confirm_file =  CONCAT('["',finance_confirm_file,'"]') where finance_confirm_file > '';

alter table "public"."order_history"  alter column contract_file type json USING contract_file::json;
alter table "public"."order_history"  alter column confirm_file type json USING confirm_file::json;
alter table "public"."order_history"  alter column finance_confirm_file type json USING finance_confirm_file::json;

UPDATE "order_item" set employee_level = null  WHERE employee_level::json->>0 ='' and employee_level::json->>1 is null;
UPDATE "order_item_history" set employee_level = null  WHERE employee_level::json->>0 ='' and employee_level::json->>1 is null;
