package com.knx.survey.common.base.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.knx.survey.common.util.IDUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class OperatorLogModel implements Serializable {
    private static final long serialVersionUID = -7847270097936488649L;

    private String id = IDUtil.getSimpleUUID();

    /**
     * 用户TOKEN
     */
    private String token;
    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    /**
     * 结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    /**
     * 消耗时间
     */
    private Long spendTime;

    /**
     * 请求参数
     */
    private String parameter;
    /**
     * 返回参数
     */
    private String result;

    /**
     * 请求方式
     */
    private String httpMethod;

    /**
     * 报错信息
     */
    private String exception;

    private String remoteAddr;
    private String classMethod;
    private String requestUri;
    private String uri;
    private String sessionId;
    private String browser;
    private String type;

}
