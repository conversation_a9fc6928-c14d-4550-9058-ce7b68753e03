package com.knx.bean.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.knx.bean.dao.mapper.KnxBeanAccountMapper;
import com.knx.bean.model.entity.KnxBeanAccount;
import com.knx.bean.model.entity.KnxBeanTransaction;
import com.knx.bean.model.enums.KnxBeanAccountStatusEnum;
import com.knx.bean.model.enums.KnxBeanTransactionStatusEnum;
import com.knx.bean.model.enums.KnxBeanTransactionTypeEnum;
import com.knx.bean.service.api.IKnxBeanAccountService;
import com.knx.bean.service.util.KnxBeanUtils;
import com.knx.common.base.model.BaseTenantModel;
import com.knx.common.security.DataSecurityUtil;
import com.knx.survey.common.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;


@Service("knxBeanAccountService")
@Slf4j
public class KnxBeanAccountService extends BaseServiceImpl<KnxBeanAccountMapper, KnxBeanAccount> implements IKnxBeanAccountService {

    @Resource
    KnxBeanAccountMapper knxBeanAccountMapper;

    @Autowired
    private KnxBeanUtils knxBeanUtils;

    @Autowired
    private KnxBeanTransactionService knxBeanTransactionService;


    @Override
    public KnxBeanAccount get(String tenantId) {
        QueryWrapper<KnxBeanAccount> queryWrapper = new QueryWrapper<>();
        String tenantIdStr = BaseTenantModel.Fields.tenantId;
        queryWrapper.eq(StringUtils.camelToUnderline(tenantIdStr), tenantId);
        KnxBeanAccount knxBeanAccount = knxBeanAccountMapper.selectOne(queryWrapper);
        DataSecurityUtil.validateTenantAction(knxBeanAccount);
        return knxBeanAccount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void defaultGiveKen(String tenantId, Integer balance) {
        // 插入肯豆流水记录记录表
        BigDecimal amount = new BigDecimal(balance);
        KnxBeanTransaction knxBeanTransaction = new KnxBeanTransaction();
        knxBeanTransaction.setTenantId(tenantId);
        knxBeanTransaction.setAmount(amount);
        knxBeanTransaction.setBalance(amount);
        knxBeanTransaction.setStatus(KnxBeanTransactionStatusEnum.USED);
        knxBeanTransaction.setOrderNo(knxBeanUtils.generateReqNo());
        knxBeanTransaction.setContractAmount(new BigDecimal(0));
        knxBeanTransaction.setType(KnxBeanTransactionTypeEnum.GIVE_KEN);
        knxBeanTransactionService.save(knxBeanTransaction);

        // 插入肯豆记录表
        KnxBeanAccount knxBeanAccount = new KnxBeanAccount();
        knxBeanAccount.setTenantId(tenantId);
        knxBeanAccount.setBalance(amount);
        knxBeanAccount.setStatus(KnxBeanAccountStatusEnum.ENABLED);
        super.save(knxBeanAccount);
    }

    @Override
    public List<KnxBeanAccount> batchListByTenantId(List<String> tenantIds) {
        if (tenantIds == null || tenantIds.isEmpty()) {
            return Collections.emptyList();
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.in(StringUtils.camelToUnderline(BaseTenantModel.Fields.tenantId), tenantIds);
        return list(queryWrapper);
    }


    @Override
    public BigDecimal getBalance(String tenantId) {
        List<KnxBeanAccount> list = lambdaQuery().eq(KnxBeanAccount::getTenantId, tenantId).list();
        if (list.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return list.get(0).getBalance();
    }

    @Override
    public BigDecimal getMessageBalance(String tenantId) {
        List<KnxBeanAccount> list = lambdaQuery().eq(KnxBeanAccount::getTenantId, tenantId).list();
        if (list.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return list.get(0).getMessageBalance();
    }
}