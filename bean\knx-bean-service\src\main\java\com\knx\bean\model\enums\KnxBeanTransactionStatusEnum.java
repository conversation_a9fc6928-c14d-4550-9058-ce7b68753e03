package com.knx.bean.model.enums;

import lombok.ToString;
import lombok.experimental.FieldNameConstants;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/6/8 15:47
 */
@FieldNameConstants
@ToString(callSuper = true)
public enum KnxBeanTransactionStatusEnum {

    CANCELED("已取消"),

    USED("已执行");

    private String name;

    KnxBeanTransactionStatusEnum(String name) {
        this.name = name;
    }

}
