DROP TABLE IF EXISTS survey_user_action;
CREATE TABLE survey_user_action
(
    "id"                              VARCHAR(36)  NOT NULL,
    "type"                            VARCHAR(50)  NOT NULL,
    "create_by"                       VARCHAR(36),
    "create_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                     TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                  VARCHAR(36),
    "is_deleted"                      BOOL         DEFAULT false,
    "app_id"                          VARCHAR(36),
    CONSTRAINT "___pk___survey_user_action___" PRIMARY KEY ("id")
);

COMMENT ON TABLE  "public"."survey_user_action" IS '用户操作表';
COMMENT ON COLUMN "public"."survey_user_action"."type" IS '操作类型';
