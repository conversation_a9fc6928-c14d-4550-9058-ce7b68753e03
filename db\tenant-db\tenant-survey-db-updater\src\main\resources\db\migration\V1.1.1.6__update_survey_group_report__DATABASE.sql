DROP TABLE IF EXISTS sag_group_report;
CREATE TABLE sag_group_report
(
    "id"                                 VARCHAR(36) NOT NULL,
    "report_style"                         VARCHAR(50) NOT NULL,
	"project_id"                         VARCHAR(36),
	"details"                            JSON,
	"status"                             VARCHAR(50),
	"file_id"                            VARCHAR(100),
    "remark"                             VARCHAR(100),
    "create_by"                          VARCHAR(36),
    "create_time"                        TIMESTAMP(3) DEFAULT NOW(),
    "update_time"                        TIMESTAMP(3) DEFAULT NOW(),
    "last_update_by"                     VARCHAR(36),
    "is_deleted"                         BOOL         DEFAULT false,
    "app_id"                             VARCHAR(36),
    CONSTRAINT "___pk___survey_group_report___" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."sag_group_report" IS '团队报告记录';
COMMENT ON COLUMN "public"."sag_group_report"."report_style" IS '团队报告类型';
COMMENT ON COLUMN "public"."sag_group_report"."project_id" IS '活动id';
COMMENT ON COLUMN "public"."sag_group_report"."details" IS '人员列表或调查者列表';
COMMENT ON COLUMN "public"."sag_group_report"."status" IS '报告状态';
COMMENT ON COLUMN "public"."sag_group_report"."file_id" IS '报告文件id';
COMMENT ON COLUMN "public"."sag_group_report"."remark" IS '报告备注';


alter table "public"."sag_report_file" add group_id VARCHAR(36);
COMMENT ON COLUMN "public"."sag_report_file"."group_id" IS '团队报告id';