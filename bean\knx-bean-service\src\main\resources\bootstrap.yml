spring:
  application:
    name: knx-bean-service

  cloud:
    nacos:
      discovery:
        namespace: knx-business-platform
        server-addr: knx-nacos-discover:8848
      config:
        group: SAG_GROUP
        namespace: knx-business-platform
        server-addr: knx-nacos-discover:8848
        file-extension: yml
        shared-configs:
          - data-id: application-default.yml
            group: DEFAULT_GROUP
          - data-id: common-mq.yml
            group: DEFAULT_GROUP
server:
  port: 8002